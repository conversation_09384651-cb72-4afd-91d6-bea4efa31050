import typescript from "rollup-plugin-typescript2";
import babel from "@rollup/plugin-babel";
import { preserveShebangs } from "rollup-plugin-preserve-shebangs";
import pkg from "./package.json";
import commonjs from "@rollup/plugin-commonjs";
import resolve from "@rollup/plugin-node-resolve";
import { terser } from "rollup-plugin-terser";

const external = [
    ...Object.keys(pkg.dependencies || {}),
    ...Object.keys(pkg.peerDependencies || {})
].map((name) => new RegExp(`^${name}`));

const isProduction = process.env.NODE_ENV === "production";

export default {
    input   : "src/index.ts",
    output  : {file: "bin/ttwp.js", format: "cjs", indent: false},
    external: external,
    plugins : [
        resolve({
            preferBuiltins: true
        }),
        typescript({clean: true}),
        babel({
            extensions  : [".ts"],
            plugins     : ["@babel/plugin-transform-runtime"],
            babelHelpers: "runtime"
        }),
        commonjs(),
        preserveShebangs(),
        isProduction && terser()
    ].filter(Bo<PERSON>an)
};
