{"compilerOptions": {"target": "esnext", "module": "esnext", "lib": ["dom", "es2017"], "jsx": "react-jsx", "declaration": false, "sourceMap": true, "removeComments": false, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "moduleResolution": "node", "noImplicitAny": false, "baseUrl": "./", "esModuleInterop": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true}, "include": ["./src/**/*"]}