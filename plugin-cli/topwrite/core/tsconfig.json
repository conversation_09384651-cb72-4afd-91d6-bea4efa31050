{"compilerOptions": {"target": "esnext", "module": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "jsx": "react-jsx", "sourceMap": true, "removeComments": false, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitAny": false, "moduleResolution": "node", "baseUrl": "./", "esModuleInterop": true, "experimentalDecorators": true, "skipLibCheck": true, "strictNullChecks": true, "resolveJsonModule": true, "paths": {"@@/*": ["./*"], "@/*": ["../src/*"]}}, "include": ["./**/*", "../src/**/*", "../hooks.ts"]}