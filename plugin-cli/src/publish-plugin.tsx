import npmVersion from 'libnpmversion';
import pack from 'libnpmpack';
import { publish } from 'libnpmpublish';
import pacote from 'pacote';
import type { PackageJson } from '@npm/types';
import { getConfig } from './config';
import semver from 'semver';

export default async function publishPlugin() {

    const { registry, token } = getConfig();

    const opt = {
        registry: `${registry}/api/topwrite/registry`,
        forceAuth: {
            token: token
        }
    };

    let manifest = await pacote.manifest('file:.', opt) as PackageJson;
    try {
        const remote = await pacote.manifest(manifest.name, opt) as PackageJson;

        if (semver.lte(manifest.version, remote.version)) {
            const newVersion = semver.inc(remote.version, 'patch', { loose: true });

            await npmVersion(newVersion);

            manifest = await pacote.manifest('file:.', opt) as PackageJson;
        }
    } catch {

    }

    const tarball = await pack('file:.', {
        foregroundScripts: true
    });

    await publish(manifest, tarball, opt);
}
