#! /usr/bin/env node

import { Command } from 'commander';
import inquirer from 'inquirer';
import buildPlugin from './build-plugin';
import logger from './logger';
import * as path from 'path';
import createPlugin, { PluginMeta } from './create-plugin';
import { getConfig, setConfig } from './config';
import publishPlugin from './publish-plugin';

const pkg = require('../package.json');
const resolve = (input: string) => path.resolve(process.cwd(), input);

const program = new Command();

program.version(pkg.version, '-v, --version');

program.command('serve')
       .description('serve a plugin')
       .action(async () => {
           try {
               await buildPlugin(true, true);
           } catch (err: any) {
               console.log(err);
               logger.error('Error: ', err.message);
           }
       });

program.command('build')
       .option('-d, --dev', 'dev')
       .description('build a plugin')
       .action(async function(options) {
           try {
               await buildPlugin(options.dev);
           } catch (err: any) {
               if (err) {
                   logger.error('Error: ', err);
               }
               process.exit(1);
           }
       });

program.command('publish')
       .description('publish a plugin')
       .action(async function() {
           try {
               await buildPlugin();

               await publishPlugin();
               logger.info('Plugin publish successfully');
           } catch (err: any) {
               if (err) {
                   logger.error('Error: ', err);
               }
               process.exit(1);
           }
       });

program.command('create [dir]')
       .description('create a new plugin')
       .action(async function(dir) {
           const plugin = await inquirer.prompt<PluginMeta>([
               {
                   type: 'list',
                   name: 'type',
                   message: 'What type of plugin do you want to create?',
                   choices: ['theme', 'extension']
               },
               {
                   name: 'displayName',
                   message: 'What\'s the name of your plugin?',
               },
               {
                   name: 'name',
                   message: 'What\'s the identifier of your plugin?',
               },
               {
                   name: 'description',
                   message: 'What\'s the description of your plugin?',
               },
           ]);

           try {
               dir = resolve(dir || plugin.name);

               createPlugin(dir, plugin);
               logger.info(`Plugin created successfully in "${dir}"`);
           } catch (err: any) {
               logger.error(err.message);
           }
       });

program.command('login')
       .description('login topthink developer center')
       .action(async () => {
           const { token } = await inquirer.prompt<{ token: string }>([
               {
                   name: 'token',
                   message: 'Enter your access token:',
                   validate: (token) => {
                       return token.length > 0;
                   }
               },
           ]);

           setConfig('token', token);

           logger.info('Login successfully');
       });

const configProgram = program.command('config');

configProgram.command('list')
             .action(function() {
                 console.log(getConfig());
             });

configProgram.command('set <key> <value>')
             .action(function(key, value) {
                 setConfig(key, value);
             });

program.parse(process.argv);

// Display help if no arguments
if (!program.args.length) program.help();
