import fs from 'fs-extra';
import path from 'path';

const TEMPLATE_DIR = path.resolve(__dirname, '../template');

export interface PluginMeta {
    type: 'theme' | 'extension';
    name: string;
    displayName: string;
    description: string;
}

export default function createPlugin(dir: string, plugin: PluginMeta) {
    const pkg = {
        'name': `topwrite-plugin-${plugin.name}`,
        'displayName': `${plugin.displayName}`,
        'description': `${plugin.description}`,
        'version': '0.0.1',
        'license': 'ISC',
        'main': 'dist/index.js',
        'icon': 'images/icon.png',
        'scripts': {
            'serve': 'ttwp serve',
            'build:dev': 'ttwp build --dev',
            'build': 'ttwp build',
            'publish': 'ttwp publish'
        },
        'devDependencies': {
            '@types/react': '^18',
            '@types/react-dom': '^18',
            'topwrite': '^1.0.0',
        }
    };

    fs.copySync(TEMPLATE_DIR, dir, {
        overwrite: true
    });

    fs.outputJsonSync(path.resolve(dir, 'package.json'), pkg, {
        spaces: 2
    });

    fs.outputFileSync(path.resolve(dir, 'README.md'), `# ${plugin.displayName}`);
}
