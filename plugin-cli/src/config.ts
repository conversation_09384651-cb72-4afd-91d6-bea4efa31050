import Configstore from 'configstore';

const configstore = new Configstore('ttwp', {
    host: 'https://sandbox.x.topthink.com',
    registry: 'https://developer.topthink.com',
    tag: 'latest',
});

interface ConfigType {
    host: string;
    registry: string;
    tag: string;
    sandbox: string;
    token: string;
}

export function getConfig<Key extends keyof ConfigType>(key: Key): ConfigType[Key]
export function getConfig(): ConfigType
export function getConfig<Key extends keyof ConfigType>(key?: Key): ConfigType[Key] | ConfigType {
    if (key) {
        return configstore.get(key);
    } else {
        return configstore.all;
    }
}

export function setConfig<Key extends keyof ConfigType>(key: Key, value: ConfigType[Key]) {
    configstore.set(key, value);
}
