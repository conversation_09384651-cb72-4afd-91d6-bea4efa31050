import { Compiler } from 'webpack';

export default function runCompiler(compiler: Compiler) {

    return new Promise((resolve, reject) => {

        compiler.run((err, stats) => {
            if (stats) {
                console.log(stats.toString({
                    chunks: true,  // Makes the build much quieter
                    colors: true    // Shows colors in the console
                }));
            }

            if (err || stats?.hasErrors()) {
                reject(err);
            } else {
                resolve(stats);
            }
        });
    });
}
