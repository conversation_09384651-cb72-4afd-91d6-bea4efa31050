import webpack, { Configuration } from 'webpack';
import path from 'path';
import WebpackDevServer, { getFreePort } from 'webpack-dev-server';
import fs from 'fs-extra';
import { compileFromFile } from 'json-schema-to-typescript';
import WebpackConfigPlugin from '@topthink/webpack-config-plugin';
import runCompiler from './run-compiler';
import HtmlWebpackPlugin from 'html-webpack-plugin';
import { nanoid } from 'nanoid';
import { getConfig, setConfig } from './config';
import { merge } from 'webpack-merge';
import pacote from 'pacote';

const resolve = (input: string) => path.resolve(process.cwd(), input);

const getPackageVersion = async (name: string) => {

    const manifest = await pacote.manifest(name, {
        registry: 'https://registry.npmmirror.com/'
    });

    return manifest.version;
};

export default async function buildPlugin(dev: boolean = false, serve: boolean = false) {

    const packageConfig = require(resolve('package.json'));

    const pluginName = packageConfig.name.replace(/^topwrite-plugin-/, '');

    await fs.emptydir(resolve('.topwrite'));
    await fs.copy(path.resolve(__dirname, '../topwrite/core'), resolve('.topwrite'));

    //构建config schema
    let supportConfig = false;
    const schemaFile = resolve('config.json');
    if (fs.existsSync(schemaFile)) {
        supportConfig = true;
        await fs.copy(path.resolve(__dirname, '../topwrite/config'), resolve('.topwrite'));
        const compileSchema = async () => {
            try {
                const ts = await compileFromFile(schemaFile, {
                    bannerComment: '/* tslint:disable */\n' +
                        '/**\n' +
                        '* This file was automatically generated by TowWrite Plugin.\n' +
                        '* DO NOT MODIFY IT BY HAND. \n' +
                        '*/\n',
                    unknownAny: false
                });
                fs.writeFileSync(resolve('.topwrite/config.d.ts'), ts);
            } catch {

            }
        };

        await compileSchema();

        if (serve) {
            fs.watch(schemaFile, (eventType) => {
                if (eventType === 'change') {
                    compileSchema();
                }
            });
        }
    }

    const supportLang = fs.existsSync(resolve('src/lang/'));

    let id = getConfig('sandbox');

    if (!id) {
        id = nanoid();
        setConfig('sandbox', id);
    }

    const host = getConfig('host');
    const tag = getConfig('tag');

    const editorVersion = await getPackageVersion(`@topwrite/editor@${tag}`);
    const readerVersion = await getPackageVersion(`@topwrite/reader@${tag}`);

    const definitions: Record<string, any> = {
        PLUGIN_NAME: JSON.stringify(pluginName),
        PLUGIN_META: undefined,
        SUPPORT_CONFIG: supportConfig,
        SUPPORT_LANG: supportLang
    };

    const plugins: Configuration['plugins'] = [
        new WebpackConfigPlugin({
            serve,
            react: {
                library: `TopWritePlugins.${pluginName}`
            },
            html: false
        }),
        new webpack.DefinePlugin(definitions),
        new webpack.ProvidePlugin({
            process: require.resolve('process/browser')
        })
    ];

    const type = pluginName.startsWith('theme-') ? 'theme' : 'extension';

    const pluginMeta = {
        type,
        host: '',
        name: pluginName,
        display_name: packageConfig.displayName,
        description: packageConfig.description,
        version: packageConfig.version,
        builtin: packageConfig.builtin,
        main: packageConfig.main,
        icon: packageConfig.icon,
        price: packageConfig.price,
        disabled: false,
        installs_count: 0,
        dev: true
    };

    if (serve) {
        definitions['PLUGIN_META'] = JSON.stringify(pluginMeta);

        plugins.push(
            new HtmlWebpackPlugin({
                filename: 'index.html',
                favicon: path.resolve(__dirname, '../public/favicon.ico'),
                template: path.resolve(__dirname, '../public/editor.ejs'),
                inject: false,
                scriptLoading: 'blocking',
                publicPath: '/',
                templateParameters: {
                    id: `sandbox:${id}`,
                    scripts: [
                        'https://jsdelivr.topthink.com/npm/lodash@4.17/lodash.min.js',
                        'https://jsdelivr.topthink.com/npm/react@18/umd/react.development.js',
                        'https://jsdelivr.topthink.com/npm/react-dom@18/umd/react-dom.development.js',
                        'https://jsdelivr.topthink.com/npm/socket.io-client@4/dist/socket.io.min.js',
                        `https://jsdelivr.topthink.com/npm/@topwrite/editor@${editorVersion}/dist/index.js`,
                    ]
                }
            }),
            new HtmlWebpackPlugin({
                filename: 'preview/index.html',
                favicon: path.resolve(__dirname, '../public/favicon.ico'),
                template: path.resolve(__dirname, '../public/reader.ejs'),
                inject: false,
                scriptLoading: 'blocking',
                publicPath: '/',
                templateParameters: {
                    scripts: [
                        'https://jsdelivr.topthink.com/npm/lodash@4.17/lodash.min.js',
                        'https://jsdelivr.topthink.com/npm/react@18/umd/react.development.js',
                        'https://jsdelivr.topthink.com/npm/react-dom@18/umd/react-dom.development.js',
                        `https://jsdelivr.topthink.com/npm/@topwrite/reader@${readerVersion}/dist/index.js`
                    ]
                }
            }),
        );
    }

    const webpackConfig: Configuration = {
        mode: dev ? 'development' : 'production',
        devtool: dev ? 'cheap-module-source-map' : 'source-map',
        entry: resolve('src/index'),
        output: {
            path: resolve('dist'),
            filename: 'index.js',
            chunkFilename: '[id]-[chunkhash:6].js',
            library: {
                name: ['TopWritePlugins', pluginName],
                type: 'window',
                export: 'default',
            },
            clean: true,
        },
        plugins,
        resolve: {
            fallback: {
                'path': require.resolve('path-browserify')
            }
        },
        externals: {
            'topwrite': 'TopWrite',
            'react': 'React',
            'react-dom': 'ReactDOM',
            'react-dom/client': 'ReactDOM',
        }
    };

    const webpackConfigFile = resolve('webpack.config.js');

    const userWebpackConfig = fs.existsSync(webpackConfigFile) ? require(webpackConfigFile) : {};

    const compiler = webpack(merge(webpackConfig, userWebpackConfig));

    if (serve) {
        const port = await getFreePort('auto', '');

        const headers = {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PATCH, PUT, DELETE',
            'Access-Control-Allow-Headers': 'Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match, If-Unmodified-Since, X-Requested-With'
        };
        const server = new WebpackDevServer({
            port,
            headers,
            allowedHosts: 'all',
            hot: 'only',
            liveReload: false,
            client: {
                webSocketURL: {
                    hostname: 'localhost'
                }
            },
            historyApiFallback: {
                rewrites: [
                    { from: /^\/preview/, to: '/preview/index.html' },
                ],
            },
            devMiddleware: {
                index: '',
            },
            proxy: [
                {
                    context: (path, req) => {
                        return path.match(/^\/preview/) && req.headers.accept?.indexOf('html') === -1;
                    },
                    target: host,
                    changeOrigin: true,
                    logLevel: 'silent'
                },
                {
                    context: '/socket.io',
                    target: host,
                    changeOrigin: true,
                    ws: true,
                    logLevel: 'silent'
                },
                {
                    context: '/download',
                    target: host,
                    changeOrigin: true,
                    logLevel: 'silent'
                },
                {
                    context: '/asset',
                    target: host,
                    changeOrigin: true,
                    logLevel: 'silent'
                },
                {
                    context: '/import',
                    target: host,
                    changeOrigin: true,
                    logLevel: 'silent'
                }
            ],
            setupMiddlewares: (middlewares, devServer) => {
                devServer.app.get('/preview$', function(_, res) {
                    res.redirect(301, '/preview/');
                });

                devServer.app.get('/meta', function(_, res) {
                    res.set(headers).json({
                        ...pluginMeta,
                        builtin: false,
                        host: `http://localhost:${port}`,
                        main: 'index.js'
                    });
                });

                return middlewares;
            },
            static: path.resolve(process.cwd()),
        }, compiler);

        await server.start();
    } else {
        await runCompiler(compiler);

        //build hooks
        const hooksFile = resolve('src/hooks.ts');
        if (fs.existsSync(hooksFile)) {
            const compiler = webpack({
                mode: dev ? 'development' : 'production',
                devtool: dev ? 'cheap-module-source-map' : 'source-map',
                entry: hooksFile,
                output: {
                    path: resolve('dist'),
                    filename: 'hooks.js',
                    library: {
                        type: 'commonjs2',
                        export: 'default',
                    },
                    chunkFormat: false
                },
                target: false,
                externals: {
                    path: 'commonjs2 path/posix',
                    crypto: 'commonjs2 crypto'
                },
                node: {
                    global: false,
                    __filename: false,
                    __dirname: false,
                },
                plugins: [
                    new WebpackConfigPlugin({
                        serve: false,
                        react: false,
                        html: false
                    }),
                ]
            });

            await runCompiler(compiler);
        }
    }
}
