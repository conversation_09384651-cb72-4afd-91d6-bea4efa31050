import typescript from "rollup-plugin-typescript2";
import babel from "@rollup/plugin-babel";
import pkg from "./package.json";
import resolve from "@rollup/plugin-node-resolve";
import commonjs from "@rollup/plugin-commonjs";
import { terser } from "rollup-plugin-terser";
import { DEFAULT_EXTENSIONS } from "@babel/core";
import image from "@rollup/plugin-image";

const external = [
    ...Object.keys(pkg.dependencies || {}),
    ...Object.keys(pkg.peerDependencies || {})
].map((name) => new RegExp(`^${name}`));

const isProduction = process.env.NODE_ENV === "production";

export default {
    input   : "src/index.ts",
    output  : {file: "lib/index.js", format: "es", indent: false, sourcemap: true},
    external: [
        ...external
    ],
    plugins : [
        image(),
        resolve({preferBuiltins: true}),
        typescript({clean: true, useTsconfigDeclarationDir: true}),
        babel({
            extensions  : [
                ...DEFAULT_EXTENSIONS,
                "ts",
                "tsx"
            ],
            plugins     : ["@babel/plugin-transform-runtime"],
            babelHelpers: "runtime"
        }),
        commonjs(),
        isProduction && terser()
    ].filter(Boolean)
};
