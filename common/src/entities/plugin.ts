import { Models } from '@topthink/redux-model';
import { JSONSchema7 } from 'json-schema';
import { Activate, Register } from './context';
import { ComponentsMap } from '../models/components';
import { PluginMeta } from '@topwrite/core';
import { Localize } from '../lib/use-intl-context';

export interface PluginConfig {
    name: string,
    meta?: PluginMeta,
    models?: Models,
    components?: Partial<ComponentsMap>,
    register?: Register,
    activate?: Activate,
    localize?: Localize,
    config?: JSONSchema7
}

export default class Plugin {

    readonly name: string;
    readonly config?: JSONSchema7;
    readonly localize?: Localize;

    readonly models: Models;
    readonly components: Partial<ComponentsMap>;
    readonly activate?: Activate;
    readonly register?: Register;

    //仅开发中的插件有此值
    readonly meta?: PluginMeta;

    constructor({ name, meta, models = {}, components = {}, activate, register, localize, config }: PluginConfig) {
        this.name = name;
        this.meta = meta;
        this.models = models;
        this.components = components;
        this.activate = activate;
        this.register = register;
        this.localize = localize;
        this.config = config;
    }

}
