import { createStore, Models, Provider } from '@topthink/redux-model';
import { LocalStorageStateProvider } from '../lib/use-local-storage-state';
import { ComponentDescriptor, ComponentsMap } from '../models/components';
import createComponentDescriptor from '../utils/create-component-descriptor';
import Plugin from './plugin';
import { ElementType, PropsWithChildren } from 'react';
import { ThemeProvider } from '../lib/use-theme-context';
import GlobalStyle from '../components/global-style';
import { Book } from '@topwrite/core';
import * as models from '../models';
import { IntlProvider, Localize } from '../lib/use-intl-context';
import { getEsModuleExport } from '../utils/get-es-module-export';
import InjectedComponent from '../components/injected-component';
import { createStorageSlot } from '../utils/storage';

export type ContextName = 'reader' | 'editor'

export type Activate = (name: ContextName) => undefined | null | JSX.Element | void
export type Register = (name: ContextName, book: Book) => Promise<void>

export default class Context {

    protected name: ContextName;
    protected book: Book;
    protected models: Models = { ...models };
    protected components: ComponentDescriptor[] = [];
    protected activates: Activate[] = [];
    protected plugins: Plugin[] = [];
    protected initialState: Record<string, any>;

    constructor(
        name: ContextName,
        book: Book,
        initialState: Record<string, any>
    ) {
        this.name = name;
        this.book = book;
        this.initialState = initialState;
    }

    registerComponents(components: Partial<ComponentsMap>, plugin: string) {
        for (let [role, component] of Object.entries(components)) {
            if (component) {
                //插件配置
                if (role === 'plugin:setting') {
                    role = `plugin:setting:${plugin}`;
                }

                this.components.push(createComponentDescriptor(role, component));
            }
        }
    }

    registerModels(models: Models, plugin?: string) {
        for (const [name, model] of Object.entries(models)) {
            if (plugin) {
                this.models[`@${plugin}@${name}`] = model;
            } else {
                this.models[name] = model;
            }
        }
    }

    async registerPlugins(plugins: Plugin[]) {
        this.plugins = plugins;

        for (const plugin of plugins) {
            try {
                //注册组件
                this.registerComponents(plugin.components, plugin.name);
                //注册模型
                this.registerModels(plugin.models, plugin.name);
                //注册hook
                if (plugin.activate) {
                    this.activates.push(plugin.activate);
                }
                if (plugin.register) {
                    await plugin.register(this.name, this.book);
                }
            } catch (e) {
                console.log(e);
            }
        }
    }

    async createApp(localize: Localize, locale: string = navigator.language, theme?: 'light' | 'dark'): Promise<ElementType> {
        locale = this.detectLocale(locale);
        const messages = await this.loadMessages(locale, localize);

        const store = createStore(this.models, {
            book: this.book,
            components: this.components,
            ...this.initialState
        });

        const Activate = ({ children }: PropsWithChildren<{ name: ContextName }>) => {
            return <>{children}</>;
        };

        const ActivateRunner = ({ children }: PropsWithChildren<{}>) => {
            const inner = <InjectedComponent
                role={`activate:${this.name}`}
                props={{ children, name: this.name }}
                component={Activate}
            />;
            return this.activates.reduce((prev, activate) => {
                const result = activate(this.name);
                if (result !== undefined) {
                    return <>{result}</>;
                }

                return prev;
            }, inner);
        };

        return ({ children }) => {
            return <Provider store={store}>
                <LocalStorageStateProvider>
                    <ThemeProvider context={this.name} theme={theme}>
                        <GlobalStyle />
                        <IntlProvider locale={locale} messages={messages}>
                            <ActivateRunner>
                                {children}
                            </ActivateRunner>
                        </IntlProvider>
                    </ThemeProvider>
                </LocalStorageStateProvider>
            </Provider>;
        };
    }

    protected detectLocale(locale: string) {
        const storage = createStorageSlot(`${this.name}.${this.book.id}.lang`);

        locale = storage.get() || locale;

        const params = new URLSearchParams(window.location.search);
        const lang = params.get('lang');

        if (lang) {
            storage.set(lang);
            locale = lang;
        }

        document.documentElement.setAttribute('lang', locale);

        return locale;
    }

    protected async loadMessages(locale: string, localize: Localize) {
        return await this.plugins.reduce(async (accu, plugin) => {
            const previous = getEsModuleExport(await accu);

            if (plugin.localize) {
                return Object.assign({}, previous, getEsModuleExport(await plugin.localize(locale)));
            }
            return previous;
        }, localize(locale));
    }

}
