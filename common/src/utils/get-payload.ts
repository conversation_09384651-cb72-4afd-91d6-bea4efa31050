export default function getPayload(html: Document | string = window.document): any {
    if (typeof html === 'string') {
        const parser = new DOMParser();
        html = parser.parseFromString(html, 'text/html');
    }

    const script = html.querySelector('script[type="application/payload+json"]');
    if (script) {
        try {
            return JSON.parse(script.innerHTML);
        } catch {
            return {};
        }
    }
    return undefined;
}
