import { Handlers, Options as ToHastOptions, toHast, all } from 'mdast-util-to-hast';
import { convert } from 'unist-util-is';
import { Element, Node, Root, Text } from 'hast';
import React, { HTMLProps, ReactElement, ReactNode } from 'react';
import { MdastNodes } from 'mdast-util-to-hast/lib';

const isRoot = convert<Root>('root');
const isElement = convert<Element>('element');
const isText = convert<Text>('text');

type Context = {
    key: number
    prefix?: string
}

type CreateElement = (
    name: string,
    attributes: { [key: string]: any },
    children?: ReactNode | ReactNode[]
) => ReactElement

interface Options {
    extensions?: Handlers[];
    creator?: CreateElement;
}

function squeezeEol(nodes: Node[]) {
    return nodes.filter(node => {
        return !(isText(node) && node.value === '\n');
    });
}

function createElement(creator: CreateElement, node: Element, ctx: Context): ReactElement {
    const properties = node.properties;
    const name = node.tagName;

    const attributes: HTMLProps<any> = { ...properties };

    if (ctx.prefix) {
        ctx.key++;
        attributes.key = ctx.prefix + ctx.key;
    }

    const elements = [];
    const children = squeezeEol(node.children);
    const length = children ? children.length : 0;
    let index = -1;

    while (++index < length) {
        const value: Node = children[index];

        if (isElement(value)) {
            elements.push(createElement(creator, value, ctx));
        } else if (isText(value)) {
            elements.push(value.value);
        }
    }

    return creator.call(node, name, attributes, elements.length > 0 ? elements : undefined);
}

export default function toJsx(tree: MdastNodes, { extensions = [], creator = React.createElement }: Options) {

    const handlers = extensions.reduce((prev, current) => {
        return Object.assign(prev, current);
    }, {});

    const options: ToHastOptions = {
        allowDangerousHtml: false,
        handlers: {
            ...handlers,
            textDirective(h, node) {
                return h({}, 'textDirective', { name: node.name, attributes: node.attributes }, all(h, node));
            },
            leafDirective(h, node) {
                return h({}, 'leafDirective', { name: node.name, attributes: node.attributes }, all(h, node));
            },
            containerDirective(h, node) {
                if (!('children' in node)) {
                    node.children = [];
                }
                let hasLabel = false;
                for (const child of node.children) {
                    if (child.data?.directiveLabel) {
                        hasLabel = true;
                        break;
                    }
                }
                if (!hasLabel) {
                    node.children.unshift({
                        type: 'paragraph',
                        children: [{
                            type: 'text',
                            value: ''
                        }]
                    });
                }

                return h({}, 'containerDirective', { name: node.name, attributes: node.attributes }, all(h, node));
            },
            heading(h, node) {
                //h1~6 => heading
                const result = h(node, 'heading', all(h, node));
                result.properties = {
                    ...result.properties,
                    depth: node.depth
                };
                return result;
            }
        },
        unknownHandler() {
            return null;
        }
    };

    const hastTree = toHast(tree, options);

    if (isRoot(hastTree)) {
        return squeezeEol(hastTree.children).map((node, index) => {
            if (isElement(node)) {
                return createElement(creator, node, { key: 0, prefix: `h-${index}-` });
            } else if (isText(node)) {
                return node.value;
            }
        });
    } else if (!isElement(hastTree)) {
        throw new Error(
            'Expected root or element, not `' + ((hastTree && hastTree.type) || hastTree) + '`'
        );
    }

    return createElement(creator, hastTree, { key: 0, prefix: 'h-', });
}
