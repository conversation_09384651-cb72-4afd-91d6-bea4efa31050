const StorageTypes = ['localStorage', 'sessionStorage', 'none'] as const;

export type StorageType = typeof StorageTypes[number];

const DefaultStorageType: StorageType = 'localStorage';

function getBrowserStorage(
    storageType: StorageType = DefaultStorageType,
): Storage | null {
    if (typeof window === 'undefined') {
        throw new Error(
            'Browser storage is not available on NodeJS / TopWrite SSR process',
        );
    }
    if (storageType === 'none') {
        return null;
    } else {
        try {
            return window[storageType];
        } catch (e) {
            return null;
        }
    }
}

// Convenient storage interface for a single storage key
export interface StorageSlot {
    get: () => string | null;
    set: (value: string) => void;
    del: () => void;
}

const NoopStorageSlot: StorageSlot = {
    get: () => null,
    set: () => {
    },
    del: () => {
    },
};

/**
 * Creates an object for accessing a particular key in localStorage.
 */
export const createStorageSlot = (
    key: string,
    options?: { persistence?: StorageType },
): StorageSlot => {
    const browserStorage = getBrowserStorage(options?.persistence);
    if (browserStorage === null) {
        return NoopStorageSlot;
    }
    return {
        get: () => browserStorage.getItem(key),
        set: (value) => browserStorage.setItem(key, value),
        del: () => browserStorage.removeItem(key),
    };
};

/**
 * Returns a list of all the keys currently stored in browser storage
 * or an empty list if browser storage can't be accessed.
 */
export function listStorageKeys(
    storageType: StorageType = DefaultStorageType,
): string[] {
    const browserStorage = getBrowserStorage(storageType);
    if (!browserStorage) {
        return [];
    }

    const keys: string[] = [];
    for (let i = 0; i < browserStorage.length; i += 1) {
        const key = browserStorage.key(i);
        if (key !== null) {
            keys.push(key);
        }
    }
    return keys;
}
