export function encode(str: string, fail = false) {
    if (str === null) {
        return '';
    }
    try {
        return window.btoa(unescape(encodeURIComponent(str)));
    } catch (e) {
        if (fail) {
            throw e;
        }
        return '';
    }
}

export function decode(str: string, fail = false) {
    if (str === null) {
        return '';
    }
    try {
        return decodeURIComponent(escape(window.atob(str)));
    } catch (e) {
        if (fail) {
            throw e;
        }
        return '';
    }
}
