import { ComponentDescriptor } from '../models/components';
import { withErrorBoundary } from '../components/error-boundary';

export default (components: ComponentDescriptor[], role: string) => {
    return components
    .filter(({ descriptor }) => (role === descriptor.role))
    .sort(({ descriptor: { priority: priorityA = 0 } }, { descriptor: { priority: priorityB = 0 } }) => priorityA - priorityB)
    .map(component => withErrorBoundary(component.Component));
}
