import { ComponentDescriptor, ComponentDescriptorType } from '../models/components';

export default function createComponentDescriptor(role: string, component: ComponentDescriptorType) {
    let descriptor: ComponentDescriptor;
    if (component instanceof Array) {
        descriptor = {
            Component: component[0],
            descriptor: {
                role,
                ...component[1]
            }
        };
    } else {
        descriptor = {
            Component: component,
            descriptor: { role }
        };
    }

    return descriptor;
}
