import url from 'url';

export default class Attachment extends HTMLElement {

    connectedCallback() {
        this.addEventListener('click', (e) => {
            e.preventDefault();
            if (!this.isContentEditable) {
                const srcItem = this.attributes.getNamedItem('src');
                let src = srcItem?.value;
                if (src) {
                    let eleLink = document.createElement('a');
                    eleLink.download = this.textContent || '';
                    eleLink.style.display = 'none';
                    eleLink.target = '_blank';
                    eleLink.href = url.resolve(location.href, src);
                    document.body.appendChild(eleLink);
                    eleLink.click();
                    document.body.removeChild(eleLink);
                }
            }
        });
    }
}
