import { AnyAction, States } from '@topthink/redux-model';
import { PluginMeta } from '@topwrite/core';
import Plugin from './entities/plugin';
import { ThemeType } from './lib/create-application';
import * as models from './models';

type Models = typeof models

declare global {
    interface Window {
        TopWritePlugins: {
            [name: string]: Plugin
        };
        TopWritePluginsMeta: PluginMeta[];
    }

    interface TypeToTriggeredEventMap {
        [type: string]: CustomEvent;
    }

    type TypeEventListener<TType extends keyof TypeToTriggeredEventMap> = EventListenerBase<TypeToTriggeredEventMap[TType]>;

    type EventListenerBase<E extends Event> = (event: E) => void;

    interface EventTarget {

        addEventListener<TType extends string>(type: TType, callback: TypeEventListener<TType>, options?: AddEventListenerOptions | boolean): void;

        removeEventListener<TType extends string>(type: TType, callback: TypeEventListener<TType> | null, options?: EventListenerOptions | boolean): void;
    }

    interface HTMLAttachmentElement extends HTMLElement {
    }

    namespace JSX {

        interface IntrinsicElements {
            't-attachment': React.DetailedHTMLProps<React.AttachmentHTMLAttributes<HTMLAttachmentElement>, HTMLAttachmentElement>;
            'heading': React.DetailedHTMLProps<React.HeadingHTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>;
        }
    }

    namespace React {
        interface AttachmentHTMLAttributes<T> extends HTMLAttributes<T> {
            src?: string;
            size?: string;
        }

        interface HeadingHTMLAttributes<T> extends HTMLAttributes<T> {
            depth?: 1 | 2 | 3 | 4 | 5 | 6;
        }
    }
}

declare module '@topthink/redux-model' {
    export function useSelector<T extends keyof Models>(model: T): States<T, Models>[T];

    export interface Model {
        getState<T extends keyof Models>(model: T): Generator<AnyAction, States<T, Models>[T]>;
    }
}

declare module 'styled-components' {
    export interface DefaultTheme extends ThemeType {

    }
}

window.TopWritePlugins = {};
