import { Model } from '@topthink/redux-model';
import { InjectedCompType } from '../components/injection';
import { ComponentProps, PropsWithChildren } from 'react';
import { XBlockProps, XInlineProps } from '../components/markdown/elements/x-element';
import {
    TextDirectiveProps,
    ContainerDirectiveProps,
    LeafDirectiveProps
} from '../components/markdown/elements/directive';
import { ContextName } from '../entities/context';

export interface ComponentDescriptor<P = any> {
    Component: InjectedCompType<P>;
    descriptor: {
        role: string
        priority?: number
    };
}

export type ComponentDescriptorType<P = any> =
    ComponentDescriptor<P>['Component']
    | [ComponentDescriptor<P>['Component'], Omit<ComponentDescriptor<P>['descriptor'], 'role'>]

export interface ComponentsMap {
    'x-block': ComponentDescriptorType<XBlockProps>;
    'x-inline': ComponentDescriptorType<XInlineProps>;
    'text-directive': ComponentDescriptorType<TextDirectiveProps>;
    'leaf-directive': ComponentDescriptorType<LeafDirectiveProps>;
    'container-directive': ComponentDescriptorType<ContainerDirectiveProps>;
    'activate': ComponentDescriptorType<PropsWithChildren<{ name: ContextName }>>;
    'activate:editor': ComponentDescriptorType<PropsWithChildren<{ name: ContextName }>>;
    'activate:reader': ComponentDescriptorType<PropsWithChildren<{ name: ContextName }>>;

    [index: string]: ComponentDescriptorType;
}

export type GetComponentType<index extends keyof ComponentsMap, T = ComponentsMap[index]> = T extends InjectedCompType ? T : never
export type GetComponentProps<index extends keyof ComponentsMap> = ComponentProps<GetComponentType<index>>

export type ComponentsMapKeys = Extract<keyof ComponentsMap, string>;

export type ComponentsStateType = ComponentDescriptor[];

class Components extends Model<ComponentsStateType> {
    initialState: ComponentsStateType = [];

    *register(comp: ComponentDescriptor) {
        yield this.setState(state => state.push(comp));
    }
}

export const components = new Components();
