import { ChangeEvent, CompositionEvent, InputHTMLAttributes, useCallback, useRef } from 'react';

export default function CompositionInput({
    onChange,
    onCompositionStart,
    onCompositionEnd,
    ...props
}: InputHTMLAttributes<HTMLInputElement>) {

    const isOnComposition = useRef(false);

    const handleChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
        if (!isOnComposition.current) {
            onChange?.(e);
        }
    }, [onChange]);

    const handleCompositionStart = useCallback((e: CompositionEvent<HTMLInputElement>) => {
        isOnComposition.current = true;
        onCompositionStart?.(e);
    }, [onCompositionStart]);

    const handleCompositionEnd = useCallback((e: CompositionEvent<HTMLInputElement>) => {
        isOnComposition.current = false;
        onChange?.(e as any);
        onCompositionEnd?.(e);
    }, [onCompositionEnd, onChange]);

    return <input
        {...props}
        onChange={handleChange}
        onCompositionStart={handleCompositionStart}
        onCompositionEnd={handleCompositionEnd}
    />;
}
