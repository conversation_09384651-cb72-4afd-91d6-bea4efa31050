import { ComponentType, Fragment, ReactElement, Suspense } from 'react';

export type InjectedCompType<P = any> = ComponentType<InjectedCompProps<P>>

export interface InjectedCompProps<P = any, C = any> {
    props: P;
    Component: ComponentType<P> | string;
    children: ReactElement | null;
    context: C;
}

interface InjectionProps<P = any, C = any> {
    defaultComponent?: ComponentType<P> | string;
    component: InjectedCompType<P>;
    props?: P;
    children?: ReactElement | null;
    context?: C;
}

export default function Injection({
    component: Comp,
    defaultComponent = Fragment,
    props,
    children = null,
    context,
}: InjectionProps) {
    if (Comp === null) {
        return null;
    }

    return <Suspense fallback={null}>
        <Comp props={props} Component={defaultComponent} context={context}>{children}</Comp>
    </Suspense>;
}
