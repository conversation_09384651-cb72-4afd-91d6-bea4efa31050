import { createGlobalStyle } from 'styled-components';
import icon from '../images/attachment.svg';

const GlobalStyle = createGlobalStyle`
    :root {
        --ttw-color: #333;
        --ttw-primary-color: #3c60ff;
        --ttw-secondary-color: #9e9e9e;
        --ttw-gray-color: rgba(0, 0, 0, 0.5);
        --ttw-background: #fff;
        --ttw-border-color: #eaeaea;
        --ttw-box-color: #212529;
        --ttw-box-background: #FFF;
        --ttw-box-hover-background: #e9ecef;
        --ttw-box-active-background: #f1f2f2;
    }

    [data-theme=dark] {
        --ttw-color: #cacbcf;
        --ttw-secondary-color: #888;
        --ttw-gray-color: rgba(255, 255, 255, 0.5);
        --ttw-background: #1F2428;
        --ttw-border-color: #1B1F23;
        --ttw-box-color: #e1e4e8;
        --ttw-box-background: #2f363d;
        --ttw-box-hover-background: #39414a;
        --ttw-box-active-background: #242A30;
    }
    
    //TODO 转移到异步加载
    //附件
    t-attachment {
        color: var(--ttw-primary-color, #3c60ff);
        display: inline-flex;
        align-items: center;
        cursor: pointer;
        overflow: hidden;
        padding: 0 4px;
        vertical-align: bottom;
        margin: 0 1px;

        &:before {
            content: '';
            width: 18px;
            height: 18px;
            background-color: var(--ttw-primary-color, #3c60ff);
            mask: url("${icon}") no-repeat center;
            margin-right: 4px;
        }

        &:after {
            content: "(" attr(size) ")";
            margin-left: 4px;
            display: none;
        }

        &[size]:after {
            display: inline;
        }

        &:hover {
            background: rgba(0, 0, 0, .1);
        }

        &:read-write {
            cursor: text;
        }
    }
`;

export default GlobalStyle;
