import { Component, ComponentType, PropsWithChildren } from 'react';

export class ErrorBoundary extends Component<PropsWithChildren> {
    state = {
        hasError: false
    };

    componentDidCatch() {
        this.setState({ hasError: true });
    }

    render() {
        if (this.state.hasError) {
            return null;
        }
        return this.props.children;
    }
}

export function withErrorBoundary<Props>(WrappedComponent: ComponentType<Props>) {
    return (props: Props) => {
        return <ErrorBoundary>
            <WrappedComponent key='WrappedComponent' {...props} />
        </ErrorBoundary>;
    };
}
