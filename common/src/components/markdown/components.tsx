import { ComponentType, createContext, useContext } from 'react';

const Context = createContext<{
    [name: string]: ComponentType
}>({});


export const ComponentsProvider = Context.Provider;

export const useComponents = () => useContext(Context);

export function useComponent(name: string, defaultComponent: ComponentType | string): ComponentType | string;
export function useComponent(name: string): ComponentType | string | undefined;
export function useComponent(name: string, defaultComponent?: ComponentType | string) {
    const components = useComponents();
    return components[name] || defaultComponent;
}
