import { toc } from 'mdast-util-toc';
import toJsx from '../../utils/to-jsx';
import { toString } from 'mdast-util-to-string';
import { encode } from 'mdurl';
import { ReactNode, useMemo } from 'react';
import { MdastNodes as Root } from 'mdast-util-to-hast/lib';
import { Handlers } from 'mdast-util-to-hast';

interface Props {
    options?: {
        header?: ReactNode
        footer?: ReactNode
    } | boolean;
    tree: Root;
    extensions: Handlers[];
}

export default function Aside({ extensions, tree, options }: Props) {
    const aside = useMemo(() => {
        let aside = null;
        if (options) {
            const asideTree = toc(tree).map;
            if (asideTree) {
                aside = toJsx(asideTree, {
                    extensions: [
                        ...extensions,
                        {
                            link: (h, node) => {
                                const text = toString(node.children);
                                return h(node, 'a', { href: encode(node.url) }, [{ type: 'text', value: text }]);
                            }
                        }
                    ]
                });
            }
            if (typeof options === 'object') {
                aside = <>
                    {options.header}
                    {aside}
                    {options.footer}
                </>;
            }
        }
        return aside;
    }, [extensions, tree, options]);

    if (aside) {
        return <aside>
            {aside}
        </aside>;
    }

    return null;
}
