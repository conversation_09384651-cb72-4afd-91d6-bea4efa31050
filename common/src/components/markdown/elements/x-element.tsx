import InjectedComponent from '../../injected-component';

export interface XBlockProps {
    name: string;
    type: string;
    parameter: string;
    value: string;
}

const BlockComponent = ({ value }: XBlockProps) => {
    return <pre><code>{value}</code></pre>;
};

export interface XInlineProps {
    name: string;
    type: string;
    value: string;
}

const InlineComponent = ({ value }: XInlineProps) => {
    return <code>{value}</code>;
};

export function XBlockElement(props: XBlockProps) {
    const { name, value, parameter } = props;
    return <InjectedComponent
        role={`block:${name}`}
        props={{ type: 'block', name, parameter, value }}
        component={BlockComponent}
    />;
}

export function XInlineElement(props: XInlineProps) {
    const { name, value } = props;
    return <InjectedComponent
        role={`inline:${name}`}
        props={{ type: 'inline', name, value }}
        component={InlineComponent}
    />;
}

