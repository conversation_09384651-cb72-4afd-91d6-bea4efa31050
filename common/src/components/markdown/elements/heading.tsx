import { createElement, HeadingHTMLAttributes } from 'react';
import InjectedComponent from '../../injected-component';
import { useComponent } from '../components';

interface Props {

}

function H({ depth = 5, ...props }: HeadingHTMLAttributes<HTMLHeadingElement>) {
    return createElement(`h${depth}`, props);
}

export default function Heading(props: Props) {

    const component = useComponent('heading', H);

    return <InjectedComponent
        role={`html:heading`}
        props={props}
        component={component}
    />;
}
