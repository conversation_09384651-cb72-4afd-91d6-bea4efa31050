import InjectedComponent from '../../injected-component';
import { Children, ReactNode } from 'react';
import { useComponent } from '../components';

export type TextDirectiveProps = {
    children: ReactNode;
} & Record<string, string>

export type LeafDirectiveProps = {
    children: ReactNode;
} & Record<string, string>

export type ContainerDirectiveProps = {
    label: ReactNode;
    children: ReactNode;
} & Record<string, string>

interface Props {
    name: string;
    attributes: Record<string, string>;
    children: ReactNode;
}

const htmlDirectives = ['video', 'audio'];

export function TextOrLeafDirective(props: Props) {
    const { name, attributes, children } = props;

    let component = useComponent(name);

    let role = `html:${name}`;

    if (customElements.get(`t-${name}`)) {
        component = component || `t-${name}`;
    } else if (!htmlDirectives.includes(name)) {
        component = undefined;
        role = `directive:${name}`;
    }

    return <InjectedComponent
        role={role}
        props={{ ...attributes, children }}
        component={component}
    />;
}

export function ContainerDirective({ name, attributes, children }: Props) {

    let component = useComponent(name);

    let role = `html:${name}`;

    const [label, ...content] = Children.toArray(children);

    const props = {
        children: content,
        label,
        ...attributes
    };

    if (customElements.get(`t-${name}`)) {
        component = component || `t-${name}`;
    } else if (!htmlDirectives.includes(name)) {
        component = undefined;
        role = `directive:${name}`;
    }

    return <InjectedComponent
        role={role}
        props={props}
        component={component}
    />;
}
