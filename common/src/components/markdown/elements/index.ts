import { XBlockElement, XInlineElement } from './x-element';
import { ComponentType } from 'react';
import { ContainerDirective, TextOrLeafDirective } from './directive';
import Heading from './heading';

interface Elements {
    [index: string]: ComponentType<any>;
}

const elements: Elements = {
    'x-inline': XInlineElement,
    'x-block': XBlockElement,
    'textDirective': TextOrLeafDirective,
    'leafDirective': TextOrLeafDirective,
    'containerDirective': ContainerDirective,
    'heading': Heading
};

export default elements;
