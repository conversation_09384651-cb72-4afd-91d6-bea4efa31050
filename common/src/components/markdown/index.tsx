import { ComponentType, createElement, ElementType, Fragment, ReactNode, useEffect, useMemo, useState } from 'react';
import { markdown } from '@topwrite/core';
import slug from 'remark-slug';
import { ComponentsProvider } from './components';
import useAsyncEffect from '../../lib/use-async-effect';
import { MdastNodes as Root } from 'mdast-util-to-hast/lib';
import Aside from './aside';
import Article from './article';

export interface MarkdownProps {
    children: string
    as?: ElementType,
    sectionAs?: ElementType,
    components?: {
        [name: string]: ComponentType
    }
    header?: ReactNode
    footer?: ReactNode
    aside?: {
        header?: ReactNode
        footer?: ReactNode
    } | boolean
    css?: string
    inject?: boolean
    loader?: ReactNode
    onShow?: () => void
}

export default function Markdown({
    as = Fragment,
    children,
    components = {},
    aside = true,
    header,
    footer,
    css,
    sectionAs,
    inject = true,
    loader = null,
    onShow,
    ...props
}: MarkdownProps) {

    const md = useMemo(() => {
        return markdown()
        .use(slug)
        .freeze();
    }, []);

    const [node, setNode] = useState<ReactNode>(loader);
    const [tree, setTree] = useState<Root | null | false>(null);

    useAsyncEffect(async () => {
        try {
            const tree = await md.run(md.parse(children));
            setTree(tree);
        } catch (e) {
            console.warn(e);
            setTree(false);
        }
    }, [children, md]);

    useEffect(() => {
        if (tree !== null) {
            if (tree === false) {
                //解析失败
                setNode(null);
            } else if (tree) {
                const extensions = md.data('toHastExtensions');
                setNode(<ComponentsProvider value={components}>
                    <Article tree={tree} extensions={extensions} header={header} footer={footer} as={sectionAs} css={css} inject={inject} />
                    <Aside tree={tree} extensions={extensions} options={aside} />
                </ComponentsProvider>);
            }
            onShow && requestAnimationFrame(onShow);
        }
    }, [tree, aside, header, footer, css, sectionAs, inject]);

    return createElement(as, props, node);
}
