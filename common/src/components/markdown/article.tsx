import { MdastNodes as Root } from 'mdast-util-to-hast/lib';
import { Handlers } from 'mdast-util-to-hast';
import styled from 'styled-components';
import { createElement, ElementType, HTMLProps, PropsWithChildren, ReactNode, useMemo } from 'react';
import toJsx from '../../utils/to-jsx';
import { useComponent } from './components';
import elements from './elements';
import htmlTags from 'html-tags';
import InjectedComponent from '../injected-component';

interface ElementComponentProps extends HTMLProps<any> {
    tagName: string;
    inject?: boolean;
}

const ElementComponent = ({
    tagName,
    inject = true,
    ...props
}: PropsWithChildren<ElementComponentProps>) => {

    const component = useComponent(tagName, tagName);

    if (!inject) {
        return createElement(component, props);
    }

    if (elements[tagName]) {
        return createElement(elements[tagName], props);
    }

    if (!htmlTags.includes(tagName as any)) {
        console.warn(`unknown tagName: ${tagName}`);
        return null;
    }

    return <InjectedComponent
        role={`html:${tagName}`}
        props={props}
        component={component}
    />;

};

interface Props {
    tree: Root;
    extensions: Handlers[];
    inject?: boolean
    header?: ReactNode;
    footer?: ReactNode;
    as?: ElementType,
    css?: string
}

export default function Article({ tree, extensions, as, css, header, footer, inject }: Props) {

    const article = useMemo(() => {
        return toJsx(tree, {
            extensions,
            creator(tagName, props, children) {
                return createElement(ElementComponent, { ...props, tagName, inject }, children);
            }
        });
    }, [tree, extensions]);

    return <article>
        {header}
        <Section $css={css} as={as}>
            {article}
        </Section>
        {footer}
    </article>;
}


const Section = styled.section<{ $css?: string }>`
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
    line-height: 1.2;
    word-wrap: break-word;

    & > *:first-child {
        margin-top: 0 !important;
    }

    & > *:last-child {
        margin-bottom: 0 !important;
    }

    h1, h2, h3, h4, h5, h6, p, ul, ol, blockquote, pre, table, audio, video {
        margin-top: 0;
        margin-bottom: 1rem;
        line-height: 1.5;
        padding: 0;

        &:last-child {
            margin-bottom: 0 !important;
        }

        &:first-child {
            margin-top: 0 !important;
        }
    }

    h1, h2, h3, h4, h5, h6 {
        font-weight: 500;
        margin-top: 2rem;
        margin-bottom: 1rem;
        line-height: 1.2;

        &.align-right {
            text-align: right;
        }

        &.align-center {
            text-align: center;
        }
    }

    h1 {
        font-size: 2.5em;
    }

    h2 {
        font-size: 2em;
    }

    h3 {
        font-size: 1.75em;
    }

    h4 {
        font-size: 1.5em;
    }

    h5 {
        font-size: 1.25em;
    }

    hr {
        border: 0;
        border-bottom: 1px solid #eeeeee;
        margin-bottom: 0.5em;
    }

    ul {
        padding-left: 2em;

        ul {
            margin: 0;
        }
    }

    ol {
        padding-left: 2em;
    }

    li {
        line-height: 2;

        &.task-list-item {
            list-style: none;
            position: relative;

            input[type=checkbox] {
                position: absolute;
                left: -1.5em;
                top: 0.5em;
                width: 1em;
                height: 1em;
            }
        }

        &.align-right {
            text-align: right;
        }

        &.align-center {
            text-align: center;
        }
    }

    code {
        display: inline-block;
        border-radius: 4px;
        padding: .2em .4em;
        background-color: #f7f7f7;
        word-break: break-all;
        font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
        white-space: pre;
        line-height: 1.3;
        color: inherit;
        font-size: inherit;
        margin: -.2em .4em;
    }

    pre {
        padding: 1.05em;
        overflow: auto;
        line-height: 1.45;
        background-color: #f7f7f7;
        border: 0;
        border-radius: 3px;
        font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
        font-size: inherit;
    }


    pre > code {
        display: inline;
        max-width: 100%;
        padding: 0;
        margin: 0;
        overflow: initial;
        line-height: inherit;
        background-color: rgba(0, 0, 0, 0);
        border: 0;
        tab-size: 4;
    }

    p {
        &.align-right {
            text-align: right;
        }

        &.align-center {
            text-align: center;
        }
    }

    table {
        border-collapse: collapse;
        table-layout: fixed;
        width: 100%;
        line-height: 1.8;
        border-spacing: 0;

        th {
            text-align: left;

            &[align='center'] {
                text-align: center;
            }

            &[align='right'] {
                text-align: right;
            }
        }

        th, td {
            border: 1px solid #e0e0e0;
            padding: 3px 12px;
            height: 34px;
        }

        tr:hover td {
            background-color: #f1f1f1;
        }
    }

    img {
        max-width: 100%;
        display: inline-block;
        padding: 3px;
        vertical-align: middle;
    }

    a {
        text-decoration: none;
        color: var(--ttw-primary-color, #3c60ff);

        &:hover {
            text-decoration: underline;
        }
    }

    blockquote {
        padding: 5px 5px 5px 15px;
        color: #858585;
        border-left: 4px solid #e5e5e5;

        &.info {
            border-left-color: #5bc0de;
            color: #5bc0de;
            background-color: #f4f8fa;
        }

        &.warning {
            background-color: #fcf8f2;
            border-color: #f0ad4e;
            color: #f0ad4e;
        }

        &.danger {
            color: #d9534f;
            background-color: #fdf7f7;
            border-color: #d9534f;
        }

        &.success {
            background-color: #f3f8f3;
            border-color: #50af51;
            color: #50af51;
        }
    }

    video {
        display: block;
        width: 100%;
    }

    audio {
        display: block;
        width: 100%;
    }

    [data-theme=dark] & {
        code {
            background-color: #333437;
        }

        pre {
            background-color: #333437;
        }

        h1, h2, hr {
            border-color: #404040;
        }

        th, td {
            border-color: #404040;
        }

        tr:hover td {
            background-color: #333437;
        }

        blockquote {
            border-left-color: #858585;

            &.info {
                border-left-color: rgb(27, 110, 134);
                color: rgb(98, 195, 223);
                background-color: rgb(19, 34, 42);
            }

            &.warning {
                background-color: rgb(49, 34, 11);
                border-color: rgb(147, 91, 12);
                color: rgb(241, 176, 84);
            }

            &.danger {
                color: rgb(219, 94, 90);
                background-color: rgb(46, 11, 11);
                border-color: rgb(137, 33, 30);
            }

            &.success {
                background-color: rgb(27, 39, 23);
                border-color: rgb(56, 122, 57);
                color: rgb(98, 183, 98);
            }
        }
    }

    ${props => props.$css}
`;
