import { ComponentType, createElement, useMemo } from 'react';
import Injection from './injection';
import findMatchingComponents from '../utils/find-matching-components';
import { useSelector } from '@topthink/redux-model';
import { ComponentsMapKeys } from '../models/components';

interface WrapElementProps<P, C> {
    props?: P;
    component: ComponentType<P> | string;
    context?: C;
}

function WrapElement<P extends {}, C extends {}>({ component, props }: WrapElementProps<P, C>) {
    return createElement(component, props);
}

interface InjectedComponentProps<P, C> {
    role: ComponentsMapKeys;
    props?: P;
    component?: ComponentType<P> | string;
    context?: C;
}

export default function InjectedComponent<P extends {}, C extends {}>({
    props,
    role,
    component,
    context
}: InjectedComponentProps<P, C>): JSX.Element {

    const children = component ? <WrapElement component={component} props={props} context={context} /> : null;

    const components = useSelector('components');

    const matchingComps = useMemo(() => findMatchingComponents(components, role), [components, role]);

    return matchingComps.reduce((inner, Comp) => {
        return <Injection defaultComponent={component} component={Comp} props={props} context={context}>{inner}</Injection>;
    }, children) as JSX.Element;
};
