import { ComponentType, useMemo } from 'react';
import Injection from './injection';
import findMatchingComponents from '../utils/find-matching-components';
import { useSelector } from '@topthink/redux-model';
import { ComponentsMapKeys } from '../models/components';

interface InjectedComponentSetProps<P, C> {
    role: ComponentsMapKeys;
    props?: P;
    component?: ComponentType<P> | string;
    context?: C;
}

export default function InjectedComponentSet<P extends {}, C extends {}>({
    props,
    component,
    role,
    context,
}: InjectedComponentSetProps<P, C>) {

    const components = useSelector('components');

    const matchingComps = useMemo(() => findMatchingComponents(components, role), [components, role]);

    return <>{matchingComps.map((Comp, i) =>
        <Injection key={i} defaultComponent={component} component={Comp} props={props} context={context} />)}</>;
};
