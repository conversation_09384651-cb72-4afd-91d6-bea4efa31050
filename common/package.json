{"name": "@topwrite/common", "version": "1.0.65", "description": "Redux Controller", "scripts": {"build": "rollup -c --environment NODE_ENV:production", "build:dev": "rollup -c", "watch": "rollup -c -w", "prepack": "npm run build"}, "module": "lib/index.js", "types": "types/index.d.ts", "files": ["lib", "types", "extends.d.ts"], "author": "yunwuxin <<EMAIL>> (https://github.com/yunwuxin)", "license": "ISC", "dependencies": {"@babel/runtime": "^7.11.2", "@react-hook/event": "^1.2.6", "@topthink/redux-model": "^1.0.7", "@topwrite/core": "^1.0.41", "@types/hast": "^2.3.1", "@types/json-schema": "^7.0.9", "@types/loadjs": "^4.0.0", "@types/react-helmet": "^6.1.9", "@types/styled-components": "^5.1.11", "@types/unist": "^2.0.3", "hast-to-hyperscript": "^10.0.0", "html-tags": "^3.1.0", "loadjs": "^4.2.0", "mdast-util-to-hast": "^12.0.0", "mdast-util-to-string": "^3.1.0", "mdast-util-toc": "^6.1.0", "mdurl": "^1.0.1", "query-string": "^7.1.1", "react-async-hook": "^4.0.0", "react-helmet": "^6.1.0", "react-hotkeys-hook": "^3.3.0", "react-intl": "^5.8.6", "react-is": "^17.0.2", "remark-slug": "^7.0.0", "styled-components": "^5.3.0", "unist-util-is": "^5.0.0"}, "peerDependencies": {"lodash": "*", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@babel/core": "^7.13.8", "@babel/plugin-transform-runtime": "^7.11.5", "@babel/preset-env": "^7.13.9", "@rollup/plugin-babel": "^5.2.1", "@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-image": "^2.1.1", "@rollup/plugin-node-resolve": "^13.0.0", "@svgr/rollup": "^6.2.0", "@types/lodash": "^4.14.161", "@types/mdurl": "^1.0.2", "@types/node": "^16.18.14", "@types/react": "^18", "rollup": "^2.26.11", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.1", "typescript": "^5.3.3"}}