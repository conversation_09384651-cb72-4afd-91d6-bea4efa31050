{"compilerOptions": {"target": "esnext", "module": "esnext", "lib": ["dom", "es2017"], "jsx": "react-jsx", "declaration": true, "declarationDir": "./types", "sourceMap": true, "removeComments": false, "strict": true, "noEmit": true, "noUnusedLocals": true, "noUnusedParameters": true, "moduleResolution": "node", "baseUrl": "./types", "esModuleInterop": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true}, "include": ["./src/**/*"]}