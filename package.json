{"name": "topwrite-packages", "private": true, "version": "1.0.0", "description": "", "scripts": {"publish-package": "lerna publish from-package", "publish-patch": "lerna <PERSON>", "publish-canary": "lerna publish --canary", "build": "pnpm run -r build"}, "workspaces": ["common", "core", "editor", "reader", "cli", "types", "loading-bar", "plugin-cli", "docsearch"], "author": "", "license": "ISC", "dependencies": {"lerna": "^5.4.0"}, "resolutions": {"@types/react": "18", "@types/react-dom": "18", "react": "18", "react-dom": "18", "@types/unist": "^2"}}