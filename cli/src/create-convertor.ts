import WebsiteConvertor from './convertor/website';

const convertors = {
    website: WebsiteConvertor,
};

export default async function createConvertor(type: keyof typeof convertors, source: any, dir: string, dest: string) {
    if (typeof convertors[type] === 'undefined') {
        throw new Error('unsupported type: ' + type);
    }

    const convertor = convertors[type];

    return new convertor(source, dir, dest);
}
