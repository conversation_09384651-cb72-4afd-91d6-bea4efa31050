#! /usr/bin/env node

import { Command } from 'commander';
import tmp from 'tmp';
import path from 'path';
import logger, { LogLevel } from './logger';
import createGenerator from './create-generator';
import { getConfig, setConfig } from './config';
import createConvertor from './create-convertor';
import * as querystring from 'node:querystring';

const pkg = require('../package.json');

const program = new Command();

program.version(pkg.version);
program.name('ttx');
program.option('-d, --debug', 'output extra debugging');

program.on('option:debug', function() {
    logger.level = LogLevel.Debug;
});

program.command('build <dir> <format> [dest]')
       .option('--sha <sha>', 'book hash value')
       .option('--no-clean', 'not clean')
       .description('build book')
       .action(async function(dir, format, dest, opt) {
           if (opt.clean) {
               tmp.setGracefulCleanup();
           }

           logger.info(`cli version ${pkg.version}`);
           const root = path.resolve(dir);
           dest = dest ? path.resolve(dest) : path.join(root, 'book.tar.gz');

           try {
               const generator = await createGenerator(dir, format, dest, pkg.version, opt.sha);

               await generator.generate();
               process.exit(0);
           } catch (e: any) {
               logger.error(e.message);
               process.exit(1);
           }
       })
;

program.command('convert')
       .requiredOption('--type <type>', '')
       .requiredOption<object>('--source <source>', '', function(value, previous = {}) {
           return {
               ...querystring.parse(value),
               ...previous
           };
       })
       .option('--dir <dir>')
       .option('--dest <dest>')
       .option('--no-clean', 'not clean')
       .description('convert book')
       .action(async function({ type, source, dest = '.', dir = '', clean }) {
           if (clean) {
               tmp.setGracefulCleanup();
           }
           dest = path.resolve(dest);
           try {
               const convertor = await createConvertor(type, source, dir, dest);
               await convertor.convert();
               process.exit(0);
           } catch (e: any) {
               logger.error(e.message);
               process.exit(1);
           }
       });

const configProgram = program.command('config');

configProgram.command('list')
             .action(function() {
                 console.log(getConfig());
             });

configProgram.command('set <key> <value>')
             .action(function(key, value) {
                 setConfig(key, value);
             });

program.parse(process.argv);
