import { Summary, SummaryArticle } from '@topwrite/core';
import Generator from './index';
import logger from '../logger';
import fs from 'fs-extra';
import { pinyin } from 'pinyin-pro';
import path from 'path';

export default class JsonGenerator extends Generator {

    protected format = 'json';

    protected scripts: string[] = [];

    protected async onInit() {
        this.processSummary();

        await this.installCommonAssets();
        await this.installReader();
        await this.copyPluginsAssets();
    }

    protected processSummary() {
        this.book.summary.getArticle(article => {
            if (this.book.config.getValue(['release', 'pathEncode'], true)) {
                article.path = pinyin(article.path, {
                    toneType: 'none',
                    nonZh: 'consecutive',
                    type: 'array',
                    v: true
                }).join('');
            }
            article.hasPath = true;
            return false;
        });
    }

    protected async installCommonAssets() {
        logger.info(`installing common assets ...`);

        await this.installPackage('lodash', 'lodash.min.js', 'lodash/lodash.min.js');
        await this.installPackage('react', 'umd/react.production.min.js', 'react/react.production.min.js');
        await this.installPackage('react-dom', 'umd/react-dom.production.min.js', 'react-dom/react-dom.production.min.js');

        logger.info(`common assets installed with success`);
    }

    protected async installReader() {
        logger.info(`installing package @topwrite/reader ...`);

        const info = await this.installPackage('@topwrite/reader', 'dist/', 'reader');

        logger.info(`package @topwrite/reader@${info.version} installed with success`);
    }

    protected async installPackage(name: string, from: string, to: string) {

        const pkgPath = require.resolve(`${name}/package.json`);
        const info = require(pkgPath);

        if (this.useCdn()) {
            if (from.endsWith('/')) {
                from = `${from}index.js`;
            }
            this.scripts.push(`https://jsdelivr.topthink.com/npm/${name}@${info.version}/${from}`);
        } else {
            const src = path.join(path.dirname(pkgPath), from);
            const dest = path.join(this.tmpFS.root, 'asset', to);

            await fs.copy(src, dest);

            if (from.endsWith('/')) {
                to = `${to}/index.js`;
            }

            this.scripts.push(`asset/${to}?v=${info.version}`);
        }

        return info;
    }

    protected async copyPluginsAssets() {
        for (const plugin of this.plugins) {
            if (this.useCdn()) {
                const host = this.options.plugins.host;
                this.scripts.push(`${host}/plugins/${plugin.name}/${plugin.version}/dist/index.js`);
            } else {
                await fs.copy(path.join(plugin.path, 'dist'), path.join(this.tmpFS.root, 'asset', 'plugins', plugin.name));
                this.scripts.push(`asset/plugins/${plugin.name}/index.js?v=${plugin.version}`);
            }
        }
    }

    protected useCdn() {
        return this.options.asset === 'cdn';
    }

    protected async onReadme() {
        //readme
        const file = await this.makeFile({ path: 'index.html', ref: 'README.md' });
        await this.tmpFS.unlink('README.md');
        await this.tmpFS.writeFile('index.html.json', this.stringify(file));
    }

    protected async onArticle(article: SummaryArticle) {
        const newPath = article.path + '.json';
        if (!this.tmpFS.exist(newPath)) {
            const file = await this.makeFile(article);

            // 把metadata写入summary
            article.metadata = {
                ...file.meta,
                ...article.metadata
            };

            await this.tmpFS.unlink(article.ref);
            await this.tmpFS.writeFile(newPath, this.stringify(file));
        }
    }

    protected async onSummary() {
        const summary = this.book.summary;
        await this.tmpFS.unlink(Summary.file.json);
        await this.tmpFS.unlink(Summary.file.markdown);

        await this.tmpFS.writeFile(Summary.file.json, this.stringify(summary.toObject()));

        const [ctime, mtime] = this.sourceFS.readFileTime();

        //元数据
        const context = {
            id: this.book.id,
            sha: this.book.sha,
            lfs: this.options.lfs,
            scripts: this.scripts,
            poweredBy: this.options.poweredBy,
            ctime: ctime,
            mtime: mtime,
        };

        await this.tmpFS.writeFile('context.json', this.stringify(context));
    }

    protected async onFinish() {
        await this.packBook();
    }

    protected async packBook() {

        logger.info('pack book ...');

        const output = fs.createWriteStream(this.dest);

        await this.tmpFS.archive(output, 'tar');
    }

    protected stringify(obj: object) {
        return JSON.stringify(obj, null, 4);
    }

}
