import { Book, File, HookContext, LetterAvatar, SummaryArticle, request, PluginCenterConfig } from '@topwrite/core';
import logger from '../logger';
import FS from '../fs';
import Plugin from '../entities/plugin';
import loadPlugins from '../load-plugins';
import yaml from 'js-yaml';
import { createCanvas } from 'canvas';
import path from 'path';
import spawn from 'cross-spawn';

export interface Options {
    plugins: Required<PluginCenterConfig>;
    lfs?: {
        url: string;
        files: {
            [index: string]: string
        }
    };
    poweredBy: false | {
        name: string
        link: string
    };
    asset: 'local' | 'cdn';
}

const MATTER_PATTERN = /(^-{3}(?:\r\n|\r|\n)([\w\W]*?)-{3}(?:\r\n|\r|\n))?([\w\W]*)*/;

const asyncEvery = async <T>(arr: Array<T>, predicate: (value: T) => unknown) => {
    for (let e of arr) {
        if (!await predicate(e)) return false;
    }
    return true;
};

export default abstract class Generator {

    protected abstract format: string;

    protected book: Book;
    protected sourceFS: FS;
    protected tmpFS: FS;
    protected dest: string;
    protected version: string;
    protected options: Options;
    protected plugins: Plugin[] = [];

    constructor(book: Book, sourceFS: FS, tmpFS: FS, options: Options, dest: string, version: string) {
        this.book = book;
        this.sourceFS = sourceFS;
        this.tmpFS = tmpFS;
        this.options = options;
        this.dest = dest;
        this.version = version;
    }

    protected getContext(): HookContext {
        return {
            book: this.book,
            sourceFS: this.sourceFS,
            tmpFS: this.tmpFS,
            request,
            logger,
            format: this.format
        };
    }

    async generate() {

        await this.initLfs();

        await this.initPlugins();

        await this.onInit();
        await this.callHook('init');

        await this.checkStyle();
        await this.generateLogo();

        await this.onReadme();
        await this.callHook('readme');

        await this.generateArticles();
        await this.callHook('articles');

        await this.onSummary();
        await this.callHook('summary');

        if (await this.callHook('beforeFinish')) {
            await this.onFinish();
            await this.callHook('finish');
        }

        logger.success('generation finished with success!');
    }

    protected async checkStyle() {
        if (!this.tmpFS.exist(Book.style)) {
            await this.tmpFS.writeFile(Book.style, '');
        }
    }

    protected async initLfs() {
        if (this.options.lfs) {
            logger.info('generate lfs files...');
            let command = `git lfs ls-files -l`;

            const { config } = this.book;

            const root = config.getValue('root', '');

            if (root) {
                command += ` --include ${root}`;
            }

            const { error, stdout } = spawn.sync(command, {
                stdio: ['inherit'],
                encoding: 'utf8',
                cwd: this.sourceFS.root,
                shell: true
            });

            const files: { [index: string]: string } = {};
            if (!error) {
                stdout.trim().split('\n').forEach((line) => {
                    if (line) {
                        const [hash, name] = line.trim().split(/\s[-*]\s/);
                        if (hash && name) {
                            let filename = name.trim();
                            if (root) {
                                filename = path.relative(root, filename);
                            }
                            files[filename] = hash.trim();
                            //删除原文件
                            this.tmpFS.unlink(filename);
                        }
                    }
                });
            }

            this.options.lfs.files = files;
            logger.info(`found ${Object.keys(files).length} files`);
        }
    }

    protected async initPlugins() {
        this.plugins = await loadPlugins(this.book, this.tmpFS.root, this.options.plugins);
    }

    protected async generateLogo() {
        if (!this.tmpFS.exist(Book.logo)) {
            const title = this.book.config.getValue('title', 'Untitled');
            const canvas = createCanvas(60, 60);
            LetterAvatar.create(title, 60, canvas);

            const buffer = canvas.toBuffer('image/png');

            await this.tmpFS.writeFile(Book.logo, buffer);
        }
    }

    protected async generateArticles() {
        logger.info('generate articles ...');

        const articles = this.book.summary.getArticles();

        logger.info(`found ${articles.length} articles`);

        for (const article of articles) {
            if (article.ref) {
                logger.info('generate article "' + article.getRef() + '"');
                try {
                    await this.onArticle(article);
                    await this.callHook('article', article);
                } catch (e: any) {
                    if (e.code === 'ENOENT') {
                        logger.warn(`article file [${article.ref}] not exist`);
                    } else {
                        logger.warn(`generate article [${article.ref}] failed: ${e.message}`);
                    }
                }
            }
        }
    }

    protected abstract onInit(): Promise<void>

    protected abstract onReadme(): Promise<void>

    protected abstract onArticle(article: SummaryArticle): Promise<void>

    protected abstract onSummary(): Promise<void>

    protected abstract onFinish(): Promise<void>

    protected async callHook(name: string, ...args: any[]) {
        logger.debug('calling hook "' + name + '"');

        return await asyncEvery(this.plugins, async (plugin) => {
            const hook = plugin.getHook(name);

            if (hook) {
                return await hook.apply(plugin.getContext(this.getContext()), args);
            }
            return true;
        });
    }

    protected async makeFile(article: { ref: string, path: string }) {
        const content = await this.tmpFS.readAsString(article.ref);
        const parsed = this.parseFileMeta(article.ref, content);

        return new File({ path: article.path, ...parsed });
    }

    protected parseFileMeta(filename: string, content: string) {
        const matches = content.match(MATTER_PATTERN);
        let meta: any = {};
        if (matches) {
            if (matches[2] !== undefined) {
                try {
                    meta = yaml.load(matches[2]) || {};
                } catch {

                }
            }
            if (matches[3] !== undefined) {
                content = matches[3];
            } else {
                content = '';
            }
        }

        const sourceFilename = path.join(this.book.config.getValue('root', ''), filename);

        const [ctime, mtime] = this.sourceFS.readFileTime(sourceFilename);

        meta.ctime = ctime;
        meta.mtime = mtime;

        return { meta, content };
    }

}
