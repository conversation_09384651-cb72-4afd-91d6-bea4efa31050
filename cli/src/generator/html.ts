import { File, isUrl, Summary, SummaryArticle } from '@topwrite/core';
import ejs, { TemplateFunction } from 'ejs';
import fs from 'fs-extra';
import JsonGenerator from './json';
import path from 'path';

export default class HtmlGenerator extends JsonGenerator {

    protected template!: TemplateFunction;

    protected format = 'html';

    protected async onInit() {
        await super.onInit();
        await this.initTemplate();
    }

    protected async initTemplate() {
        const str = await fs.readFile(path.resolve(__dirname, '../templates/html.ejs'), 'utf-8');
        this.template = ejs.compile(str);
    }

    protected useCdn() {
        return false;
    }

    protected async onReadme() {
        const file = await this.makeFile({ path: 'index.html', ref: 'README.md' });
        const html = await this.renderFile(file);
        await this.tmpFS.unlink('README.md');
        await this.tmpFS.writeFile('index.html', html);
    }

    protected async onArticle(article: SummaryArticle): Promise<void> {
        if (!this.tmpFS.exist(article.path)) {
            const file = await this.makeFile(article);

            // 把metadata写入summary
            article.metadata = {
                ...file.meta,
                ...article.metadata
            };

            const html = await this.renderFile(file, article.title);

            await this.tmpFS.unlink(article.ref);
            await this.tmpFS.writeFile(article.path, html);
        }
    }

    protected async onSummary() {
        await this.tmpFS.unlink(Summary.file.json);
        await this.tmpFS.unlink(Summary.file.markdown);
    }

    protected async renderFile(file: File, title?: string) {
        const context = {
            id: this.book.id,
            sha: this.book.sha,
            metadata: this.book.metadata,
            summary: this.book.summary.toObject(),
            config: this.book.config.toObject(),
            file: file,
            lfs: this.options.lfs,
            options: {
                poweredBy: this.options.poweredBy
            }
        };

        const scripts = this.scripts.map(script => isUrl(script) ? script : file.relative(script));

        const bookTitle = this.book.config.getValue('title', 'Untitled');

        const articleTitle = file.getMeta('title', title);

        return this.template({
            title: (articleTitle ? `${articleTitle} - ` : '') + bookTitle,
            keywords: file.getMeta('keywords'),
            description: file.getMeta('description'),
            scripts: scripts,
            payload: JSON.stringify(context).replace(/\//g, '\\/')
        });
    }

}
