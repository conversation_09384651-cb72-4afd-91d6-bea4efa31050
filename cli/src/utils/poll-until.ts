interface Options {
    tryEvery: number;
    stopAfter: number;
}

export function pollUntil<T = any>(callback: Function, options: Options) {

    const { tryEvery, stopAfter } = options;

    const start = Date.now();

    const runCallback = (resolve: any, reject: any) => {
        setTimeout(async () => {
            try {
                const result = await callback();
                resolve(result);
            } catch (e) {
                if ((Date.now() - start) > stopAfter) {
                    reject(e);
                } else {
                    runCallback(resolve, reject);
                }
            }
        }, tryEvery);
    };

    return new Promise<T>(runCallback);
}
