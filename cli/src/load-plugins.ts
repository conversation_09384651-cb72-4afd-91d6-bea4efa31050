import { Book, createPluginCenter, PluginCenterConfig, PluginMeta } from '@topwrite/core';
import logger from './logger';
import spawn from 'cross-spawn';
import path from 'path';
import Plugin from './entities/plugin';
import * as fs from 'fs';
import { NodeVM } from 'vm2';

async function loadPlugin(meta: PluginMeta, dir: string) {
    logger.info(`installing plugin "${meta.name}"`);

    const command = `npm install ${meta.host}.tgz`;

    const { error } = spawn.sync(command, {
        stdio: ['inherit'],
        cwd: dir,
        shell: true
    });

    if (error) {
        throw error;
    }

    const modulePath = path.join(dir, 'node_modules', 'topwrite-plugin-' + meta.name);
    const hooksPath = path.join(modulePath, 'dist', 'hooks.js');
    let hooks = {};
    if (fs.existsSync(hooksPath)) {
        const vm = new NodeVM({
            require: {
                builtin: ['path/posix', 'crypto'],
            },
            sandbox: {
                URL,
                URLSearchParams
            }
        });
        hooks = vm.runFile(hooksPath);
    }

    const configPath = path.join(modulePath, 'config.json');
    let config = {};
    if (fs.existsSync(configPath)) {
        config = require(configPath);
    }

    logger.info(`plugin "${meta.name}@${meta.version}" installed with success`);

    return new Plugin({
        name: meta.name,
        version: meta.version,
        path: modulePath,
        hooks,
        config
    });
}

export default async function loadPlugins(book: Book, dir: string, config: PluginCenterConfig) {

    const pluginCenter = createPluginCenter(config);

    const metas = await pluginCenter.getInstalled(book);

    const plugins: Plugin[] = [];

    const theme = book.config.getValue('theme');

    logger.info(`installing ${metas.length} plugins from registry`);

    for (let meta of metas) {
        if (meta.disabled) {
            throw new Error('plugin "' + meta.name + '" has been disabled');
        }
        if (meta.type !== 'theme' && theme === 'ebook' && !meta.ebook) {
            //ebook下过滤一些插件
            logger.info(`plugin "${meta.name}" ignored in ebook`);
            continue;
        }
        plugins.push(await loadPlugin(meta, dir));
    }

    return plugins;
}
