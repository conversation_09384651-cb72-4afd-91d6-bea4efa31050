import Crawler, { ArticleData } from './index';
import { pollUntil } from '../utils/poll-until';
import { Page } from 'puppeteer-core';

export default class Docusaurus extends Crawler {

    async getArticles(page: Page) {
        await page.addScriptTag({ content: pollUntil.toString() });
        return page.evaluate(async () => {
            const nav = document.querySelector('aside nav>ul');
            if (nav) {
                const getArticles = async (node: Element) => {
                    const list = node.querySelectorAll('&>li');
                    const articles: ArticleData[] = [];

                    for (const li of Array.from(list)) {
                        const link = li.querySelector('a');

                        if (link) {
                            let children: any[] = [];
                            if (link.className.includes('menu__link--sublist')) {
                                link.click();
                                try {
                                    const ul = await pollUntil<Element>(() => {
                                        const ul = li.querySelector('&>ul');
                                        if (!ul) {
                                            throw new Error('render html timeout');
                                        }
                                        return ul;
                                    }, {
                                        stopAfter: 5 * 1000,
                                        tryEvery: 200
                                    });

                                    children = await getArticles(ul);
                                } catch (e: any) {
                                    console.log(e.message);
                                }
                            }

                            articles.push({
                                title: link.textContent?.trim() || '未命名',
                                url: link.getAttribute('href') || '#',
                                children
                            });
                        }
                    }
                    return articles;
                };

                return await getArticles(nav);
            }
            return [];
        });
    }

    async getContent(page: Page) {
        const ele = await page.$('article .markdown');
        if (ele) {
            return await ele.evaluate(node => {
                //删除标题的隐藏锚点
                const anchors = node.querySelectorAll('a.hash-link');
                anchors.forEach((anchor) => {
                    anchor.remove();
                });

                return node.innerHTML;
            });
        }
    }

}
