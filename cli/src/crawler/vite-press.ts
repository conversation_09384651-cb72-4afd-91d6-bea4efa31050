import { Page } from 'puppeteer-core';
import Crawler, { ArticleData } from './index';

export default class VitePress extends Crawler {
    async getArticles(page: Page): Promise<ArticleData[]> {

        return page.evaluate(async () => {
            const groups = document.querySelectorAll('aside nav .group');

            const groupArticles = await Promise.all(Array.from(groups).map(async (nav) => {
                const getArticles = async (node: Element) => {
                    const list = node.querySelectorAll('&>.VPSidebarItem');
                    const articles: ArticleData[] = [];

                    for (const li of Array.from(list)) {
                        const link = li.querySelector('&>.item');

                        if (link) {
                            let children: ArticleData[] = [];

                            const ul = li.querySelector('&>.items');
                            if (ul) {
                                children = await getArticles(ul);
                            }

                            articles.push({
                                title: link.querySelector('.text')?.textContent || '未命名',
                                url: link.querySelector('.VPLink')?.getAttribute('href') || '#',
                                children
                            });
                        }
                    }
                    return articles;
                };

                return await getArticles(nav);
            }));

            return groupArticles.flat();
        });
    }

    async getContent(page: Page): Promise<string | undefined> {
        const ele = await page.$('.VPContent main');
        if (ele) {
            return await ele.evaluate(node => {
                //删除标题的隐藏锚点
                node.querySelectorAll('a.header-anchor')
                    .forEach(anchor => anchor.remove());

                //删除隐藏的html块
                node.querySelectorAll('.vp-doc [class*=language-]>span.lang')
                    .forEach(element => element.remove());

                return node.innerHTML;
            });
        }
    }

}
