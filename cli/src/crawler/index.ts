import { Page } from 'puppeteer-core';

export interface ArticleData {
    title: string;
    url: string;
    children: ArticleData[];
}

export default abstract class Crawler {

    abstract getArticles(page: Page): Promise<ArticleData[]>;

    abstract getContent(page: Page): Promise<string | undefined>

    async prepareContentPage(page: Page, url?: string) {
        if (url) {
            await page.goto(url);
        }
    }
}
