import { Page } from 'puppeteer-core';
import Crawler, { ArticleData } from './index';

export default class Docsify extends Crawler {
    async getArticles(page: Page): Promise<ArticleData[]> {
        const sidebar = await page.waitForSelector('aside .sidebar-nav>ul');
        if (sidebar) {
            return sidebar.evaluate(async (node) => {
                const getArticles = async (node: Element) => {
                    const list = node.querySelectorAll('&>li');
                    const articles: ArticleData[] = [];

                    for (const li of Array.from(list)) {
                        let link = li.firstChild;

                        if (link) {
                            let url = '#';
                            if (link instanceof HTMLElement) {
                                link = link.querySelector('&>a') || link;
                                if (link instanceof HTMLElement) {
                                    if (link.className.includes('section-link')) {
                                        continue;
                                    }
                                    url = link.getAttribute('href') || url;
                                }
                            }

                            let children: ArticleData[] = [];

                            const ul = li.querySelector('&>ul');
                            if (ul) {
                                children = await getArticles(ul);
                            }

                            articles.push({
                                title: link.textContent || '未命名',
                                url,
                                children
                            });
                        }
                    }
                    return articles;
                };

                return await getArticles(node);
            });
        }
        return [];
    }

    async getContent(page: Page): Promise<string | undefined> {
        await page.reload();
        await page.waitForSelector('section.content article.markdown-section>*');
        return page.evaluate(() => {
            const node = document.querySelector('section.content article.markdown-section');
            if (node) {
                //删除编辑按钮
                const editElement = node.firstElementChild;
                if (editElement?.tagName === 'P') {
                    editElement.remove();
                }
                //删除标题的隐藏锚点
                const anchors = node.querySelectorAll('a.anchor');
                anchors.forEach((anchor) => {
                    anchor.replaceWith(...Array.from(anchor.children));
                });

                return node.innerHTML;
            }
        });

    }

}
