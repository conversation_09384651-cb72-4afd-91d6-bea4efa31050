import { Page } from 'puppeteer-core';
import Crawler, { ArticleData } from './index';

export default class GitBook extends Crawler {
    getArticles(page: Page): Promise<ArticleData[]> {
        return page.evaluate(() => {
            const ul = document.querySelector('aside ul');
            if (ul) {
                const getArticles = (node: Element) => {
                    const list = node.querySelectorAll('&>li');

                    const articles: ArticleData[] = [];

                    for (const li of Array.from(list)) {
                        let link = li.firstElementChild;

                        if (link) {
                            link = link.querySelector('a') || link;

                            let children: ArticleData[] = [];
                            const ul = li.querySelector('ul');
                            if (ul) {
                                children = getArticles(ul);
                            }

                            articles.push({
                                title: link.textContent?.trim() || '未命名',
                                url: link.getAttribute('href') || '#',
                                children
                            });
                        }
                    }
                    return articles;
                };
                return getArticles(ul);
            }
            return [];
        });
    }

    getContent(page: Page): Promise<string | undefined> {
        return page.evaluate(() => {
            const ele = document.querySelector('main header')?.nextElementSibling;
            if (ele) {
                //删除标题的隐藏锚点
                const anchors = ele.querySelectorAll('.opacity-0');
                anchors.forEach((anchor) => {
                    anchor.remove();
                });

                return ele.innerHTML;
            }
        });
    }

}
