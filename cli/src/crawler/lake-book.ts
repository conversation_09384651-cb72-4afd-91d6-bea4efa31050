import { Page } from 'puppeteer-core';
import Crawler, { ArticleData } from './index';

interface TocItem {
    uuid: string;
    parent_uuid: string;
    type: 'LINK' | 'DOC' | 'TITLE';
    title: string;
    url: string;
}

interface TreeItem extends TocItem {
    children: TreeItem[];
}

declare global {
    interface Window {
        appData: {
            book: {
                toc: TocItem[]
                slug: string
            },
            group: {
                login: boolean
            }
        } | object;
        appNamespace: string;
    }
}

export default class LakeBook extends Crawler {
    async getArticles(page: Page): Promise<ArticleData[]> {
        return page.evaluate(() => {
            const appData = window.appData;
            if ('book' in appData) {
                const { toc, slug } = appData.book;
                const { login } = appData.group;
                const map: Record<string, TreeItem> = {};
                const root: TreeItem[] = [];

                toc.forEach(item => {
                    if (item.type !== 'LINK') {
                        map[item.uuid] = { ...item, children: [] };
                    }
                });
                toc.forEach(item => {
                    const node = map[item.uuid];
                    if (node) {
                        if (item.parent_uuid) {
                            if (map[item.parent_uuid]) {
                                map[item.parent_uuid].children.push(node);
                            }
                        } else {
                            root.push(node);
                        }
                    }
                });

                const tocToArticle = (list: TreeItem[]) => {
                    return list.map((item) => ({
                        title: item.title,
                        url: item.url ? `/${login}/${slug}/${item.url}` : '',
                        children: tocToArticle(item.children)
                    }));
                };

                return tocToArticle(root);
            }
            return [];
        });
    }

    async prepareContentPage(page: Page, url?: string): Promise<void> {
        if (url) {
            page.goto(url);
        } else {
            await page.goto('about:blank');
        }
    }

    async getContent(page: Page): Promise<string | undefined> {
        const response = await page.waitForResponse(
            response => {
                return response.url().startsWith('https://www.yuque.com/api/docs');
            }
        );
        if (response.status() === 200) {
            const result = await response.json();
            return result.data.content;
        }
    }
}
