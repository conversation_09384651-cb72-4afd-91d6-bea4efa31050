import Configstore from 'configstore';

const configstore = new Configstore('ttx', {
    plugins_host: 'https://plugins.x.topthink.com'
});

interface ConfigType {
    plugins_host: string;
}

export function getConfig(): ConfigType
export function getConfig<Key extends keyof ConfigType>(key: Key): ConfigType[Key]
export function getConfig(key?: string): ConfigType {
    if (key) {
        return configstore.get(key);
    } else {
        return configstore.all;
    }
}

export function setConfig<Key extends keyof ConfigType>(key: Key, value: ConfigType[Key]) {
    configstore.set(key, value);
}
