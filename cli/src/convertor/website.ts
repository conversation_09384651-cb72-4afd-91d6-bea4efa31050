import Convertor from './index';
import puppeteer, { Page } from 'puppeteer-core';
import fs from 'fs-extra';
import { SummaryArticleShape } from '@topwrite/core';
import Docusaurus from '../crawler/docusaurus';
import Crawler, { ArticleData } from '../crawler';
import VitePress from '../crawler/vite-press';
import Docsify from '../crawler/docsify';
import logger from '../logger';
import GitBook from '../crawler/git-book';
import LakeBook from '../crawler/lake-book';
import path from 'path';
import filenamify from 'filenamify';


export default class WebsiteConvertor extends Convertor<{ url: string }> {
    protected current = 1;

    async convert() {
        logger.info('initializing...');

        const browser = await puppeteer.launch({
            headless: true,
            executablePath: process.env['PUPPETEER_EXECUTABLE_PATH'],
            args: ['--no-sandbox'],
            defaultViewport: { width: 1920, height: 1080 }
        });

        try {
            const page = await browser.newPage();
            await page.setRequestInterception(true);
            page.on('request', request => {
                if (request.resourceType() === 'image') {
                    request.abort();
                } else {
                    request.continue();
                }
            });
            page.on('console', msg => logger.debug(msg.text()));

            logger.info('detect articles...');
            await page.goto(new URL(this.source.url).toString());

            const generator = await page.evaluate(() => {
                const element = document.querySelector('meta[name=generator]');
                if (element) {
                    return element.getAttribute('content');
                }
                if ('Docsify' in window) {
                    return 'Docsify';
                }
                if (document.location.host === 'www.yuque.com') {
                    return 'LakeBook';
                }
                return null;
            });

            let crawler: Crawler;
            switch (true) {
                case generator?.startsWith('Docusaurus'):
                    crawler = new Docusaurus();
                    break;
                case generator?.startsWith('VitePress'):
                    crawler = new VitePress();
                    break;
                case generator?.startsWith('Docsify'):
                    crawler = new Docsify();
                    break;
                case generator?.startsWith('GitBook'):
                    crawler = new GitBook();
                    break;
                case generator?.startsWith('LakeBook'):
                    crawler = new LakeBook();
                    break;
                default:
                    throw new Error(`Unsupported website`);
            }

            const articles = await crawler.getArticles(page);

            this.generateFilenames(articles);

            await crawler.prepareContentPage(page);
            const summary = await this.buildFiles(page, articles, crawler);
            await this.downloadImages();
            logger.info('archiving...');
            const output = fs.createWriteStream(this.destFS.resolve('files.tar.gz'));
            await this.contentFS.archive(output);
            await this.destFS.writeFile('summary.json', JSON.stringify(summary));
            logger.info('done.');
        } finally {
            await browser.close();
        }
    }

    protected generateFilenames(articles: ArticleData[]) {
        for (const article of articles) {
            if (article.url && article.url !== '#' && !article.url.startsWith('http')) {
                const origin = filenamify(article.title);

                let filename = origin + '.md';

                let i = 1;
                while (!filename || Object.values(this.filenames).includes(filename)) {
                    filename = origin + '_' + i + '.md';
                    i++;
                }
                article.url = new URL(article.url, this.source.url).toString();
                this.filenames.set(article.url, filename);
            }
            this.generateFilenames(article.children);
        }
    }

    protected async buildFiles(page: Page, articles: ArticleData[], crawler: Crawler) {
        const summaryArticles: SummaryArticleShape[] = [];
        for (const article of articles) {
            const summaryArticle: SummaryArticleShape = {
                title: article.title,
                ref: '',
                children: []
            };

            const filename = this.filenames.get(article.url);
            if (filename) {
                logger.info(`fetch articles (${this.current++}/${this.filenames.size})`);
                await crawler.prepareContentPage(page, article.url);
                const html = await crawler.getContent(page);
                if (html) {
                    const markdown = this.toMarkdown(html, article.url);
                    const ref = path.join(this.dir, filename);
                    await this.contentFS.writeFile(ref, markdown);
                    summaryArticle.ref = ref;
                }
            }

            if (article.children.length > 0) {
                summaryArticle.children = await this.buildFiles(page, article.children, crawler);
            }

            summaryArticles.push(summaryArticle);
        }
        return summaryArticles;
    }

}
