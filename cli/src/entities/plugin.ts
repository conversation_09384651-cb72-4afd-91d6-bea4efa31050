import { HookContext } from '@topwrite/core';

interface Hooks {
    [index: string]: Function;
}

interface PluginOption {
    name: string;
    version: string;
    path: string;
    hooks: Hooks;
    config: object;
}

export default class Plugin {
    name: string;
    version: string;
    path: string;
    hooks: Hooks;
    config: object;

    constructor({ name, version, path, hooks, config }: PluginOption) {
        this.name = name;
        this.version = version;
        this.path = path;
        this.hooks = hooks;
        this.config = config;
    }

    getHook(name: string) {
        return this.hooks[name];
    }

    getContext(context: HookContext) {
        return {
            ...context,
            getConfig: () => {
                return context.book.config.getPluginConfig(this.name, this.config);
            }
        };
    }
}
