import fs, { WriteStream } from 'fs-extra';
import path from 'path';
import spawn from 'cross-spawn';
import Ignore from 'fstream-ignore';
import archiver from 'archiver';
import { pack } from 'tar-pack';

interface Dirent {
    type: 'File' | 'Directory';
    path: string;
}

export default class FS {

    readonly root: string;

    constructor(root: string) {
        this.root = root;
    }

    readFileTime(filename: string = '') {
        const command = `git log --pretty=format:%cI -- ${filename}`;

        const { error, stdout } = spawn.sync(command, {
            stdio: ['inherit'],
            encoding: 'utf8',
            cwd: this.root,
            shell: true
        });

        if (error || !stdout) {
            return [undefined, undefined];
        }

        const times = stdout.split('\n');

        return [times[times.length - 1], times[0]];
    }

    createReadStream(filename: string, options?: Parameters<typeof fs.createReadStream>[1]) {
        return fs.createReadStream(this.resolve(filename), options);
    }

    async readFile(filename: string): Promise<Buffer> {
        return await fs.readFile(this.resolve(filename));
    }

    async readAsObject(filename: string, defaultValue = {}): Promise<any> {
        try {
            return await fs.readJSON(this.resolve(filename));
        } catch {
            return defaultValue;
        }
    }

    async readAsString(filename: string): Promise<string> {
        try {
            return await fs.readFile(this.resolve(filename), 'utf-8');
        } catch {
            return '';
        }
    }

    async writeFile(filename: string, data: any) {
        filename = this.resolve(filename);
        fs.ensureDirSync(path.dirname(filename));
        return fs.writeFile(filename, data);
    }

    createWriteStream(filename: string, options?: Parameters<typeof fs.createWriteStream>[1]) {
        filename = this.resolve(filename);
        fs.ensureDirSync(path.dirname(filename));
        return fs.createWriteStream(filename, options);
    }

    async appendFile(filename: string, data: any) {
        return fs.appendFile(this.resolve(filename), data);
    }

    exist(filename: string) {
        return fs.pathExistsSync(this.resolve(filename));
    }

    async unlink(filename: string) {
        try {
            await fs.unlink(this.resolve(filename));
        } catch {

        }
    }

    async readDir(path: string = ''): Promise<Dirent[]> {

        const files: Dirent[] = [];

        return new Promise((resolve) => {
            Ignore({
                path: this.resolve(path),
                ignoreFiles: ['.gitignore', '.bookignore']
            }).on('child', function(c: any) {
                files.push({
                    type: c.type,
                    path: c.path.substr(c.root.path.length + 1)
                });
            }).on('close', () => {
                resolve(files);
            });
        });
    }

    resolve(...args: string[]) {
        return path.join(this.root, ...args);
    }

    async archive(output: WriteStream, format: 'tar' | 'zip' = 'tar') {
        if (format == 'tar') {
            //archiver的tar打包无法打包中文文件名
            return new Promise<void>((resolve, reject) => {
                pack(this.root, { fromBase: true, ignoreFiles: ['.gitignore', '.bookignore'] })
                .pipe(output)
                .on('error', (err: any) => {
                    reject(err);
                })
                .on('close', () => {
                    resolve();
                });
            });
        } else {
            const files = await this.readDir();

            return new Promise<void>((resolve, reject) => {

                output.on('error', (err: any) => {
                    reject(err);
                });
                output.on('close', () => {
                    resolve();
                });

                const archive = archiver(format, {
                    gzip: true,
                    gzipOptions: { level: 9 },
                    zlib: { level: 9 }
                });

                archive.pipe(output);

                for (const file of files) {
                    if (file.type === 'File') {
                        archive.append(this.createReadStream(file.path), { name: file.path });
                    }
                }

                archive.finalize();
            });
        }
    }

}
