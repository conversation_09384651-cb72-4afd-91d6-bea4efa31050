const webpack             = require("webpack");
const path                = require("path");
const WebpackConfigPlugin = require("@topthink/webpack-config-plugin");

module.exports = (env) => {

    const isDevelopment = !!env.dev;
    const isServer      = !!env.WEBPACK_SERVE;

    const host = env.prod ? "https://sandbox.x.topthink.com" : "http://sandbox.x.topthink.org";

    let id = "sandbox:dev";

    return {
        devtool  : isDevelopment ? "cheap-module-source-map" : "source-map",
        mode     : isDevelopment ? "development" : "production",
        entry    : {
            index: {
                import : "./src/index.ts",
                library: {
                    name: "TopWrite",
                    type: "window"
                }
            }
        },
        output   : {
            filename     : "[name].js",
            chunkFilename: "[id]-[contenthash:6].js",
            path         : path.resolve(__dirname, "dist")
        },
        externals: {
            "react"           : "React",
            "react-dom"       : "ReactDOM",
            "react-dom/client": "ReactDOM",
            "socket.io-client": "io"
        },
        resolve  : {
            fallback: {
                url: require.resolve("url/")
            }
        },
        plugins  : [
            new WebpackConfigPlugin({
                serve: isServer,
                html : isServer ? {
                    chunks            : ["index"],
                    template          : "public/index.ejs",
                    inject            : false,
                    scriptLoading     : "blocking",
                    publicPath        : "/",
                    templateParameters: {
                        id
                    }
                } : false
            }),
            new webpack.ProvidePlugin({
                process: require.resolve("process/browser")
            })
        ],
        devServer: {
            hot         : "only",
            client      : {
                webSocketURL: {
                    hostname: "localhost"
                }
            },
            allowedHosts: "all",
            proxy       : {
                "/socket.io": {
                    target      : host,
                    changeOrigin: true,
                    ws          : true
                },
                "/preview"  : {
                    target      : host,
                    changeOrigin: true
                },
                "/download" : {
                    target      : host,
                    changeOrigin: true
                },
                "/asset"    : {
                    target      : host,
                    changeOrigin: true
                },
                "/assistant": {
                    target      : host,
                    changeOrigin: true
                },
                "/import"   : {
                    target      : host,
                    changeOrigin: true
                }
            },
            compress    : false
        }
    };
};
