import { styled, useActions } from '@topwrite/common';
import { useEffect, useState } from 'react';
import useFormatMessage from '../../lib/use-format-message';
import { socket } from '../../lib/socket';
import CommitDetail from './commit-detail';
import { Commit } from 'repo';
import ScrollList from '../../components/scroll-list';
import { PaneHeader } from '../../components/pane';
import CommitItem from '../../components/commit-item';

export default function History() {
    const t = useFormatMessage();
    const [current, setCurrent] = useState<string>();
    const { setExtra } = useActions('workspace');

    useEffect(() => {
        return () => {
            setExtra(null);
        };
    }, []);

    const handleClick = (commit: Commit) => {
        return () => {
            setCurrent(commit.sha);
            setExtra(<CommitDetail commit={commit} />);
        };
    };

    return <>
        <PaneHeader>{t('active-bar.history')}</PaneHeader>
        <CommitList>
            <ScrollList<Commit>
                fetchData={async (result) => {
                    let moreResult;
                    if (result) {
                        moreResult = await socket.readHistory(null, result.data.length);
                    } else {
                        moreResult = await socket.readHistory();
                    }
                    return {
                        data: moreResult,
                        hasMore: moreResult.length >= 15,
                        page: 1
                    };
                }}
                renderItem={(commit) => {
                    return <CommitItem
                        key={commit.sha}
                        onClick={handleClick(commit)}
                        commit={commit}
                        active={current === commit.sha}
                    />;
                }}
            />
        </CommitList>
    </>;
}

const CommitList = styled.div`
  flex: auto;
  overflow: hidden;
  position: relative;
`;
