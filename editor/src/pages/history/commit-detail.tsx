import { socket } from '../../lib/socket';
import { styled, useAsync } from '@topwrite/common';
import { Commit } from 'repo';
import dayjs from 'dayjs';
import { GoGitCommit } from 'react-icons/go';
import DiffFiles from '../../components/diff-files';
import Loader from '../../components/loader';

interface CommitProps {
    commit: Commit;
}

export default function CommitDetail({ commit }: CommitProps) {

    const { result } = useAsync(() => socket.readCommitDiff(commit.sha), [commit.sha]);

    const date = dayjs(commit.author.date);

    let body = <Loader />;
    if (result) {
        const loadChanges = async (filename: string) => {
            const files = await socket.readCommitDiff(commit.sha, filename);

            return files[0].changes;
        };
        body = <DiffFiles loadChanges={loadChanges} files={result} />;
    }

    return <Container>
        <CommitHeader>
            <CommitMessage>
                <GoGitCommit className='me-1' />
                {commit.message}
            </CommitMessage>
            <CommitMeta>
                <span className='me-1' title={date.format()}>
                    {date.fromNow()}
                </span>
                by {commit.author.name}
            </CommitMeta>
        </CommitHeader>
        {body}
    </Container>;
}

const CommitMeta = styled.p`
    margin: 0;
    color: var(--ttw-secondary-color)
`;

const CommitMessage = styled.p`
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 15px;
    line-height: 1.5;
    color: var(--ttw-color);
`;

const CommitHeader = styled.div`
    padding: 10px;
    background-color: var(--ttw-foreground);
    border-bottom: 1px solid var(--ttw-border-color);
    position: relative;
`;

const Container = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
`;
