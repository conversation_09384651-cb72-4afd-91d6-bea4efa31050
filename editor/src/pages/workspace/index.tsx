import { styled, useActions, useAsync, useSelector } from '@topwrite/common';
import { Buffer } from 'buffer';
import { produce } from 'immer';
import { FC, memo, useCallback, useEffect, useRef, useState } from 'react';
import { Button } from 'react-bootstrap';
import { HiBan } from 'react-icons/hi';
import { Editor as SlateEditor } from 'slate';
import { ReactEditor } from 'slate-react';
import Editor from '../../components/editor';
import FileProvider, { useFile } from '../../components/file-provider';
import Loader from '../../components/loader';
import { ImageCacheProvider } from '../../components/local-image';
import File from '../../entities/file';
import { socket } from '../../lib/socket';
import useFormatMessage from '../../lib/use-format-message';
import ImagePreview from './image-preview';

const Content: FC<{ lineNumber: number, onChange: (buf: Buffer) => void }> = function({ lineNumber, onChange }) {
    const t = useFormatMessage();
    const file = useFile();
    const [force, setForce] = useState(new Set());
    const [editor, setEditor] = useState<SlateEditor | null>(null);
    const jumped = useRef(false);

    useEffect(() => {
        if (editor && lineNumber > 0 && !jumped.current) {
            jumped.current = true;

            //TODO 优化 编辑器加载完成后，再跳转到指定行
            const root = ReactEditor.toDOMNode(editor, editor);

            const observer = new ResizeObserver(() => {
                editor.gotoLine(lineNumber);
            });

            observer.observe(root);

            setTimeout(() => {
                observer.disconnect();
            }, 2000);
        }
    }, [editor]);

    const mimeType = force.has(file.path) ? 'text/plain' : file.mime;

    if (mimeType.startsWith('text/') || mimeType === 'application/json') {
        return <Editor ref={setEditor} onChange={onChange} file={file} />;
    } else if (mimeType.startsWith('image/')) {
        return <ImagePreview file={file} />;
    }

    return <div className='text-center text-secondary'>
        <p><HiBan size={70} /></p>
        <p>{t('editor.workspace.binary.message')}</p>
        <p>
            <Button
                size='sm'
                variant='link'
                onClick={() => setForce(new Set(force.add(file.path)))}
            >{t('editor.workspace.binary.confirm')}</Button>
        </p>
    </div>;
};

const Workspace = memo(() => {

    const { summary } = useSelector('book');
    const { current, lineNumber } = useSelector('workspace');
    const { setCurrent } = useActions('workspace');
    const lock = useRef(false);

    const { result: file, loading, merge } = useAsync(async () => {
            if (current) {
                return await File.createFromSocket(current);
            }
            return undefined;
        },
        [current],
        {
            setLoading({ result }) {
                return {
                    status: 'loading',
                    loading: true,
                    result,
                    error: undefined
                };
            }
        }
    );

    useEffect(() => {
        if (!current) {
            const first = summary.getFirstArticle();
            if (first) {
                setCurrent(first.ref);
            }
        }
    }, []);

    useEffect(() => {
        const listener = async (names: string[]) => {
            if (current && names.includes(current)) {
                if (!lock.current) {
                    const file = await File.createFromSocket(current);
                    merge({
                        result: file
                    });
                }
                lock.current = false;
            }
        };

        socket.on('file.change', listener);

        return () => {
            socket.off('file.change', listener);
        };
    }, [current, merge]);

    const handleChange = useCallback((buf: Buffer) => {
        if (file) {
            lock.current = true;
            socket.writeFileAsync(file.path, buf);

            //TODO 优化 减少编辑文档时的更新
            merge({
                result: produce(file, draft => {
                    draft.content = buf;
                })
            });
        }
    }, [file?.ctime, merge]);

    return <Container>
        <ImageCacheProvider>
            <FileProvider file={file}>
                <Content lineNumber={lineNumber} onChange={handleChange} />
            </FileProvider>
        </ImageCacheProvider>
        <Loader loading={loading} />
    </Container>;
});

export default Workspace;

const Container = styled.div`
    display: flex;
    flex-direction: column;
    position: absolute;
    justify-content: center;
    top: 0;
    left: 1px;
    bottom: 0;
    right: 0;
`;
