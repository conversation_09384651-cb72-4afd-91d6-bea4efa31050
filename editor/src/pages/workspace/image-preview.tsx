import { useEffect, useMemo } from 'react';
import File from '../../entities/file';
import createObjectUrl from '../../lib/create-object-url';
import { styled } from '@topwrite/common';

export default function ImagePreview({ file }: { file: File }) {

    const src = useMemo(() => {
        return createObjectUrl(file);
    }, [file]);

    useEffect(() => {
        return () => {
            if (src) {
                window.URL.revokeObjectURL(src);
            }
        };
    }, [file]);

    return <Container>
        <img src={src} alt='' />
    </Container>;
}

const Container = styled.div`
    flex: 1;
    background: var(--ttw-foreground);
    display: flex;
    justify-content: center;
    overflow: hidden;

    img {
        background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAIElEQVQYV2NkYGAwZsAEZ9GFGIeIQix+wfQgyDODXSEAcUwGCqymDnYAAAAASUVORK5CYII=);
        max-height: 95%;
        max-width: 95%;
        align-self: center;
    }
`;
