import { Spinner } from 'react-bootstrap';
import { AiFillCheckCircle, AiFillCloseCircle, AiFillMinusCircle } from 'react-icons/ai';

interface StatusProps {
    status?: 'pending' | 'failed' | 'running' | 'succeed' | 'unknown' | string;
    size?: 'sm';
}

export default function Status({ status = 'unknown', size }: StatusProps) {

    switch (status) {
        case 'pending':
            return <Spinner animation='grow' variant='warning' size={size} />;
        case 'running':
            return <Spinner animation='grow' variant='info' size={size} />;
        case 'succeed':
            return <AiFillCheckCircle
                size={size === 'sm' ? 16 : 32}
                color='var(--bs-success)'
            />;
        case 'unknown':
            return <AiFillMinusCircle
                size={size === 'sm' ? 16 : 32}
                color='var(--bs-secondary)'
            />;
        case 'failed':
            return <AiFillCloseCircle size={size === 'sm' ? 16 : 32} color='var(--bs-danger)' />;
        default:
            return null;
    }

}
