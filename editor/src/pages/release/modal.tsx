import { useRef, useState } from 'react';
import useFormatMessage from '../../lib/use-format-message';
import Form, { FormType } from '../../components/form';
import { styled, useAsyncCallback } from '@topwrite/common';
import { socket } from '../../lib/socket';
import Button from '../../components/button';
import { ISubmitEvent } from '@rjsf/core';
import DiffFiles from '../../components/diff-files';
import Dimmer from '../../components/dimmer';
import { GoPlus, GoStop } from 'react-icons/go';
import Loader from '../../components/loader';
import useOverlayState from '../../lib/use-overlay-state';
import Modal from '../../components/modal';
import Tooltip from '../../components/tooltip';

interface ReleaseModalProps {
    onSuccess: () => void;
}

export default function ReleaseModal({ onSuccess }: ReleaseModalProps) {

    const t = useFormatMessage();
    const formRef = useRef<FormType>(null);
    const [error, setError] = useState<Error>();

    const { result, execute: readStatus } = useAsyncCallback(async () => {
        try {
            return await socket.getReleaseStatus();
        } catch (e: any) {
            setError(e);
        }
    });

    const { show, state } = useOverlayState({
        onShow: async () => {
            await readStatus();
        }
    });

    const handleSubmit = async ({ formData }: ISubmitEvent<{ message: string }>) => {
        await socket.saveRelease(formData);
        onSuccess();
    };

    let body = <Loader />;
    if (error) {
        body = <Dimmer>
            <GoStop className='text-secondary' size={40} />
            <h4 className='mt-3 text-secondary'>{error.message}</h4>
        </Dimmer>;
    } else if (result) {
        const loadChanges = async (filename: string) => {
            const files = await socket.readDiff(result!.sha, filename);

            return files[0].changes;
        };
        body = <DiffFiles loadChanges={loadChanges} files={result.files} type='unified' />;
    }

    return <>
        <Tooltip tooltip={t('release.new')}>
            <Button variant='light' onClick={show}><GoPlus /></Button>
        </Tooltip>
        <Modal
            {...state}
            title={t('release.new')}
            size='lg'
            okButtonProps={{
                disabled: !result
            }}
            onOk={async () => {
                formRef.current && await formRef.current.submit();
            }}
        >
            <Container>
                {body}
                <FormContainer>
                    <Form
                        onSubmit={handleSubmit}
                        ref={formRef}
                        schema={{
                            type: 'object',
                            properties: {
                                message: {
                                    title: t('release.message'),
                                    type: 'string',
                                },
                            }
                        }}
                        uiSchema={{
                            message: {
                                'ui:widget': 'textarea',
                                'ui:options': {
                                    'rows': 3
                                },
                                'ui:autofocus': true
                            }
                        }}>
                    </Form>
                </FormContainer>
            </Container>
        </Modal>
    </>;

};

const FormContainer = styled.div`
  padding: 1rem;
  border-top: 1px solid #dee2e6;
`;

const Container = styled.div`
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 80vh;
  margin: -1rem;
`;
