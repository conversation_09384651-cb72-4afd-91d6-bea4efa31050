import { Release, ReleaseLog } from 'repo';
import useFormatMessage from '../../lib/use-format-message';
import { styled, useAsyncCallback, useSelector } from '@topwrite/common';
import { socket } from '../../lib/socket';
import { useEffect, useRef } from 'react';
import dayjs from 'dayjs';
import Button from '../../components/button';

interface LogProps {
    log: ReleaseLog;
    release: Release;
    clearTrace: Function;
}

export default function Log({ log, release, clearTrace }: LogProps) {
    const t = useFormatMessage();
    const { download, release: canRelease } = useSelector('options');

    const { execute, loading } = useAsyncCallback(() => {
        clearTrace();
        return socket.retryRelease(log.release_id, log.type);
    });

    const ref = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (ref.current) {
            ref.current.scrollTop = ref.current.scrollHeight;
        }
    }, [log.trace]);

    return <Container>
        <Header>
            <Col>
                <div>Status</div>
                <div>{t(`release.${log.status_text}`)}</div>
            </Col>
            <Col>
                <div>Total duration</div>
                <div>
                    {log.start_time && log.end_time ? dayjs.duration(dayjs(log.end_time)
                    .diff(dayjs(log.start_time))).format('m[m] s[s]') : '-'}
                </div>
            </Col>
            <Col>
                <div>Timeout</div>
                <div>{dayjs.duration(release.timeout * 1000).format('m[m] s[s]')}</div>
            </Col>
            <Col>
                <div>Artifacts</div>
                <div>
                    {release.is_latest && log.status === 1 ?
                        <a href={`${download}?type=${log.type}&sha=${release.sha}`} target='_blank'>{t('release.download')}</a> : '--'}
                </div>
            </Col>
            {release.is_latest && canRelease && <Actions>
                {log.status === 1 && <Button variant='outline-success' onClick={execute} loading={loading}>
                    {t('release.retry')}
                </Button>}
                {log.status === -1 && <Button variant='outline-secondary' onClick={execute} loading={loading}>
                    {t('release.retry')}
                </Button>}
                {log.status === -2 && <Button variant='outline-info' onClick={execute} loading={loading}>
                    {t('release.pack')}
                </Button>}
            </Actions>}
        </Header>
        <Trace ref={ref}>
            <pre>{log.trace}</pre>
        </Trace>
    </Container>;
}

const Trace = styled.div`
    flex: auto;
    overflow-y: auto;

    pre {
        font-family: Menlo, Monaco, Consolas, courier new, monospace;
        display: block;
        padding: 9.5px;
        margin: 0;
        font-size: 13px;
        word-break: break-word;
        white-space: pre-wrap;
        background: #1E1F22;
        color: #fff;
        min-height: 100%;
    }
`;

const Actions = styled.div`
    align-self: center;
    flex: auto;
    text-align: right;
`;

const Col = styled.div`
    margin-right: 40px;
    margin-left: 16px;

    > :first-child {
        color: var(--ttw-gray-color);
        margin-bottom: 4px;
    }

    a {
        text-decoration: none;
    }
`;

const Header = styled.div`
    background: var(--ttw-foreground);
    border-bottom: 1px solid var(--ttw-border-color);
    display: flex;
    padding: 16px;
`;

const Container = styled.div`
    flex: auto;
    display: flex;
    flex-direction: column;
    min-width: 0;
`;
