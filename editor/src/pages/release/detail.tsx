import { Release, ReleaseLog } from 'repo';
import { Tab } from 'react-bootstrap';
import { styled, useAsync } from '@topwrite/common';
import { socket } from '../../lib/socket';
import Loader from '../../components/loader';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { keyBy } from 'lodash';
import Log from './log';
import Sidebar from './sidebar';

interface LogsProps {
    release: Release;
}

function Logs({ release }: LogsProps) {

    const [logs, setLogs] = useState(() => keyBy(release.logs, 'type'));

    const [type, setType] = useState<string>(release.main);

    useEffect(() => {
        const updateListener = (type: string, log: ReleaseLog) => {
            setLogs(logs => ({
                ...logs,
                [type]: {
                    ...logs[type],
                    ...log
                }
            }));
        };

        const updateEvent = `release.${release.id}.update`;
        socket.on(updateEvent, updateListener);

        const traceListener = (type: string, trace: string) => {
            setLogs(logs => ({
                ...logs,
                [type]: {
                    ...logs[type],
                    trace: logs[type].trace + trace
                }
            }));
        };

        const traceEvent = `release.${release.id}.trace`;
        socket.on(traceEvent, traceListener);

        return () => {
            socket.off(updateEvent, updateListener);
            socket.off(traceEvent, traceListener);
        };
    }, [release]);

    const clearTrace = useCallback(() => {
        setLogs(logs => ({
            ...logs,
            [type]: {
                ...logs[type],
                trace: ''
            }
        }));
    }, [type]);

    const log = useMemo(() => {
        return logs[type] ?? {
            release_id: release.id,
            type,
            status: -2,
            status_text: 'unknown',
            trace: '',
        };
    }, [type, logs, release]);

    return <Tab.Container id='release-detail' activeKey={type} onSelect={(eventKey) => {
        if (eventKey) {
            setType(eventKey);
        }
    }}>
        <Container>
            <Log log={log} clearTrace={clearTrace} release={release} />
            <Sidebar logs={logs} release={release} />
        </Container>
    </Tab.Container>;
}

const Container = styled.div`
  display: flex;
  height: 100%;
`;

interface DetailProps {
    id: number;
}

export default function Detail({ id }: DetailProps) {
    const { result: release, loading } = useAsync(() => socket.getReleaseDetail(id), [id]);

    if (!release) {
        return <Loader loading={loading} />;
    }

    return <Logs release={release} />;
}
