import { styled } from '@topwrite/common';
import { GoGitCommit } from 'react-icons/go';
import Status from './status';
import { Nav } from 'react-bootstrap';
import { Release, ReleaseLog } from 'repo';
import dayjs from 'dayjs';
import { useMemo } from 'react';

interface Props {
    release: Release;
    logs: {
        [index: string]: ReleaseLog | undefined
    };
}

const TYPES = {
    html: 'HTML',
    pdf: 'PDF',
    epub: 'Epub',
    word: 'Word',
    json: 'Raw'
};

export default function Sidebar({ release, logs }: Props) {

    const date = dayjs(release.create_time);

    const items = useMemo(() => {
        return Object.entries(TYPES).sort(([k1], [k2]) => {
            if (k1 === release.main) {
                return -1;
            }
            if (k2 === release.main) {
                return 1;
            }
            if (!logs[k1] === !logs[k2]) {
                return 0;
            }
            return logs[k1] ? -1 : 1;
        });
    }, [logs]);

    return <Container>
        <Meta>
            <Sha>
                <GoGitCommit className='me-1' />
                {release.sha.substr(0, 7)}
            </Sha>
            <Author>
                <span className='me-1' title={date.format()}>
                    {date.fromNow()}
                </span>
                by {release.user.name}
            </Author>
        </Meta>
        <Header>
            <Status status={logs[release.main]?.status_text ?? 'unknown'} />
            <Message>
                {release.message || 'No Message'}
            </Message>
        </Header>
        <Nav variant='pills' className='flex-column'>
            {items.map(([k, v]) => {
                return <Nav.Item key={k}>
                    <Nav.Link eventKey={k}>
                        <Status status={logs[k]?.status_text} size='sm' />{v}
                    </Nav.Link>
                </Nav.Item>;
            })}
        </Nav>
    </Container>;
}

const Author = styled.div`

`;

const Sha = styled.div`
    flex: auto;
    display: flex;
    align-items: center;
`;

const Meta = styled.div`
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    align-items: center;
    height: 20px;
    margin-bottom: 8px;
    color: var(--ttw-secondary-color);
    padding: 8px;
`;

const Header = styled.div`
    padding: 8px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
`;

const Message = styled.div`
    margin-left: 14px;
    flex: auto;
    white-space: pre;
`;

const Container = styled.div`
    width: 290px;
    background: var(--ttw-foreground);
    padding: 12px 8px;
    border-left: 1px solid var(--ttw-border-color);
    flex-shrink: 0;

    .nav-link {
        color: var(--ttw-color);
        align-items: center;
        display: flex;

        .bi, .spinner-grow {
            margin-right: 1rem;
        }

        &.active {
            color: var(--ttw-color);
            background-color: var(--ttw-file-active-background);
        }
    }
`;
