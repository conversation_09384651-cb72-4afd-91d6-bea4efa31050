import { IChangeEvent } from '@rjsf/core';
import { Schema } from '@topthink/json-form';
import { Config, styled, useActions, useSelector } from '@topwrite/common';
import { produce } from 'immer';
import { JSONSchema7 } from 'json-schema';
import { cloneDeep, isEqual, set } from 'lodash';
import { useState } from 'react';
import { Nav, NavItem, NavLink, TabContainer, TabContent, TabPane } from 'react-bootstrap';
import Button from '../components/button';
import Form from '../components/form';
import Modal, { ModalProps } from '../components/modal';
import useFormatMessage from '../lib/use-format-message';

interface ConfigProps {
    config: Config;
    updateConfig: (updater: (config: Config) => void) => void;
}

function BaseConfig({ config, updateConfig }: ConfigProps) {

    const handler = ({ formData, errors }: IChangeEvent) => {
        if (errors.length === 0) {
            updateConfig((config) => {
                const language = formData.language == 'zh-CN' ? undefined : formData.language;

                config.setValue('language', language);
            });
        }
    };

    const t = useFormatMessage();

    return <Form
        formData={{
            language: config.getValue('language', 'zh-CN')
        }}
        onChange={handler}
        schema={{
            type: 'object',
            properties: {
                language: {
                    title: t('active-bar.settings.config.base.language'),
                    type: 'string',
                    enum: ['zh-CN', 'en'],
                    enumNames: [t('language.zh_cn'), t('language.en')],
                    default: 'zh-CN'
                },
            }
        }}
    />;
}

function ReleaseConfig({ config, updateConfig }: ConfigProps) {
    const handler = ({ formData, errors }: IChangeEvent) => {
        if (errors.length === 0) {
            updateConfig((config) => {
                config.setValue('release', formData);
            });
        }
    };

    const t = useFormatMessage();

    const schema = cloneDeep(Config.schema.properties!.release) as Schema;

    set(schema, 'properties.pathEncode.title', t('active-bar.settings.config.release.path_encode'));

    return <Form
        formData={config.getValue('release')}
        onChange={handler}
        schema={schema}
    />;
}

function PdfConfig({ config, updateConfig }: ConfigProps) {

    const handler = ({ formData, errors }: IChangeEvent) => {
        if (errors.length === 0) {
            updateConfig((config) => {
                config.setValue('pdf', formData);
            });
        }
    };

    const t = useFormatMessage();

    const schema: JSONSchema7 = cloneDeep(Config.schema.properties!.pdf) as Schema;

    set(schema, 'title', t('active-bar.settings.config.pdf'));
    set(schema, 'properties.fontFamily.title', t('active-bar.settings.config.pdf.font_family'));
    set(schema, 'properties.margin.title', t('active-bar.settings.config.pdf.margin'));
    set(schema, 'properties.margin.properties.right.title', t('active-bar.settings.config.pdf.margin.right'));
    set(schema, 'properties.margin.properties.left.title', t('active-bar.settings.config.pdf.margin.left'));
    set(schema, 'properties.margin.properties.top.title', t('active-bar.settings.config.pdf.margin.top'));
    set(schema, 'properties.margin.properties.bottom.title', t('active-bar.settings.config.pdf.margin.bottom'));
    set(schema, 'properties.paperSize.title', t('active-bar.settings.config.pdf.paper_size'));
    set(schema, 'properties.chapterMark.title', t('active-bar.settings.config.pdf.chapter_mark'));
    set(schema, 'properties.pageBreaksBefore.title', t('active-bar.settings.config.pdf.page_breaks_before'));
    set(schema, 'properties.pageNumbers.title', t('active-bar.settings.config.pdf.page_numbers'));

    return <Form
        formData={config.getValue('pdf')}
        onChange={handler}
        schema={schema}
    />;
}

function StructureConfig({ config, updateConfig }: ConfigProps) {
    const t = useFormatMessage();

    const schema: Schema = {
        type: 'object',
        properties: {
            root: cloneDeep(Config.schema.properties!.root),
        }
    };

    set(schema, 'properties.root.title', t('active-bar.settings.config.structure.root'));

    const handler = ({ formData, errors }: IChangeEvent) => {
        if (errors.length === 0) {
            updateConfig((config) => {
                config.setValue('root', formData.root);
            });
        }
    };

    const uiSchema = {
        root: {
            'ui:placeholder': t('active-bar.settings.config.structure.root.placeholder'),
            'ui:description': t('active-bar.settings.config.structure.root.description')
        }
    };

    const formData = {
        root: config.getValue('root')
    };

    return <Form
        formData={formData}
        schema={schema}
        uiSchema={uiSchema}
        onChange={handler}
    />;
}

export function ConfigModal(props: Partial<ModalProps>) {
    const t = useFormatMessage();

    const { config } = useSelector('book');
    const { updateConfig } = useActions('book');

    const [data, setData] = useState(config);

    const updateData = (updater: (config: Config) => void) => {
        setData(produce(updater));
    };

    const apply = () => {
        updateConfig((config) => {
            config.setValues(data.getValues());
        });
    };

    return <Modal
        {...props}
        title={t('active-bar.settings.config')}
        size='xl'
        scrollable
        onOk={apply}
        footer={({ okButton, cancelButton }) => {
            return <>
                {cancelButton}
                <Button
                    variant='secondary'
                    onClick={apply}
                    disabled={isEqual(data.getValues(), config.getValues())}>{t('modal.apply')}</Button>
                {okButton}
            </>;
        }}
        bodyAs={ConfigBody}
    >
        <TabContainer id='config' defaultActiveKey='base'>
            <Nav>
                <NavItem>
                    <NavLink eventKey='base'>{t('active-bar.settings.config.base')}</NavLink>
                </NavItem>
                <NavItem>
                    <NavLink eventKey='release'>{t('active-bar.settings.config.release')}</NavLink>
                </NavItem>
                <NavItem>
                    <NavLink eventKey='pdf'>{t('active-bar.settings.config.pdf')}</NavLink>
                </NavItem>
                <NavItem>
                    <NavLink eventKey='structure'>
                        {t('active-bar.settings.config.structure')}
                    </NavLink>
                </NavItem>
            </Nav>
            <TabContent>
                <TabPane eventKey='base'>
                    <BaseConfig updateConfig={updateData} config={data} />
                </TabPane>
                <TabPane eventKey='release'>
                    <ReleaseConfig updateConfig={updateData} config={data} />
                </TabPane>
                <TabPane eventKey='pdf'>
                    <PdfConfig updateConfig={updateData} config={data} />
                </TabPane>
                <TabPane eventKey='structure'>
                    <StructureConfig updateConfig={updateData} config={data} />
                </TabPane>
            </TabContent>
        </TabContainer>
    </Modal>;
}

const ConfigBody = styled.div`
    display: flex;
    flex-direction: row;
    padding: 0;
    overflow: hidden;
    height: 70vh;

    .nav {
        flex-shrink: 0;
        width: 200px;
        flex-direction: column;
        margin: 0.5rem 0;

        .nav-item {
            .nav-link {
                color: var(--ttw-gray-color);

                &.active {
                    font-weight: 700;
                    color: var(--ttw-color);
                }
            }
        }
    }

    .tab-content {
        flex: auto;
        overflow-y: auto;
        padding: 15px;
    }
`;
