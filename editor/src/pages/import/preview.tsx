import { ReactNode, useCallback, useEffect, useState } from 'react';
import { request, SummaryArticleShape, useOptions } from '@topwrite/common';
import Ebook from './preview/ebook';
import Button from '../../components/button';
import useFormatMessage from '../../lib/use-format-message';
import Error from './error';
import { socket } from '../../lib/socket';
import Loader from '../../components/loader';
import Website from './preview/website';
import Toast from '../../components/toast';
import { FormCheck, FormText } from 'react-bootstrap';

export type ImportData = {
    id: string;
    type: 'ebook';
    source: {
        path: string;
        name: string;
        icon: ReactNode;
    }
} | {
    id: string;
    type: 'website';
};

interface Props {
    data: ImportData;
    onSuccess: (data: SummaryArticleShape[]) => void;
}

interface TraceEvent {
    type: 'trace';
    message: string;
}

interface ErrorEvent {
    type: 'error';
    error: string;
}

interface ResultEvent {
    type: 'result';
    result: SummaryArticleShape[];
}

export default function Preview({ data, onSuccess }: Props) {
    const { import: url } = useOptions();
    const t = useFormatMessage();
    const [loading, setLoading] = useState(false);
    const [source, setSource] = useState<object>({});
    const [independent, setIndependent] = useState(true);
    const [error, setError] = useState<string>();
    const [message, setMessage] = useState<string>();

    useEffect(() => {
        const event = `import.${data.id}`;
        const listener = (data: TraceEvent | ErrorEvent | ResultEvent) => {
            switch (data.type) {
                case 'error':
                    setError(data.error);
                    break;
                case 'result':
                    onSuccess(data.result);
                    break;
                case 'trace':
                    setMessage(data.message.trim().split('\n').at(-1));
                    break;
            }
        };

        socket.on(event, listener);
        return () => {
            socket.off(event, listener);
        };
    }, [data, onSuccess, setMessage, setError]);

    const onClick = useCallback(async () => {
        setLoading(true);
        setMessage(undefined);
        try {
            await request({
                url: `${url}/${data.id}`,
                method: 'POST',
                data: {
                    type: data.type,
                    independent,
                    source
                },
            });
        } catch (e: any) {
            Toast.error(e.message);
            setLoading(false);
        }
    }, [data, independent, source]);

    if (error) {
        return <Error error={error} />;
    }

    return <>
        <Loader loading={loading}>{message}</Loader>
        {data.type === 'ebook' && <Ebook {...data.source} onChange={setSource} />}
        {data.type === 'website' && <Website onChange={setSource} />}
        {data.type === 'website' && <div className={'mb-3'}>
            <FormCheck
                type='switch'
                id='import-independent'
                label={t('catalog.import_independent')}
                checked={independent}
                onChange={(e) => setIndependent(e.target.checked)}
            />
            <FormText>{t('catalog.import_independent.message')}</FormText>
        </div>}
        <Button className={'mb-2'} onClick={onClick}>{t('catalog.import_start')}</Button>
    </>;
}
