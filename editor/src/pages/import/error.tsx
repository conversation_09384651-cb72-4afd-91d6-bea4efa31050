import { BsExclamationCircle } from 'react-icons/bs';
import { Item } from './item';
import useFormatMessage from '../../lib/use-format-message';

interface Props {
    error: string;
}

export default function Error({ error }: Props) {
    const t = useFormatMessage();
    return <Item className={'w-100 mb-3'}>
        <BsExclamationCircle size={64} className={'text-danger'} />
        <h6>{t('catalog.import_failed')}</h6>
        <p className={'text-break'}>{error}</p>
    </Item>;
}
