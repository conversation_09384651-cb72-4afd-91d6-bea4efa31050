import { styled } from '@topwrite/common';
import { ChangeEvent, MouseEvent, ReactNode, useCallback, useRef } from 'react';

export const FileItem = ({ title, ext, icon, onChange }: {
    title: string,
    ext: string,
    icon: ReactNode,
    onChange: (file: File) => void
}) => {
    const ref = useRef<HTMLInputElement>(null);
    const onFileChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            onChange(file);
        }
    }, [onChange]);

    const onClick = useCallback((e: MouseEvent<HTMLDivElement>) => {
        e.preventDefault();
        if (ref.current) {
            ref.current.value = '';
            requestAnimationFrame(() => ref.current?.click());
        }
    }, []);

    return <>
        <input accept={ext} onChange={onFileChange} ref={ref} type='file' hidden />
        <Item onClick={onClick}>
            {icon}
            <h6>{title}</h6>
            <p>{ext}</p>
        </Item>
    </>;
};

export const Item = styled.div`
    width: 25%;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;

    svg {
        padding: .5rem;
        color: var(--bs-primary);
    }

    h6 {
        margin-top: 4px;
        margin-bottom: 0;
        font-weight: normal;
        line-height: 24px;
    }

    p {
        margin-bottom: 0;
        font-size: 12px;
        color: var(--bs-gray-600);
    }
`;
