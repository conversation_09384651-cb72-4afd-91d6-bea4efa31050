import useFormatMessage from '../../lib/use-format-message';
import { useCallback, useState } from 'react';
import Modal, { MessageProps } from '../../components/modal';
import { styled, SummaryArticleShape, useOptions } from '@topwrite/common';
import { FileItem, Item } from './item';
import { ReactComponent as WordIcon } from '../../images/ebook/word.svg';
import { ReactComponent as EpubIcon } from '../../images/ebook/epub.svg';
import { ReactComponent as WebsiteIcon } from '../../images/ebook/website.svg';
import uploadFile from '../../lib/upload-file';
import { customAlphabet } from 'nanoid';
import Loader from '../../components/loader';
import Button from '../../components/button';
import Preview, { ImportData } from './preview';

const nanoid = customAlphabet('1234567890abcdef', 10);

const EBooks = {
    word: {
        title: 'Word',
        ext: '.docx',
        icon: <WordIcon />
    },
    epub: {
        title: 'Epub',
        ext: '.epub',
        icon: <EpubIcon />
    },
};

export default function ImportModal({ state, resolve }: MessageProps<void, SummaryArticleShape[]>) {
    const t = useFormatMessage();
    const [current, setCurrent] = useState<ImportData>();
    const [loading, setLoading] = useState(false);
    const { import: url } = useOptions();

    const onFileChange = useCallback(function(name: string) {
        const ebook = EBooks[name];

        return async (file: File) => {
            setLoading(true);
            try {
                const id = nanoid();
                await uploadFile(`${url}/file`, file, {
                    metadata: { id, ext: ebook.ext, }
                });

                setCurrent({
                    id,
                    type: 'ebook',
                    source: {
                        path: `${id}${ebook.ext}`,
                        name: file.name,
                        icon: ebook.icon
                    }
                });
            } finally {
                setLoading(false);
            }
        };
    }, []);

    return <Modal
        {...state}
        backdrop={'static'}
        title={t('catalog.import_article')}
        footer={null}
    >
        {current ? <PreviewContainer>
            <Preview data={current} onSuccess={resolve} />
            <Button variant={'secondary'} onClick={() => {
                setCurrent(undefined);
            }}>{t('catalog.import_cancel')}</Button>
        </PreviewContainer> : <>
            <Info>{t('catalog.import_desc')}</Info>
            <FileTypeSelector>
                <Loader loading={loading} />
                {Object.entries(EBooks).map(([name, { title, ext, icon }]) => {
                    return <FileItem key={name} title={title} ext={ext} icon={icon} onChange={onFileChange(name)} />;
                })}
                <Item onClick={() => {
                    setCurrent({ id: nanoid(), type: 'website' });
                }}>
                    <WebsiteIcon />
                    <h6>{t('catalog.import_website')}</h6>
                    <p>{t('catalog.import_website_series')}</p>
                </Item>
            </FileTypeSelector>
        </>}
    </Modal>;
}

const PreviewContainer = styled.div`
    display: flex;
    justify-content: center;
    flex-direction: column;
    padding: 1rem 2rem;
`;

const FileTypeSelector = styled.div`
    display: flex;
`;

const Info = styled.div`
    margin-bottom: 1rem;
    color: rgb(var(--bs-secondary-rgb));
`;
