import { Item } from '../item';
import { Form } from 'react-bootstrap';
import { ReactNode, useEffect, useState } from 'react';
import useFormatMessage from '../../../lib/use-format-message';

interface Props {
    icon: ReactNode;
    name: string;
    path: string;
    onChange: (source: object) => void;
}

export default function Ebook({ icon, name, path, onChange }: Props) {
    const t = useFormatMessage();
    const [single, setSingle] = useState(false);

    useEffect(() => {
        onChange({
            path,
            single
        });
    }, [single]);

    return <>
        <Item className={'w-100 mb-2'}>
            {icon}
            <h6>{name}</h6>
        </Item>
        <Form.Check checked={single} onChange={(e) => {
            setSingle(e.target.checked);
        }} className='mb-3' type='switch' label={t('catalog.import_as_single')} />
    </>;
}
