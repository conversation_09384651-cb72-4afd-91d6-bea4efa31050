import { FormControl, FormText } from 'react-bootstrap';
import useFormatMessage from '../../../lib/use-format-message';

interface Props {
    onChange: (source: object) => void;
}

export default function Website({ onChange }: Props) {
    const t = useFormatMessage();
    return <div className={'mb-3'}>
        <FormControl placeholder={t('catalog.import_website.placeholder')} onChange={(e) => onChange({ url: e.target.value })} />
        <FormText>{t('catalog.import_website.message')}</FormText>
    </div>;
}
