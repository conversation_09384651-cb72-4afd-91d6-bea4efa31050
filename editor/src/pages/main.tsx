import StatusBar from './status-bar';
import Body from './body';
import { styled, useSelector } from '@topwrite/common';
import { lazy, Suspense } from 'react';
import Loader from '../components/loader';

const MergeTool = lazy(() => import('./merge-tool'));

export default function Main() {

    const { status: { unmerged } } = useSelector('workspace');

    const children = unmerged ? <Suspense fallback={<Loader />}>
        <MergeTool />
    </Suspense> : <>
        <Body />
        <StatusBar />
    </>;

    return <Container>
        {children}
    </Container>;
};

const Container = styled.div`
    width: 100%;
    height: 100vh;
    background: var(--ttw-background);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
`;
