import {
    InjectedComponentSet,
    styled,
    useLocalStorageState,
    useModel,
    useSelector,
    useThemeContext
} from '@topwrite/common';
import { ReactNode, useCallback, useEffect, useState } from 'react';
import { BsCircleHalf, <PERSON>sMoon, BsSun } from 'react-icons/bs';
import { VscCircleFilled, VscPreview, VscSourceControl, VscSync } from 'react-icons/vsc';
import Toast from '../components/toast';
import Tooltip from '../components/tooltip';
import { ReactComponent as Feedback } from '../images/feedback.svg';
import { socket } from '../lib/socket';

export interface MenuItemProps {
    tooltip?: string;
    children: ReactNode;
    onClick?: string | (() => void);
}

const MenuItem = ({ tooltip, children, onClick, ...props }: MenuItemProps) => {
    if (typeof onClick === 'string') {
        const href = onClick;
        onClick = () => {
            window.open(href);
        };
    }
    if (!tooltip) {
        return <Item onClick={onClick} {...props}>{children}</Item>;
    }

    return <Tooltip placement='top' tooltip={tooltip}>
        <Item onClick={onClick} {...props}>{children}</Item>
    </Tooltip>;
};

export default function StatusBar() {

    const [{ status: { branch }, syncing }, { sync }] = useModel('workspace');
    const { preview, feedback, assistant } = useSelector('options');
    const [message, setMessage] = useState<string>('');
    const { isDarkTheme, isAutoTheme, toggleTheme } = useThemeContext();

    let ab: ReactNode = null;
    if (branch.ab[0] + branch.ab[1] > 0) {
        ab = <span>{branch.ab[1]}↓ {branch.ab[0]}↑</span>;
    }

    useEffect(() => {
        const listener = (error: string) => {
            Toast.error(error, { closable: true });
        };

        const event = `sync.error`;
        socket.on(event, listener);
        return () => {
            socket.off(event, listener);
        };
    }, []);

    useEffect(() => {
        const listener = (message: string) => {
            setMessage(message);
        };

        const event = `sync.message`;
        socket.on(event, listener);
        return () => {
            socket.off(event, listener);
        };
    }, []);

    const clickHandler = useCallback(() => {
        if (!syncing) {
            sync();
        }
    }, [syncing]);

    let themeItem: ReactNode;
    if (isAutoTheme) {
        themeItem = <MenuItem onClick={toggleTheme} tooltip={'theme.auto'}><BsCircleHalf /></MenuItem>;
    } else if (isDarkTheme) {
        themeItem = <MenuItem onClick={toggleTheme} tooltip={'theme.dark'}><BsMoon /></MenuItem>;
    } else {
        themeItem = <MenuItem onClick={toggleTheme} tooltip={'theme.light'}><BsSun /></MenuItem>;
    }

    const [assistantWindow, setAssistantWindow] = useLocalStorageState('assistant.window', true);

    return <Container>
        <LeftContainer>
            <MenuItem><VscCircleFilled color='var(--bs-success)' size={20} /></MenuItem>
            <MenuItem><VscSourceControl /><span>{branch.head}</span></MenuItem>
            <MenuItem tooltip={'status-bar.sync'} onClick={clickHandler}>
                <VscSync className={syncing ? 'bi-spin' : ''} />{ab}
            </MenuItem>
            {syncing && <MessageItem>{message}</MessageItem>}
        </LeftContainer>
        <RightContainer>
            {assistant && <MenuItem onClick={() => setAssistantWindow(!assistantWindow)}>🤖 小智</MenuItem>}
            {preview &&
                <MenuItem onClick={preview} tooltip={'status-bar.preview'} data-tour={'editor-preview'}><VscPreview /></MenuItem>}
            {feedback && <MenuItem onClick={feedback} tooltip={'status-bar.feedback'}><Feedback /></MenuItem>}
            {themeItem}
            <InjectedComponentSet role={'editor:menu:item'} component={MenuItem} />
        </RightContainer>
    </Container>;
}

const Item = styled.div`
    line-height: 24px;
    padding: 0 5px;
    display: flex;
    align-items: center;
    cursor: pointer;

    .bi:not(:only-child) {
        margin-right: 4px;
    }

    &:hover {
        background: var(--ttw-box-hover-background);
    }

    span {
        font-family: Segoe WPC, Segoe UI, Microsoft YaHei, sans-serif;
        font-size: 12px;
    }
`;

const MessageItem = styled(Item)`
    cursor: default;
    color: #6a737d;

    &:hover {
        background: inherit;
    }
`;

const LeftContainer = styled.div`
    display: flex;
`;

const RightContainer = styled.div`
    display: flex;
    padding-right: 1rem;
    gap: 0.5rem;
`;

const Container = styled.div`
    order: 10;
    height: 25px;
    border-top: 1px solid var(--ttw-border-color);
    background: var(--ttw-foreground);
    font-size: 13px;
    position: relative;
    display: flex;
    justify-content: space-between;

    body:fullscreen & {
        display: none;
    }
`;
