import useFormatMessage from '../../lib/use-format-message';
import { PaneHeader } from '../../components/pane';
import SearchWidget from './search/widget';
import AllFiles from './all-files';
import { useSelector } from '@topwrite/common';
import SearchResults from './search/results';

export default function Files() {
    const t = useFormatMessage();

    const { query } = useSelector('files');

    return <>
        <PaneHeader>
            {t('active-bar.files')}
        </PaneHeader>
        <SearchWidget />
        {query ? <SearchResults /> : <AllFiles />}
    </>;
};
