import { FileEntries, FileEntry, useFileEntries } from '../../lib/use-files';
import { styled, useActions, useModel } from '@topwrite/common';
import { FC, useCallback, useMemo, useRef, useState } from 'react';
import path from 'path';
import useFormatMessage from '../../lib/use-format-message';
import FileIcon from '../../components/file-icon';
import { Dropdown, DropdownProps } from 'react-bootstrap';
import useFullPath from '../../lib/use-full-path';
import { GoKebabVertical } from 'react-icons/go';
import { BsPencil, BsTrash } from 'react-icons/bs';
import Modal from '../../components/modal';
import { Actions, FileItem, FileList } from '../../components/file-list';
import { FaFolder, FaFolderOpen } from 'react-icons/fa';
import useHover from '@react-hook/hover';
import ContextMenu, { MenuItem } from '../../components/context-menu';

const ItemActions: FC<{ file: FileEntry, id: string }> = ({ file, id }) => {
    const t = useFormatMessage();
    const { renameFile, removeFile } = useActions('workspace');
    const onItemClick: DropdownProps['onSelect'] = useCallback(async (key, e) => {
        e.preventDefault();
        switch (key) {
            case 'rename':
                const result = await Modal.prompt<string>({
                    title: t('side-bar.rename'),
                    schema: {
                        title: t('side-bar.new_name'),
                        type: 'string',
                    },
                    formData: file.path
                });

                if (result) {
                    renameFile(file.path, result);
                }
                break;
            case 'delete':
                if (await Modal.confirm({ message: t('confirm.delete') })) {
                    removeFile(file.path);
                }
                break;
        }
    }, [renameFile, removeFile]);

    return <Actions>
        <Dropdown onSelect={onItemClick}>
            <Dropdown.Toggle variant='light' id={id}>
                <GoKebabVertical />
            </Dropdown.Toggle>
            <ContextMenu>
                <MenuItem icon={<BsPencil />} eventKey='rename'>
                    {t('side-bar.rename')}
                </MenuItem>
                <MenuItem icon={<BsTrash />} eventKey='delete'>
                    {t('side-bar.delete')}
                </MenuItem>
            </ContextMenu>
        </Dropdown>
    </Actions>;
};

const Item: FC<{ file: FileEntry }> = ({ file }) => {

    const [{ current, status: { changes } }, { setCurrent }] = useModel('workspace');

    const [open, setOpen] = useState(() => {
        return Boolean(file.type === 'dir' && current?.startsWith(file.path + '/'));
    });

    const active = current === file.path;
    const changed = Boolean(changes[useFullPath(file.path)]);

    const hasChildren = file.type === 'dir';

    const ref = useRef<HTMLDivElement>(null);

    const hovering = useHover(ref);

    const handleClick = () => {
        if (hasChildren) {
            setOpen(!open);
        } else if (!active) {
            setCurrent(file.path);
        }
    };

    return <FileItem
        open={open}
        active={active}
        changed={changed}
        text={path.basename(file.path)}
        space={hasChildren ? (open ? <FaFolderOpen size={12} /> : <FaFolder size={12} />)
            : <FileIcon filename={file.path} />
        }
        onClick={handleClick}
        suffix={hovering && <ItemActions id='file-item' file={file} />}
        ref={ref}
    >
        {file.type === 'dir' ? <Items entries={file.children} /> : null}
    </FileItem>;
};

const Items: FC<{ entries: FileEntries }> = ({ entries }) => {

    const files = useMemo(() => {
        return Object.values(entries).sort((a, b) => {
            if (a.type !== b.type) {
                return a.type === 'dir' ? -1 : 1;
            }

            return a.path.localeCompare(b.path);
        });
    }, [entries]);


    return <FileList>
        {files.map((file) => {
            return <Item key={file.path} file={file} />;
        })}
    </FileList>;
};


export default function AllFiles() {
    const entries = useFileEntries();

    return <Container>
        <Items entries={entries} />
    </Container>;
}

const Container = styled.div`
  position: relative;
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
`;
