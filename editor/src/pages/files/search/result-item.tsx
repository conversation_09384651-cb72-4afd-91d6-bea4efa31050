import { BsCaretDownFill, BsCaretRightFill, BsFileText } from 'react-icons/bs';
import { FileItem, FileList, Icon } from '../../../components/file-list';
import { Badge } from 'react-bootstrap';
import { useMemo, useState } from 'react';
import RangeItem from './range-item';
import { SearchResult } from 'repo';
import { styled } from '@topwrite/common';

export default function ResultItem({ result }: { result: SearchResult }) {
    const [open, setOpen] = useState(true);

    const count = useMemo(() => result.matches.reduce((acc, match) => acc + match.ranges.length, 0), [result]);

    return <FileItem
        prefix={<StyledIcon><BsFileText /></StyledIcon>}
        text={result.path}
        space={open ? <BsCaretDownFill size={12} /> : <BsCaretRightFill size={12} />}
        suffix={<Icon><Badge pill bg={'secondary'}>{count}</Badge></Icon>}
        open={open}
        onClick={() => setOpen(!open)}
    >
        <FileList>
            {result.matches.map(match => match.ranges.map(range => {
                return <RangeItem
                    key={`${result.path}:${match.lineNumber}:${range.start}:${range.end}`}
                    path={result.path}
                    text={match.text}
                    lineNumber={match.lineNumber}
                    range={range}
                />;
            }))}
        </FileList>
    </FileItem>;
}

const StyledIcon = styled(Icon)`
  margin-right: .25rem;
  width: auto;
`;
