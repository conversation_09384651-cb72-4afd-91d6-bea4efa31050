import { CompositionInput, styled, useModel } from '@topwrite/common';
import { VscChevronDown, VscChevronRight } from 'react-icons/vsc';
import { useCallback, useMemo } from 'react';
import { debounce } from 'lodash';
import useFormatMessage from '../../../lib/use-format-message';

export default function SearchWidget() {
    const t = useFormatMessage();
    const [{ query, replace, mode }, { search, setMode, setReplace }] = useModel('files');

    const debounced = useMemo(() => debounce(search, 500), [search]);

    const handleSearch = useCallback((e) => {
        debounced(e.target.value);
    }, [debounced]);

    const handleChange = useCallback((e) => {
        setReplace(e.target.value);
    }, []);

    return <Container>
        <ModeArea onClick={() => {
            setMode(mode === 'search' ? 'replace' : 'search');
        }}>
            {mode === 'search' && <VscChevronRight />}
            {mode === 'replace' && <VscChevronDown />}
        </ModeArea>
        <InputArea>
            <FindInput>
                <CompositionInput
                    defaultValue={query}
                    className='form-control'
                    type='search'
                    placeholder={t('active-bar.files.search')}
                    onChange={handleSearch}
                    autoFocus
                />
            </FindInput>
            {mode === 'replace' && <ReplaceInput>
                <input
                    type='text'
                    className='form-control'
                    defaultValue={replace}
                    onChange={handleChange}
                    placeholder={t('active-bar.files.replace')}
                />
            </ReplaceInput>}
        </InputArea>
    </Container>;
}

const FindInput = styled.div`

`;

const ReplaceInput = styled.div`

`;

const ModeArea = styled.div`
    width: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 0.375rem;

    &:hover {
        background: var(--ttw-file-active-background);
    }
`;

const InputArea = styled.div`
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: .5em;
`;

const Container = styled.div`
    padding: .5em .5em .5em .25em;
    border-bottom: 1px solid var(--ttw-border-color);
    display: flex;
    gap: .25em;
`;
