import { Actions, FileItem } from '../../../components/file-list';
import { SearchResultRange } from 'repo';
import { useCallback, useMemo, useRef } from 'react';
import { Buffer } from 'buffer';
import { styled, useModel, useSelector } from '@topwrite/common';
import useHover from '@react-hook/hover';
import { Button } from 'react-bootstrap';
import { VscReplace } from 'react-icons/vsc';
import useFormatMessage from '../../../lib/use-format-message';
import { socket } from '../../../lib/socket';

export default function RangeItem({ text, range, path, lineNumber }: {
    path: string,
    text: string,
    lineNumber: number,
    range: SearchResultRange
}) {
    const t = useFormatMessage();
    const [{ current }, { setCurrent }] = useModel('workspace');
    const { instance } = useSelector('editor');
    const [{ active, replace, mode }, { setActive }] = useModel('files');
    const id = `${path}:${lineNumber}:${range.start}:${range.end}`;

    const [before, inside, after] = useMemo(() => {
        const buffer = Buffer.from(text);

        const before = buffer.subarray(0, range.start).toString();
        const inside = buffer.subarray(range.start, range.end).toString();
        const after = buffer.subarray(range.end).toString();

        return [before, inside, after];
    }, [range, text]);

    const handleClick = useCallback(() => {
        setActive(id);
        if (current === path) {
            //跳转到指定位置
            instance?.gotoLine(lineNumber);
        } else {
            setCurrent(path, lineNumber);
        }
    }, [current, instance, path, lineNumber, id]);

    const fullText = <Text title={text}>
        {before}
        <Match>{inside}</Match>
        {after}
    </Text>;

    const ref = useRef<HTMLDivElement>(null);

    const hovering = useHover(ref);

    const doReplace = useCallback(() => {
        socket.replaceFileAsync(path, replace, lineNumber, range.start, range.end - range.start);
    }, [path, lineNumber, range, replace]);

    return <FileItem
        active={active === id}
        text={fullText}
        onClick={handleClick}
        suffix={hovering && mode === 'replace' && <Actions>
            <Button onClick={doReplace} variant={'light'} title={t('active-bar.files.replace')}>
                <VscReplace />
            </Button>
        </Actions>}
        ref={ref}
    />;
}

const Match = styled.span`
    background-color: rgba(234, 92, 0, 0.33);
`;

const Text = styled.span`
    color: var(--bs-gray-600);
`;
