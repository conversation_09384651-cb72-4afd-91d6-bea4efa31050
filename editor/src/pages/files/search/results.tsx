import { styled, useActions, useSelector } from '@topwrite/common';
import { isEmpty } from 'lodash';
import Empty from '../../../components/empty';
import { FileList } from '../../../components/file-list';
import ResultItem from './result-item';
import { useEffect, useMemo } from 'react';
import { Decorate } from '../../../components/editor/plate-plugin';
import { Range, Text } from 'slate';
import useFormatMessage from '../../../lib/use-format-message';
import { Buffer } from 'buffer';

export default function SearchResults() {
    const t = useFormatMessage();
    const { query, results, active } = useSelector('files');
    const { addDecorate, removeDecorate } = useActions('editor');

    const current = useMemo(() => {
        if (active) {
            const [path, lineNumber, start, end] = active.split(':');

            const result = results.find(result => result.path === path);
            if (result) {
                const match = result.matches.find(match => match.lineNumber === Number(lineNumber));
                if (match) {
                    const range = match.ranges.find(range => range.start === Number(start) && range.end === Number(end));
                    if (range) {
                        const buffer = Buffer.from(match.text);

                        const before = buffer.subarray(0, range.start).toString();
                        const inside = buffer.subarray(range.start, range.end).toString();

                        return {
                            lineNumber: match.lineNumber,
                            start: before.length + 1,
                            end: before.length + inside.length + 1,
                        };
                    }
                }
            }
        }
        return null;
    }, [results, active]);

    useEffect(() => {
        const decorate: Decorate = ([node, path]) => {
            const ranges: Range[] = [];

            if (query && Text.isText(node)) {
                const { text, position } = node;
                const parts = text.split(query);
                let offset = 0;

                parts.forEach((part, i) => {
                    if (i !== 0) {
                        const range: Range = {
                            anchor: { path, offset: offset - query.length },
                            focus: { path, offset },
                        };

                        range['lowlight'] = true;
                        if (current && position
                            && position.start.line === current.lineNumber
                            && position.start.column + range.anchor.offset === current.start
                            && position.start.column + range.focus.offset === current.end
                        ) {
                            range['highlight'] = true;
                        }
                        ranges.push(range);
                    }

                    offset = offset + part.length + query.length;
                });
            }

            return ranges;
        };

        addDecorate(decorate);

        return () => {
            removeDecorate(decorate);
        };
    }, [query, active, current]);

    return <Container>
        {isEmpty(results) ? <Empty message={t('active-bar.files.search.empty')} /> : <FileList>
            {results.map((item) => <ResultItem key={item.path} result={item} />)}
        </FileList>}
    </Container>;
}

const Container = styled.div`
    flex: 1;
    overflow-x: hidden;
    overflow-y: auto;
    position: relative;

    .badge {
        font-weight: 600;
        height: 16px;
        line-height: 16px;
        width: 16px;
        padding: 0;
    }

`;
