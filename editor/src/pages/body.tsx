import { styled, useAsyncEffect, useLocalStorageState, usePrevious, useSelector } from '@topwrite/common';
import { Allotment } from 'allotment';
import { isEqual } from 'lodash';
import { useEffect, useState } from 'react';
import Modal from '../components/modal';
import useFormatMessage from '../lib/use-format-message';
import ActiveBar, { ActiveBarContainer } from './active-bar';
import SideBar from './side-bar';
import Title from './title';
import Workspace from './workspace';
import Assistant from './assistant';

export default function Body() {
    const { extra } = useSelector('workspace');
    const { config } = useSelector('book');
    const [isFullscreen, setIsFullscreen] = useState(false);

    const prevStructure = usePrevious({
        root: config.getValue('root'),
        structure: config.getValue('structure')
    });

    const t = useFormatMessage();

    useAsyncEffect(async () => {
        if (prevStructure && !isEqual({
            root: config.getValue('root'),
            structure: config.getValue('structure')
        }, prevStructure)) {
            await Modal.alert(t('body.reload'));
            location.reload();
        }
    }, [config]);

    useEffect(() => {
        const listener = () => {
            setIsFullscreen(document.fullscreenElement === document.body);
        };

        document.body.addEventListener('fullscreenchange', listener);

        return () => {
            document.body.removeEventListener('fullscreenchange', listener);
        };
    }, []);

    const [aside, setAside] = useLocalStorageState('aside.width', 300);
    const [assistantWidth, setAssistantWidth] = useLocalStorageState('assistant.width', 400);
    const [assistantWindow] = useLocalStorageState('assistant.window', true);

    return <Container>
        <ActiveBarContainer>
            <Allotment onDragEnd={([asideWidth]) => {
                setAside(asideWidth);
            }}>
                {!isFullscreen && <Allotment.Pane className='d-flex' minSize={300} maxSize={600} preferredSize={aside}>
                    <BarContainer>
                        <Title />
                        <BarBody>
                            <ActiveBar />
                            <SideBar />
                        </BarBody>
                    </BarContainer>
                </Allotment.Pane>}
                <Allotment.Pane>
                    <Allotment onDragEnd={([, assistantWidth]) => {
                        setAssistantWidth(assistantWidth);
                    }}>
                        <Allotment.Pane minSize={600}>
                            <Workspace />
                            {extra && <ExtraContainer>{extra}</ExtraContainer>}
                        </Allotment.Pane>
                        {assistantWindow && <Allotment.Pane preferredSize={assistantWidth} minSize={400}>
                            <Assistant />
                        </Allotment.Pane>}
                    </Allotment>
                </Allotment.Pane>
            </Allotment>
        </ActiveBarContainer>
    </Container>;
}

const ExtraContainer = styled.div`
    top: 0;
    left: 1px;
    right: 0;
    bottom: 0;
    position: absolute;
    background: var(--ttw-background);
    z-index: 300;
`;

const BarBody = styled.div`
    display: flex;
    flex-direction: row;
    flex-grow: 1;
    overflow: hidden;
`;

const BarContainer = styled.div`
    flex: auto;
    flex-direction: column;
    overflow: hidden;
    display: flex;

    body:fullscreen & {
        display: none;
    }
`;

const Container = styled.div`
    flex: auto;
    overflow: hidden;
    order: 5;
    position: relative;
    display: flex;
`;
