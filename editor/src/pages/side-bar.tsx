import { Tab } from 'react-bootstrap';
import Catalog from './catalog';
import Files from './files';
import Changes from './commit/changes';
import Release from './release';
import History from './history';
import { styled } from '@topwrite/common';
import Theme from './plugin/theme';
import Extension from './plugin/extension';

export default function SideBar() {

    return <Container>
        <Tab.Content>
            <Tab.Pane eventKey='catalog' data-tour={'catalog-pane'}>
                <Catalog />
            </Tab.Pane>
            <Tab.Pane eventKey='files'>
                <Files />
            </Tab.Pane>
            <Tab.Pane eventKey='commit'>
                <Changes />
            </Tab.Pane>
            <Tab.Pane eventKey='history'>
                <History />
            </Tab.Pane>
            <Tab.Pane eventKey='release'>
                <Release />
            </Tab.Pane>
            <Tab.Pane eventKey='theme'>
                <Theme />
            </Tab.Pane>
            <Tab.Pane eventKey='extension'>
                <Extension />
            </Tab.Pane>
        </Tab.Content>
    </Container>;
}

const Container = styled.div`
    display: flex;
    overflow: hidden;
    flex-direction: column;
    flex-wrap: nowrap;
    flex-grow: 1;

    .nav-tabs {
        height: 31px;

        .nav-link {
            border-top: none;
            border-radius: 0;
            padding-top: 0;
            padding-bottom: 0;
            line-height: 30px;
            font-size: 14px;
            color: #999;

            &:focus {
                outline: none;
            }

            &.active {
                color: #333;
            }
        }
    }

    .tab-content {
        user-select: none;
        position: relative;
        flex: auto;
        display: flex;
        overflow: hidden;
        background: var(--ttw-background);

        & > .active {
            display: flex;
        }

        .tab-pane {
            flex-direction: column;
            flex: auto;
            width: 100%;
        }
    }
`;
