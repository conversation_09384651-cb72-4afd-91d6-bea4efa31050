import useFormatMessage from '../../lib/use-format-message';
import { styled, useActions, useModel, useSelector } from '@topwrite/common';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { isEmpty } from 'lodash';
import Tooltip from '../../components/tooltip';
import Diff from './diff';
import FileIcon from '../../components/file-icon';
import confirm from '../../components/modal/confirm';
import { socket } from '../../lib/socket';
import Button from '../../components/button';
import { GoDiffAdded, GoDiffModified, GoDiffRemoved } from 'react-icons/go';
import { RiArrowGoBackFill } from 'react-icons/ri';
import Empty from '../../components/empty';
import Form from '../../components/form';
import { PaneHeader } from '../../components/pane';
import { FileItem, FileList, Icon } from '../../components/file-list';
import Loader from '../../components/loader';
import { generateCommitMessage } from '../../lib/generate-commit-message';

interface StatusIconProps {
    status: 'M' | 'A' | 'D' | 'U';
}

const StatusIcon = ({ status }: StatusIconProps) => {
    switch (status) {
        case 'A':
            return <GoDiffAdded className='text-success' />;
        case 'M':
            return <GoDiffModified className='text-warning' />;
        case 'D':
            return <GoDiffRemoved className='text-danger' />;
        default:
            return null;
    }
};

export default function Changes() {
    const t = useFormatMessage();
    const [{ status: { changes }, release: releaseState, syncing }, { commit }] = useModel('workspace');
    const [current, setCurrent] = useState<string>();
    const { setExtra } = useActions('workspace');
    const { release } = useSelector('options');
    const releasing = releaseState == 'pending' || releaseState === 'running';

    useEffect(() => {
        return () => {
            setCurrent(undefined);
            setExtra(null);
        };
    }, []);

    useEffect(() => {
        if (current && !changes[current]) {
            setCurrent(undefined);
            setExtra(null);
        }
    }, [changes]);

    const defaultMessage = useMemo(() => generateCommitMessage(changes), [changes]);

    const handleClick = (path: string) => {
        return () => {
            setCurrent(path);
            setExtra(<Diff filename={path} />);
        };
    };

    const handleResetAll = useCallback(async () => {
        if (await confirm({
            title: t('commit.reset_all_title'),
            message: t('commit.reset_all_message')
        })) {
            await socket.discard('.');
        }
    }, []);

    const [formData, setFormData] = useState({ message: '' });

    const handleCommit = useCallback(async (release: boolean) => {
        commit(formData.message || defaultMessage, release);
    }, [commit, formData, defaultMessage]);

    return <>
        <PaneHeader
            action={!isEmpty(changes) && <Tooltip tooltip={t('commit.reset_all')}>
                <Button variant='light' onClick={handleResetAll}><RiArrowGoBackFill /></Button>
            </Tooltip>}
        >
            {t('active-bar.commit.change')}
        </PaneHeader>
        <ChangesBody>
            {isEmpty(changes) ? <Empty /> :
                <>
                    <ChangesFile>
                        <FileList>
                            {Object.entries(changes).map(([path, change]) => {
                                return <FileItem
                                    active={current === path}
                                    key={path}
                                    text={path}
                                    onClick={handleClick(path)}
                                    space={<FileIcon filename={path} />}
                                    suffix={<Icon><StatusIcon status={change} /></Icon>}
                                />;
                            })}
                        </FileList>
                    </ChangesFile>
                    <ChangesCommit className='d-grid'>
                        <Form
                            schema={{
                                type: 'object',
                                properties: {
                                    message: {
                                        title: t('commit.message'),
                                        type: 'string',
                                    }
                                }
                            }}
                            uiSchema={{
                                message: {
                                    'ui:widget': 'textarea',
                                    'ui:placeholder': defaultMessage,
                                    'ui:options': {
                                        rows: 5
                                    }
                                }
                            }}
                            formData={formData}
                            onChange={({ formData }) => setFormData(formData)}
                        >
                            <div className='d-flex justify-content-start'>
                                <Button onClick={() => handleCommit(false)} className='me-1'>
                                    {t('commit.submit')}
                                </Button>
                                {release && (releasing ? <Tooltip wrap tooltip={t('commit.releasing')}>
                                        <Button variant='secondary' disabled>
                                            {t('commit.submit_with_release')}
                                        </Button>
                                    </Tooltip> :
                                    <Button variant='secondary' onClick={() => handleCommit(true)}>
                                        {t('commit.submit_with_release')}
                                    </Button>)}
                            </div>
                        </Form>
                        <Loader loading={syncing} />
                    </ChangesCommit>
                </>}
        </ChangesBody>
    </>;
}

const ChangesCommit = styled.div`
    border-top: 1px solid var(--ttw-border-color);
    padding: 1em;
    position: relative;
`;

const ChangesFile = styled.div`
    flex: auto;
    overflow-x: hidden;
    overflow-y: auto;
    position: relative;
`;

const ChangesBody = styled.div`
    flex: auto;
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
`;
