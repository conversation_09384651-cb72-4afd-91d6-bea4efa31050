import FileIcon from '../../components/file-icon';
import { socket } from '../../lib/socket';
import { styled, useAsync, useAsyncCallback } from '@topwrite/common';
import DiffViewer from '../../components/diff-viewer';
import useFormatMessage from '../../lib/use-format-message';
import confirm from '../../components/modal/confirm';
import Button from '../../components/button';
import Loader from '../../components/loader';
import { useCallback } from 'react';
import { PaneHeader } from '../../components/pane';

interface DiffProps {
    filename: string;
}

export default function Diff({ filename }: DiffProps) {

    const { result: file, loading } = useAsync(async () => {
        const files = await socket.readDiff('HEAD', filename);
        return files[0];
    }, [filename]);

    const { execute, loading: discarding } = useAsyncCallback(() => socket.discard(filename));

    const t = useFormatMessage();

    const onClick = useCallback(async () => {
        if (await confirm({
            title: t('commit.reset_title', { filename }),
            message: t('commit.reset_message')
        })) {
            await execute();
        }
    }, [execute, filename]);

    return <Container>
        <PaneHeader
            action={<Button
                loading={discarding}
                variant='outline-danger'
                onClick={onClick}
            >
                {t('commit.reset')}
            </Button>}
        >
            <FileIcon filename={filename} />
            {filename}
            {file && file.new_mode !== file.old_mode &&
            <small className='ms-1 text-secondary'>
                {!file.new_mode && `${t('diff.deleted')} `}{file.old_mode || 0} → {file.new_mode || 0}
            </small>}
        </PaneHeader>
        {file && <CustomDiffViewer file={file} />}
        <Loader loading={loading} />
    </Container>;
}

const CustomDiffViewer = styled(DiffViewer)`
  flex: auto;
  overflow-y: auto;
  padding-bottom: 20px;
`;

const Container = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
`;
