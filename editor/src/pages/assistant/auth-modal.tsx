import { IChangeEvent } from '@rjsf/core';
import { useActions } from '@topwrite/common';
import { Schema } from '@topthink/json-form';
import { useRef, useState } from 'react';
import { Modal } from 'react-bootstrap';
import Button from '../../components/button';
import Form, { FormType } from '../../components/form';
import useFormatMessage from '../../lib/use-format-message';

interface AuthModalProps {
    show: boolean;
    onHide: () => void;
}

interface FormData {
    token: string;
}

export default function AuthModal({ show, onHide }: AuthModalProps) {
    const [formData, setFormData] = useState<FormData>({ token: '' });
    const [errors, setErrors] = useState<any[]>([]);
    const { updateConfig } = useActions('book');
    const formRef = useRef<FormType>(null);
    const t = useFormatMessage();

    const handleFormChange = ({ formData, errors }: IChangeEvent<FormData>) => {
        setFormData(formData);
        setErrors(errors);
    };

    const handleSave = async () => {
        if (errors.length === 0) {
            updateConfig((config) => {
                config.setValue(['assistant', 'token'], formData.token);
            });
            onHide();
        }
    };

    const schema: Schema = {
        type: 'object',
        properties: {
            token: {
                type: 'string',
                encrypt: true,
                minLength: 1
            }
        },
        required: ['token']
    };

    const uiSchema = {
        token: {
            'ui:placeholder': t('assistant.auth.token_placeholder'),
            'ui:label': false
        }
    };

    return (
        <Modal show={show} onHide={onHide} backdrop='static' centered>
            <Modal.Header closeButton>
                <Modal.Title>{t('assistant.auth.title')}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <Form
                    ref={formRef}
                    schema={schema}
                    uiSchema={uiSchema}
                    formData={formData}
                    onChange={handleFormChange}
                    onSubmit={handleSave}
                />
            </Modal.Body>
            <Modal.Footer>
                <Button variant='secondary' onClick={onHide}>
                    {t('assistant.auth.cancel')}
                </Button>
                <Button
                    onClick={handleSave}
                    disabled={errors.length > 0}
                >
                    {t('assistant.auth.save')}
                </Button>
            </Modal.Footer>
        </Modal>
    );
}
