import { Fragment, useState } from 'react';
import { Spinner } from 'react-bootstrap';
import Content, { ContentType } from './content';
import { BsXCircleFill, BsCheckCircleFill, BsCaretUpFill, BsCaretDownFill, BsDashCircleFill } from 'react-icons/bs';
import { styled } from '@topwrite/common';
import useFormatMessage from '../../lib/use-format-message';

export interface ToolMessage {
    name: string;
    title: string;
    arguments: string;
    response?: string;
    error?: boolean;
    content?: ContentType;
}

interface Props {
    tool: ToolMessage;
    loading?: boolean;
}

const formatArguments = (tool: ToolMessage) => {
    try {
        return JSON.stringify(JSON.parse(tool.arguments), null, 4);
    } catch (e) {
        return tool.arguments;
    }
};

export default function Tool({ tool, loading }: Props) {
    const [show, setShow] = useState(false);
    const t = useFormatMessage();
    const hasResponse = 'response' in tool;
    const isCancelled = !hasResponse && !loading;

    return <Fragment>
        <div className='mb-2'>
            {show && (hasResponse || isCancelled) ? <ToolContainer className='rounded fs-7'>
                    <div onClick={() => setShow(false)} role='button' className='d-flex align-items-center p-1 px-2 gap-2'>
                        {isCancelled ? <BsDashCircleFill className='text-muted' /> :
                            (tool.error ? <BsXCircleFill className='text-danger' /> :
                                <BsCheckCircleFill className='text-success' />)}
                        <span className='text-muted'>{isCancelled ? t('assistant.tool.cancelled') : t('assistant.tool.used')}</span>
                        <span>{tool.title}</span>
                        <BsCaretUpFill className='text-muted' />
                    </div>
                    <div className='border-top p-2 d-flex flex-column gap-2'>
                        <ToolSection className='border rounded'>
                            <div className='d-flex align-items-center p-1 px-2 fs-7 gap-2'>
                                <span className='text-muted'>{t('assistant.tool.parameters')}</span>
                            </div>
                            <Response className='border-top p-2'>
                                {formatArguments(tool)}
                            </Response>
                        </ToolSection>
                        {!isCancelled && <ToolSection className='border rounded'>
                            <div className='d-flex align-items-center p-1 px-2 fs-7 gap-2'>
                                <span className='text-muted'>{t('assistant.tool.response')}</span>
                            </div>
                            <Response className='border-top p-2'>
                                {tool.response || 'None'}
                            </Response>
                        </ToolSection>}
                    </div>
                </ToolContainer> :
                <ToolButton onClick={() => setShow(hasResponse || isCancelled)} role='button' className='d-inline-flex align-items-center rounded p-1 px-2 fs-7 gap-2'>
                    {isCancelled ? <BsDashCircleFill className='text-muted' /> :
                        (hasResponse ? (tool.error ? <BsXCircleFill className='text-danger' /> :
                                <BsCheckCircleFill className='text-success' />) :
                            <Spinner animation='border' variant='primary' size='sm' />)}
                    <span className='text-muted'>{isCancelled ? t('assistant.tool.cancelled') : (hasResponse ? t('assistant.tool.used') : t('assistant.tool.using'))}</span>
                    <span>{tool.title}</span>
                    {(hasResponse || isCancelled) && <BsCaretDownFill className='text-muted' />}
                </ToolButton>
            }
        </div>
        {tool.content && <Content value={tool.content} />}
    </Fragment>;
}

const Response = styled.div`
    white-space: pre-wrap;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: 150px;
`;

const ToolContainer = styled.div`
    background: var(--ttw-editor-background);
    box-shadow: var(--ttw-shadow);
`;

const ToolSection = styled.div`
    background: var(--ttw-priview-background);
`;

const ToolButton = styled.div`
    background: var(--ttw-editor-background);
    box-shadow: var(--ttw-shadow);
`;
