import { Modal } from 'react-bootstrap';
import { styled, useAsync } from '@topwrite/common';
import { socket } from '../../lib/socket';
import Di<PERSON>Viewer from '../../components/diff-viewer';
import FileIcon from '../../components/file-icon';
import Loader from '../../components/loader';
import useFormatMessage from '../../lib/use-format-message';

interface DiffModalProps {
    filename: string;
    show: boolean;
    onHide: () => void;
}

export default function DiffModal({ filename, show, onHide }: DiffModalProps) {
    const t = useFormatMessage();

    const { result: file, loading } = useAsync(async () => {
        if (!show || !filename) return null;
        const files = await socket.readDiff('HEAD', filename);
        return files[0];
    }, [filename, show]);

    return <Modal show={show} onHide={onHide} size='lg' scrollable>
        <Modal.Header closeButton>
            <Modal.Title as={'h6'} className={'d-flex align-items-center'}>
                <FileIcon filename={filename} />
                <span className='ms-2'>{filename}</span>
                {file && file.new_mode !== file.old_mode && <small className='ms-1 text-secondary'>
                    {!file.new_mode && t('assistant.diff_deleted') + ' '}{file.old_mode || 0} → {file.new_mode || 0}
                </small>}
            </Modal.Title>
        </Modal.Header>
        <Modal.Body className={'p-0'}>
            {loading ? <LoaderContainer>
                <Loader loading={true} />
            </LoaderContainer> : file ? <DiffViewer file={file} type='unified' /> :
                <EmptyState>{t('assistant.diff_load_error')}</EmptyState>}
        </Modal.Body>
    </Modal>;
}

const LoaderContainer = styled.div`
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
`;

const EmptyState = styled.div`
    text-align: center;
    color: var(--bs-secondary);
    padding: 2rem;
`;
