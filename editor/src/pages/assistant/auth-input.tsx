import { useState } from 'react';
import { styled, useActions } from '@topwrite/common';
import Button from '../../components/button';
import useFormatMessage from '../../lib/use-format-message';
import AuthModal from './auth-modal';

export default function AuthInput() {
    const [showAuthModal, setShowAuthModal] = useState(false);
    const t = useFormatMessage();

    return <Container>
        <AuthArea>
            <AuthMessage>
                {t('assistant.auth.input_disabled')}
            </AuthMessage>
            <AuthButton>
                <Button onClick={() => setShowAuthModal(true)}>
                    {t('assistant.auth.button')}
                </Button>
            </AuthButton>
        </AuthArea>
        
        <AuthModal 
            show={showAuthModal} 
            onHide={() => setShowAuthModal(false)} 
        />
    </Container>;
}

const Container = styled.div`
    background: var(--ttw-background);
    padding: 1rem;
`;

const AuthArea = styled.div`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color);
    border-radius: var(--bs-border-radius-lg);
    background: var(--ttw-foreground);
    text-align: center;
`;

const AuthMessage = styled.div`
    font-size: 0.9rem;
    color: var(--bs-secondary);
    margin-bottom: 1rem;
    line-height: 1.5;
`;

const AuthButton = styled.div`
    display: flex;
    justify-content: center;
`;
