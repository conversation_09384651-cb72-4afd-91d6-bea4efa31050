import { styled } from '@topwrite/common';
import { useCallback, useEffect, useRef } from 'react';
import MessageItem, { Message } from './message-item';

export default function MessageList({ messages }: { messages: Message[] }) {

    const scrollRef = useRef<HTMLDivElement>(null);

    const scrollToBottom = useCallback(() => {
        const dom = scrollRef.current;
        if (dom) {
            requestAnimationFrame(() => {
                requestAnimationFrame(() => {
                    setTimeout(() => {
                        dom.scrollTo({
                            top: dom.scrollHeight
                        });
                    }, 50);
                });
            });
        }
    }, []);

    useEffect(() => {
        if (messages.length > 0) {
            scrollToBottom();
        }
    }, [messages, scrollToBottom]);

    return <Container ref={scrollRef}>
        {messages.map((message, index) => {
            return <MessageItem key={index} message={message} />;
        })}
    </Container>;
}

const Container = styled.div`
    display: flex;
    flex-direction: column;
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
`;
