import { createContext, PropsWithChildren, useContext, useEffect, useState } from 'react';
import { useActions, PluginMeta } from '@topwrite/common';
import Detail from './detail';

interface SelectContextProps {
    setCurrent: (item: PluginMeta) => void;
    current?: PluginMeta;
}

const SelectContext = createContext<SelectContextProps | undefined>(undefined);

export default function Selectable({ children }: PropsWithChildren<any>) {

    const [current, setCurrent] = useState<PluginMeta>();
    const { setExtra } = useActions('workspace');

    useEffect(() => {
        setExtra(current ? <Detail meta={current} /> : null);
        return () => {
            setExtra(null);
        };
    }, [current]);

    return <SelectContext.Provider value={{ setCurrent, current }}>
        {children}
    </SelectContext.Provider>;
}

export function useSelectable() {
    return useContext(SelectContext)!;
}
