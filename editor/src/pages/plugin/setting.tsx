import { VscSettingsGear } from 'react-icons/vsc';
import { Config, InjectedComponent, PluginMeta, styled, useModel } from '@topwrite/common';
import Button from '../../components/button';
import { useCallback, useMemo, useState } from 'react';
import { AjvError, IChangeEvent, UiSchema } from '@rjsf/core';
import { cloneDeep, keys, set } from 'lodash';
import { JSONSchema7 } from 'json-schema';
import Form from '../../components/form';
import useOverlayState from '../../lib/use-overlay-state';
import { Modal } from 'react-bootstrap';
import { ModalProps } from 'react-bootstrap/Modal';
import useFormatMessage from '../../lib/use-format-message';

function PluginConfig({ name, defaultValue, onChange, schema, uiSchema }: {
    name: string,
    schema: JSONSchema7,
    uiSchema?: UiSchema;
    defaultValue?: any,
    onChange: (value: any, errors: AjvError[]) => void;
}) {
    const handleChange = useCallback(({ formData, errors }: IChangeEvent) => {
        onChange(formData, errors);
    }, [onChange]);

    return <InjectedComponent
        props={{
            onChange: handleChange,
            formData: defaultValue,
            schema: schema,
            uiSchema: uiSchema,
            liveValidate: true
        }}
        component={Form}
        role={`plugin:setting:${name}`}
    />;
}

function ExtensionSettingModal({ name, schema, ...props }: ModalProps & { name: string, schema: JSONSchema7; }) {

    const [{ config }, { updateConfig }] = useModel('book');
    const t = useFormatMessage();

    const [error, setError] = useState(false);
    const [value, setValue] = useState(() => {
        return config.getPluginConfig(name, schema).getValues();
    });

    const handleSubmit = useCallback(() => {
        updateConfig(config => {
            const pluginConfig = config.getPluginConfig(name, schema);
            pluginConfig.setValues(value);
            config.setPluginConfig(name, pluginConfig);
        });
        props.onHide?.();
    }, [updateConfig, value]);

    const uiSchema = useMemo(() => {
        const schema = {};
        const presetConfig = config.getPresetPluginConfig(name);

        keys(presetConfig).forEach(key => {
            set(schema, [key, "ui:widget"], "hidden");
        });

        return schema;
    }, [config]);

    return <Modal {...props} size='lg' scrollable>
        <Modal.Header>
            <Modal.Title as='h6'>{t('plugin.extension.setting.title')}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
            <PluginConfig
                defaultValue={value}
                onChange={(value, errors) => {
                    setValue(value);
                    setError(errors.length > 0);
                }}
                name={name}
                schema={schema}
                uiSchema={uiSchema}
            />
        </Modal.Body>
        <Modal.Footer>
            <Button variant='secondary' onClick={props.onHide}>{t('modal.cancel')}</Button>
            <Button onClick={handleSubmit} disabled={error}>{t('modal.ok')}</Button>
        </Modal.Footer>
    </Modal>;
}

function ThemeConfig({ defaultValue, onChange }: {
    defaultValue: any,
    onChange: (value: any, errors: AjvError[]) => void;
}) {
    const t = useFormatMessage();

    const commonSchema = useMemo(() => {
        const commonSchema = cloneDeep(Config.schema.properties!.themeConfig) as JSONSchema7;
        set(commonSchema, 'properties.defaultMode.title', t('plugin.theme.setting.default_mode'));
        set(commonSchema, 'properties.primaryColor.title', t('plugin.theme.setting.primary_color'));
        set(commonSchema, 'properties.expandLevel.title', t('plugin.theme.setting.expand_level'));
        set(commonSchema, 'properties.expandLevel.description', t('plugin.theme.setting.expand_level.description'));
        set(commonSchema, 'properties.navs.title', t('plugin.theme.setting.navs'));
        set(commonSchema, 'properties.navs.items.properties.title.title', t('plugin.theme.setting.navs.title'));
        set(commonSchema, 'properties.navs.items.properties.url.title', t('plugin.theme.setting.navs.url'));
        return commonSchema;
    }, []);

    const commonUiSchema = {
        defaultMode: {
            'ui:widget': 'radio',
            'ui:options': {
                button: true,
                enumOptions: [
                    {
                        value: 'light',
                        label: t('plugin.theme.setting.default_mode.light')
                    },
                    {
                        value: 'dark',
                        label: t('plugin.theme.setting.default_mode.dark')
                    }
                ],
            }
        },
        navs: {
            items: {
                url: {
                    'ui:description': t('plugin.theme.setting.navs.url.description')
                }
            }
        }
    };

    const handleChange = useCallback(({ formData, errors }: IChangeEvent) => {
        onChange(formData, errors);
    }, [onChange]);

    return <Form
        formData={defaultValue}
        schema={commonSchema}
        uiSchema={commonUiSchema}
        onChange={handleChange}
        liveValidate
    />;
}

function ThemeSettingModal({ name, type, schema, ...props }: ModalProps & {
    name: string,
    schema?: JSONSchema7;
}) {
    const [{ config }, { updateConfig }] = useModel('book');
    const t = useFormatMessage();

    const [commonError, setCommonError] = useState(false);
    const [commonConfig, setCommonConfig] = useState(() => {
        return config.getValue('themeConfig');
    });

    const [pluginError, setPluginError] = useState(false);
    const [pluginConfigValue, setPluginConfigValue] = useState(() => {
        if (schema) {
            return config.getPluginConfig(name, schema).getValues();
        }
        return undefined;
    });

    const handleSubmit = useCallback(() => {
        updateConfig(config => {
            config.setValue('themeConfig', commonConfig);
            if (schema) {
                const pluginConfig = config.getPluginConfig(name, schema);
                pluginConfig.setValues(pluginConfigValue);
                config.setPluginConfig(name, pluginConfig);
            }
        });
        props.onHide?.();
    }, [schema, updateConfig, commonConfig, pluginConfigValue]);

    return <Modal {...props} size='lg' scrollable>
        <Modal.Header>
            <Modal.Title as='h6'>{t('plugin.theme.setting.title')}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
            <fieldset className='border rounded p-3'>
                <legend className='float-none w-auto fs-6 px-2 mb-0'>{t('plugin.theme.setting.common')}</legend>
                <p className='text-muted'><small>{t('plugin.theme.setting.common.description')}</small></p>
                <ThemeConfig
                    defaultValue={commonConfig}
                    onChange={(value, errors) => {
                        setCommonConfig(value);
                        setCommonError(errors.length > 0);
                    }}
                />
            </fieldset>
            {schema && <fieldset className='border rounded p-3 mt-3'>
                <legend className='float-none w-auto fs-6 px-2 mb-0'>{t('plugin.theme.setting.current')}</legend>
                <PluginConfig
                    defaultValue={pluginConfigValue}
                    onChange={(value, errors) => {
                        setPluginConfigValue(value);
                        setPluginError(errors.length > 0);
                    }}
                    name={name}
                    schema={schema}
                />
            </fieldset>}
        </Modal.Body>
        <Modal.Footer>
            <Button variant='secondary' onClick={props.onHide}>{t('modal.cancel')}</Button>
            <Button onClick={handleSubmit} disabled={commonError || pluginError}>{t('modal.ok')}</Button>
        </Modal.Footer>
    </Modal>;
}

type SettingButtonProps = {
    name: string;
    type: 'theme';
    schema?: JSONSchema7;
} | {
    name: string;
    type: 'extension';
    schema: JSONSchema7;
};

function SettingButton({ schema, name, type }: SettingButtonProps) {
    const { state, show } = useOverlayState();

    return <>
        <Icon onClick={show}><VscSettingsGear /></Icon>
        {type === 'extension' && <ExtensionSettingModal {...state} name={name} schema={schema} />}
        {type === 'theme' && <ThemeSettingModal {...state} name={name} schema={schema} />}
    </>;
}

export default function Setting({ meta }: {
    meta: PluginMeta;
}) {

    const plugin = window.TopWritePlugins[meta.name];

    if (!plugin) {
        return null;
    }

    const { config: schema } = plugin;

    if (meta.type === 'theme') {
        return <SettingButton name={meta.name} type={meta.type} schema={schema} />;
    } else {
        if (!schema) return null;
        return <SettingButton name={meta.name} type={meta.type} schema={schema} />;
    }
}


const Icon = styled.a`
  display: flex;
  align-items: center;
  cursor: pointer;
  line-height: 18px;
  padding: 1px 2px;
  color: inherit !important;
  border-radius: 5px;
  font-size: 16px;
  height: 18px;

  &:hover {
    background: rgba(184, 184, 184, 0.31);
  }
`;
