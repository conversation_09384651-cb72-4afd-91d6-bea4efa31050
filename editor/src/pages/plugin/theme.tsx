import useFormatMessage from '../../lib/use-format-message';
import { pluginCenter, PluginMeta, styled, useSelector } from '@topwrite/common';
import Dimmer from '../../components/dimmer';
import Item from './item';
import ScrollList from '../../components/scroll-list';
import { PaneHeader } from '../../components/pane';
import Selectable from './selectable';
import { getCurrentTheme } from '../../lib/plugins';
import Button from '../../components/button';

export default function Theme() {
    const t = useFormatMessage();
    const { preview } = useSelector('options');
    const current = getCurrentTheme();

    return <Selectable>
        <PaneHeader
            action={preview && <Button variant='light' onClick={() => window.open(preview)}>{t('side-bar.plugin.theme.preview')}</Button>}
        >
            {t('side-bar.plugin.theme')}
        </PaneHeader>
        <Item meta={current} />
        <PaneHeader>
            {t('side-bar.plugin.theme.title')}
        </PaneHeader>
        <ListContainer>
            <ScrollList<PluginMeta>
                fetchData={async (result) => {
                    const list = await pluginCenter.queryPlugins({
                        type: 'theme',
                        page: result ? result.page + 1 : 1
                    });
                    return {
                        data: list.data,
                        hasMore: list.current_page < list.last_page,
                        page: list.current_page
                    };
                }}
                renderItem={(meta, index) => {
                    if (meta.name === current.name) {
                        return null;
                    }
                    return <Item key={index} meta={meta} />;
                }}
            />
        </ListContainer>
    </Selectable>;
}

const ListContainer = styled(Dimmer.Dimmable)`
  flex: auto;
  overflow-y: auto;
`;

