import { css, PluginMeta, styled } from '@topwrite/common';
import Actions from './actions';
import { useSelectable } from './selectable';
import { VscCloudDownload } from 'react-icons/vsc';
import { Statics } from './badge';
import numberAverage from '../../lib/number-average';
import ForbidIcon from '../../images/forbid.svg';
import useFormatMessage from '../../lib/use-format-message';

interface ItemProps {
    meta: PluginMeta;
}

export default function Item({ meta }: ItemProps) {
    const { current, setCurrent } = useSelectable();
    const t = useFormatMessage();

    return <Container
        disabled={meta.disabled}
        message={meta.expired ? t('plugin.trial.message') : undefined}
        selected={current && current.name === meta.name}
        onClick={() => setCurrent(meta)}
    >
        <Logo>
            <img src={`${meta.host}/${meta.icon}`} />
        </Logo>
        <Detail>
            <Header>
                <Name>{meta.display_name}</Name>
                <Statics>
                    <VscCloudDownload />
                    <span>{numberAverage(meta.installs_count)}</span>
                </Statics>
            </Header>
            <Footer>
                <Description title={meta.description}>{meta.description}</Description>
                <Actions meta={meta} mini />
            </Footer>
        </Detail>
    </Container>;
}

const Description = styled.div`
    font-size: 12px;
    color: var(--ttw-secondary-color);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
`;

const Logo = styled.div`
    width: 66px;
    height: 72px;
    padding: 15px 14px 15px 10px;
    text-align: center;
    line-height: 42px;
    position: relative;

    img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
`;

const Container = styled.div<{ selected?: boolean, disabled?: boolean, message?: string }>`
    font-size: 14px;
    line-height: 1.4em;
    display: flex;
    cursor: pointer;
    border-bottom: 1px solid var(--ttw-border-color);

    &:last-child {
        border-bottom: none;
    }

    &:hover {
        background-color: var(--ttw-file-hover-background);
    }

    ${props => props.selected && css`
        background-color: var(--ttw-file-active-background);
    `};

    ${props => props.message && css`
        ${Logo}:after {
            content: '${props.message}';
            font-size: 12px;
            position: absolute;
            left: 10px;
            right: 14px;
            top: 15px;
            bottom: 15px;
            background-color: rgba(0, 0, 0);
            color: #FFFFFF;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 3px;
            opacity: .5;
        }
    `};

    ${props => props.disabled && css`
        ${Logo}:after {
            content: '';
            position: absolute;
            left: 10px;
            right: 14px;
            top: 15px;
            bottom: 15px;
            background-color: rgba(0, 0, 0);
            background-image: url(${ForbidIcon});
            background-position: center;
            background-repeat: no-repeat;
            color: #FFFFFF;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 3px;
            opacity: .5;
        }
    `}
`;

const Detail = styled.div`
    flex: 1;
    padding: 10px 0;
    overflow: hidden;
`;

const Header = styled.div`
    height: 21px;
    line-height: 21px;
    display: flex;
    padding-right: 7px;
    margin-bottom: 8px;
`;

const Name = styled.span`
    font-weight: 600;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    flex: 1;
`;

const Footer = styled.div`
    display: flex;
    justify-content: space-between;
    padding-right: 7px;
    height: 22px;
    line-height: 22px;
    overflow: hidden;
`;
