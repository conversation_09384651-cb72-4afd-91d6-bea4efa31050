import { styled } from '@topwrite/common';
import { Badge } from 'react-bootstrap';
import { ReactNode } from 'react';
import useFormatMessage from '../../lib/use-format-message';

export function Type({ type }: { type: string }) {
    const t = useFormatMessage();
    return type === 'theme' ? <Container bg='info'>{t('plugin.theme')}</Container> :
        <Container bg='success'>{t('plugin.extension')}</Container>;
}

export function Paid() {
    const t = useFormatMessage();
    return <Container bg='danger'>{t('plugin.paid')}</Container>;
}

export function Identifier({ children }: { children: ReactNode }) {
    return <Container bg='secondary'>{children}</Container>;
}

const Container = styled(Badge)`
  opacity: .85;
  min-width: fit-content;
  margin-top: 2px;
  margin-bottom: 2px;
  margin-right: 7px;
`;

export const Statics = styled.span<{ size?: 'lg' }>`
    font-size: ${props => props.size === 'lg' ? '14px' : '12px'};
    display: flex;
    align-items: center;
    line-height: 1;
    column-gap: 5px;
    margin-left: 5px;

    svg {
        margin-right: 2px;
        font-size: 120%;
    }

    span {
        font-size: 100%;
        line-height: 1;
    }
`;
