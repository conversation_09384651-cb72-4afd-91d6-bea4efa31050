import { pluginCenter, PluginMeta, styled, useDebounce } from '@topwrite/common';
import ScrollList from '../../components/scroll-list';
import Item from './item';
import { ReactNode, useEffect, useRef, useState } from 'react';
import { FormControl } from 'react-bootstrap';
import useFormatMessage from '../../lib/use-format-message';

interface Props {
    type: 'theme' | 'extension';
    children?: ReactNode;
}

export default function Search({ type, children }: Props) {

    const t = useFormatMessage();
    const [keyword, setKeyword] = useState<string>('');

    const input = useRef<HTMLInputElement>(null);

    const [key, setKey] = useState<string>();

    const updateKeyword = useDebounce(function(key: string) {
        setKey(key);
    }, 600);

    useEffect(() => {
        updateKeyword(keyword);
    }, [keyword]);

    const placeholder = type === 'theme' ? t('side-bar.plugin.theme.placeholder') : t('side-bar.plugin.extension.placeholder');

    return <>
        <SearchInput>
            <FormControl
                ref={input}
                value={keyword}
                onChange={(e) => setKeyword(e.target.value)}
                placeholder={placeholder}
                type='search'
            />
        </SearchInput>
        {key ? <Container>
            <ScrollList<PluginMeta>
                key={key}
                fetchData={async (result) => {
                    const params: any = {
                        type: type,
                        page: String((result?.page || 0) + 1),
                        keyword: keyword
                    };

                    const list = await pluginCenter.queryPlugins(params);

                    return {
                        data: list.data,
                        hasMore: list.current_page < list.last_page,
                        page: list.current_page
                    };
                }}
                renderItem={(meta) => {
                    return <Item key={meta.name} meta={meta} />;
                }}
            />
        </Container> : children}
    </>;
}


const SearchInput = styled.div`
    padding: 6px;
    background: var(--ttw-foreground);
    border-bottom: 1px solid var(--ttw-border-color);
`;

const Container = styled.div`
  flex: 1;
  position: relative;
  overflow-y: auto;
`;
