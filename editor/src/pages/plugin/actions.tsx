import { Book, css, pluginCenter, PluginMeta, styled, useBook, useModel } from '@topwrite/common';
import { xor } from 'lodash';
import useFormatMessage from '../../lib/use-format-message';
import { MouseEvent, PropsWithChildren, ReactNode, useCallback } from 'react';
import Modal from '../../components/modal';
import Toast from '../../components/toast';
import FormatCurrency from './format-currency';
import Setting from './setting';
import {
    getInstalledExtensions,
    getInstalledTheme,
    isBrokenPlugin,
    isDevOrPresetPlugin,
    updatePlugin
} from '../../lib/plugins';
import Tooltip from '../../components/tooltip';
import dayjs from 'dayjs';
import { Button } from 'react-bootstrap';

interface BuyProps {
    meta: PluginMeta;
}

async function buyPlugin(book: Book, meta: PluginMeta, t: ReturnType<typeof useFormatMessage>) {
    window.open(pluginCenter.getBuyUrl(book, meta), 'buy-plugin');
    if (await Modal.confirm({
        title: t('plugin.buy.result.title'),
        message: t('plugin.buy.result.message'),
        okText: t('plugin.buy.result.ok')
    })) {
        //检查是支付成功
        if (await pluginCenter.bought(book, meta)) {
            return true;
        } else {
            Toast.error(t('plugin.buy.result.error'));
        }
    }
    return false;
}

export function Buy({ meta }: BuyProps) {

    const t = useFormatMessage();
    const book = useBook();

    const handleClick = useCallback(async () => {
        if (await buyPlugin(book, meta, t)) {
            window.location.reload();
        }
    }, [book, meta]);


    return <Item variant='outline-danger' onClick={handleClick}>
        {t('plugin.buy.button')}&nbsp;
        <FormatCurrency>{meta.price}</FormatCurrency>
    </Item>;
}

interface ActionsProps {
    meta: PluginMeta;
    mini?: boolean;
    className?: string;
}

export default function Actions({ meta, mini, className }: PropsWithChildren<ActionsProps>) {

    const [book, { updateConfig }] = useModel('book');
    const { config } = book;
    const t = useFormatMessage();

    const handleClick = useCallback((e: MouseEvent) => {
        e.stopPropagation();
    }, []);

    const buttons: ReactNode[] = [];

    //开发中的插件无安装卸载操作按钮
    if (!isDevOrPresetPlugin(meta)) {
        const handleReload = () => {
            window.location.reload();
        };
        const canInstall = async () => {
            return Number(meta.price) === 0 || await pluginCenter.bought(book, meta) || await buyPlugin(book, meta, t);
        };

        const installText = meta.price > 0 ? <FormatCurrency>{meta.price}</FormatCurrency> : t('plugin.install');

        if (meta.type === 'theme') {
            const theme = config.getValue('theme');
            const installed = getInstalledTheme();

            const isInstalled = installed && installed.name === meta.name;
            const isCurrent = `theme-${theme}` === meta.name;

            const handleInstall = async () => {
                if (await canInstall()) {
                    updateConfig(config => {
                        config.setValue('theme', meta.name.replace(/^theme-/, ''));
                    });
                }
            };

            const handleTrial = async () => {
                const trial = await pluginCenter.trial(book, meta);
                if (trial) {
                    if (isCurrent) {
                        //已安装
                        updatePlugin(m => {
                            if (m.name === meta.name) {
                                m.expired = trial.expire_time;
                                m.disabled = false;
                            }
                        });
                    }

                    updateConfig(config => {
                        config.setValue('theme', meta.name.replace(/^theme-/, ''));
                        config.setValue('plugins', [
                            ...config.getValue('plugins')
                        ]);
                    });
                }
            };

            if (isInstalled) {
                buttons.push(<Setting key='setting' meta={meta} />);
            }

            if (isCurrent) {
                if (!isInstalled || isBrokenPlugin(meta)) {
                    if (mini) {
                        buttons.unshift(<Item key='reload' onClick={handleReload}>{t('plugin.reload')}</Item>);
                    } else {
                        buttons.push(<Item key='reload' onClick={handleReload}>{t('plugin.reload')}</Item>);
                    }
                }
                buttons.push(<Tooltip key='uninstall' tooltip={'plugin.uninstall.theme'}>
                    <span><Item disabled>{t('plugin.uninstall')}</Item></span>
                </Tooltip>);
            } else {
                buttons.push(<Item key='install' onClick={handleInstall}>{installText}</Item>);
            }

            if (meta.price > 0) {
                if (meta.disabled || meta.expired) {
                    buttons.push(<Buy key='buy' meta={meta} />);
                }

                if (!meta.expired && (!isCurrent || meta.disabled)) {
                    buttons.unshift(<Item key='trial' variant={'outline-orange'} onClick={handleTrial}>{t('plugin.trial.button')}</Item>);
                }
            }
        } else {
            const plugins = config.getPlugins();
            const installed = getInstalledExtensions().map(plugin => plugin.name);

            const revised = xor(plugins, installed);

            const handleInstall = async () => {
                if (await canInstall()) {
                    updateConfig(config => {
                        config.setValue('plugins', [
                            ...config.getValue('plugins'),
                            meta.name
                        ]);
                    });
                }
            };

            const handleTrial = async () => {
                const trial = await pluginCenter.trial(book, meta);
                if (trial) {
                    if (plugins.includes(meta.name)) {
                        //已安装
                        updatePlugin(m => {
                            if (m.name === meta.name) {
                                m.expired = trial.expire_time;
                                m.disabled = false;
                            }
                        });
                        updateConfig(config => {
                            config.setValue('plugins', [
                                ...config.getValue('plugins'),
                            ]);
                        });
                    } else {
                        updateConfig(config => {
                            config.setValue('plugins', [
                                ...config.getValue('plugins'),
                                meta.name
                            ]);
                        });
                    }
                }
            };

            const handleUninstall = () => {
                updateConfig(config => {
                    config.setValue('plugins', config.getValue('plugins').filter(name => name !== meta.name));
                });
            };

            if (installed.includes(meta.name)) {
                buttons.push(<Setting key='setting' meta={meta} />);
            }

            const wantReload = revised.includes(meta.name) || isBrokenPlugin(meta);

            if (wantReload) {
                if (mini) {
                    buttons.unshift(<Item key='reload' onClick={handleReload}>{t('plugin.reload')}</Item>);
                } else {
                    buttons.push(<Item key='reload' onClick={handleReload}>{t('plugin.reload')}</Item>);
                }
            }

            if (plugins.includes(meta.name)) {
                buttons.push(<Item key='uninstall' onClick={handleUninstall}>{t('plugin.uninstall')}</Item>);
            } else {
                buttons.push(<Item key='install' onClick={handleInstall}>{installText}</Item>);
            }

            if (meta.price > 0) {
                if ((meta.disabled || meta.expired) && plugins.includes(meta.name)) {
                    buttons.push(<Buy key='buy' meta={meta} />);
                }

                if (!meta.expired && (!plugins.includes(meta.name) || meta.disabled)) {
                    buttons.unshift(<Item key='trial' variant={'outline-orange'} onClick={handleTrial}>{t('plugin.trial.button')}</Item>);
                }
            }
        }
    } else {
        buttons.push(<Setting key='setting' meta={meta} />);
    }

    let message: string | null = null;

    if (!mini) {
        if (meta.expired) {
            message = meta.disabled ? t('plugin.expired') : t('plugin.trailing', {
                date: dayjs(meta.expired).fromNow(true)
            });
        } else if (meta.disabled) {
            message = t('plugin.disabled');
        }
    }

    return <Container className={className} mini={mini} onClick={handleClick}>
        {mini ? buttons.splice(0, 1) : buttons}
        {message && <Message>{message}</Message>}
    </Container>;
}

const Container = styled.div<{ mini?: boolean }>`
  display: flex;
  align-items: center;
  flex-shrink: 0;
  gap: 6px;
  ${props => props.mini ? css`
    ${Item} {
      --bs-btn-padding-y: 0.05rem;
      --bs-btn-padding-x: 0.5rem;
      --bs-btn-font-size: 0.875rem;
      --bs-btn-border-radius: 0.25rem;
    }
  ` : css`
    ${Item} {
      --bs-btn-padding-y: 0.1rem;
      --bs-btn-padding-x: 0.5rem;
    }
  `}
`;

const Item = styled(Button).attrs(props => ({
    variant: props.variant || 'outline-primary'
}))`
`;


const Message = styled.span`
  color: #aaaaaa;
`;
