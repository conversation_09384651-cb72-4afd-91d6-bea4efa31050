import { request, Markdown, PluginMeta, useAsync, styled, isUrl } from '@topwrite/common';
import Loader from '../../components/loader';
import { ImgHTMLAttributes } from 'react';
import Anchor from '../../components/html/anchor';

interface ReadmeProps {
    meta: PluginMeta;
}

export default function Readme({ meta }: ReadmeProps) {

    const { result } = useAsync(async () => {
        const { data } = await request.get(`${meta.host}/README.md`);
        return data;
    }, [meta]);

    const Image = ({ src, ...props }: ImgHTMLAttributes<HTMLImageElement>) => {
        if (src && !isUrl(src)) {
            src = `${meta.host}/${src}`;
        }
        return <img src={src} {...props} />;
    };

    return <Container>
        {result ? <Markdown components={{ img: Image, a: Anchor }} aside={false}>{result}</Markdown> : <Loader />}
    </Container>;
}

const Container = styled.div`
  max-width: 1120px;
  margin: 0 auto;
  padding: 16px 20px;
`;
