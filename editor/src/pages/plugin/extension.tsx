import useFormatMessage from '../../lib/use-format-message';
import { pluginCenter, PluginMeta, styled } from '@topwrite/common';
import Dimmer from '../../components/dimmer';
import Item from './item';
import ScrollList from '../../components/scroll-list';
import { PaneHeader } from '../../components/pane';
import Empty from '../../components/empty';
import Selectable from './selectable';
import { getCurrentExtensions } from '../../lib/plugins';
import { Allotment } from 'allotment';

export default function Extension() {
    const t = useFormatMessage();

    const current = getCurrentExtensions();

    return <Selectable>
        <PaneContainer>
            <Allotment vertical>
                <Allotment.Pane className='d-flex flex-column' preferredSize={223} minSize={35} maxSize={500}>
                    <PaneHeader>
                        {t('side-bar.plugin.extension')}
                    </PaneHeader>
                    <ListContainer>
                        {current.length === 0 && <Empty />}
                        {current.map(meta => <Item key={meta.name} meta={meta} />)}
                    </ListContainer>
                </Allotment.Pane>
                <Allotment.Pane className='d-flex flex-column'>
                    <PaneHeader>
                        {t('side-bar.plugin.extension.title')}
                    </PaneHeader>
                    <ListContainer>
                        <ScrollList<PluginMeta>
                            fetchData={async (result) => {
                                const list = await pluginCenter.queryPlugins({
                                    type: 'extension',
                                    page: result ? result.page + 1 : 1
                                });
                                return {
                                    data: list.data,
                                    hasMore: list.current_page < list.last_page,
                                    page: list.current_page
                                };
                            }}
                            renderItem={(meta, index) => {
                                if (current.map(m => m.name).includes(meta.name)) {
                                    return null;
                                }
                                return <Item key={index} meta={meta} />;
                            }}
                        />
                    </ListContainer>
                </Allotment.Pane>
            </Allotment>
        </PaneContainer>
    </Selectable>;
}

const PaneContainer = styled.div`
    flex: auto;
    overflow: hidden;
`;

const ListContainer = styled(Dimmer.Dimmable)`
    flex: auto;
    overflow-y: auto;
`;
