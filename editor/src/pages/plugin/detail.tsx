import { css, PluginMeta, styled } from '@topwrite/common';
import { Tab, Tabs } from 'react-bootstrap';
import useFormatMessage from '../../lib/use-format-message';
import Readme from './readme';
import Actions from './actions';
import { Identifier, Paid, Statics, Type } from './badge';
import { VscCloudDownload } from 'react-icons/vsc';
import numberAverage from '../../lib/number-average';
import ForbidIcon from '../../images/forbid.svg';

interface DetailProps {
    meta: PluginMeta;
}

export default function Detail({ meta }: DetailProps) {

    const t = useFormatMessage();

    return <Container
        disabled={meta.disabled}
        message={meta.expired ? t('plugin.trial.message') : undefined}
    >
        <Header>
            <Logo>
                <img src={`${meta.host}/${meta.icon}`} />
            </Logo>
            <Info>
                <Title>
                    <Name>{meta.display_name}</Name>
                    <Identifier>{meta.name}</Identifier>
                    <Type type={meta.type} />
                    {meta.price > 0 && <Paid />}
                </Title>
                <SubTitle>
                    <Statics size='lg'>
                        <span>v{meta.version}</span>
                    </Statics>
                    <Divider />
                    <Statics size='lg'>
                        <VscCloudDownload />
                        <span>{numberAverage(meta.installs_count)}</span>
                    </Statics>
                </SubTitle>
                <Description>
                    {meta.description}
                </Description>
                <CustomActions meta={meta} />
            </Info>
        </Header>
        <Body>
            <Tabs id='plugin-detail' defaultActiveKey='readme' unmountOnExit>
                <Tab eventKey='readme' title={t('plugin.readme')}>
                    <Readme meta={meta} />
                </Tab>
            </Tabs>
        </Body>
    </Container>;
}

const Divider = styled.span`
    display: inline-block;
    width: 1px;
    background: hsla(0, 0%, 50%, .7);
    height: 100%;
    margin: 0 10px;
`;

const Logo = styled.div`
    height: 128px;
    width: 128px;
    position: relative;

    img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
`;


const Container = styled.div<{ disabled?: boolean, message?: string }>`
    height: 100%;
    display: flex;
    overflow: hidden;
    flex-direction: column;

    ${props => props.message && css`
        ${Logo}:after {
            content: '${props.message}';
            font-size: 15px;
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0);
            color: #FFFFFF;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 3px;
            opacity: .5;
        }
    `}
    ${props => props.disabled && css`
        ${Logo}:after {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0);
            background-image: url(${ForbidIcon});
            background-position: center;
            background-repeat: no-repeat;
            color: #FFFFFF;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 3px;
            opacity: .5;
        }
    `}
`;

const Header = styled.div`
    display: flex;
    flex-shrink: 0;
    padding: 20px 20px 14px;
    overflow: hidden;
    font-size: 14px;
`;

const Info = styled.div`
    flex: auto;
    padding-left: 20px;
    overflow: hidden;
    user-select: text;
`;

const Title = styled.div`
    display: flex;
    align-items: center;
`;

const Name = styled.span`
    flex: 0;
    font-size: 26px;
    line-height: 30px;
    white-space: nowrap;
    margin-right: 10px;
`;

const SubTitle = styled.div`
    margin-top: 10px;
    white-space: nowrap;
    height: 20px;
    line-height: 20px;
    display: flex;
    align-items: center;
`;

const Description = styled.div`
    margin-top: 10px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
`;

const CustomActions = styled(Actions)`
    margin-top: 10px;
`;

const Body = styled.div`
    flex: auto;
    overflow: hidden;

    .nav-tabs {
        padding-left: 20px;
    }

    .tab-content {
        height: calc(100% - 37px);

        .tab-pane {
            height: 100%;
            overflow-y: auto;
            position: relative;
        }
    }
`;
