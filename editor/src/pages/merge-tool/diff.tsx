import { socket } from '../../lib/socket';
import { useAsync } from '@topwrite/common';
import Editor from './editor';
import { useCallback } from 'react';
import Loader from '../../components/loader';

interface DiffProps {
    path: string;
    onResolve: (path: string, content: string) => void;
}

export default function Diff({ path, onResolve }: DiffProps) {

    const { result } = useAsync(async () => {

        const ours = await socket.readBlobFile(':2', path);
        const theirs = await socket.readBlobFile(':3', path);

        return {
            ours: ours.toString(),
            theirs: theirs.toString()
        };
    }, [path]);

    const handleSave = useCallback((content: string) => {
        onResolve(path, content);
    }, [path, onResolve]);

    if (!result) {
        return <Loader />;
    }

    return <Editor key={path} onSave={handleSave} ours={result.ours} theirs={result.theirs} />;
}
