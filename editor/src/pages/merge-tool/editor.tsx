import { useCallback, useMemo, useRef, useState } from 'react';
import { createEditor, Descendant } from 'slate';
import { Editable, Slate, withReact } from 'slate-react';
import Plain from '../../components/editor/serializer/plain';
import { diffLines } from 'diff';
import { Button } from 'react-bootstrap';
import confirm from '../../components/modal/confirm';
import { debounce } from 'lodash';
import { styled } from '@topwrite/common';
import useFormatMessage from '../../lib/use-format-message';
import pipe from '../../components/editor/utils/pipe';
import { withHistory } from 'slate-history';
import { Allotment } from 'allotment';

interface EditorProps {
    ours: string;
    theirs: string;
    onSave: (content: string) => void;
}

export default function Editor({ ours, theirs, onSave }: EditorProps) {
    const t = useFormatMessage();

    const [initialValue, setInitialValue] = useState<Descendant[]>(Plain.deserialize(theirs));

    const editor = useMemo(() => pipe(createEditor(), withReact, withHistory), [initialValue]);

    const [content, setContent] = useState(theirs);

    const changes = useMemo(() => {
        return diffLines(ours, content, {
            ignoreWhitespace: false,
            newlineIsToken: true
        });
    }, [ours, content]);

    const MemoEditor = useCallback<typeof Slate>(({ children, ...props }) => {
        return <Slate {...props} >{children}</Slate>;
    }, [editor]);

    const onContentChange = useCallback(debounce((value: Descendant[]) => {
        const newContent = Plain.serialize(value);
        if (newContent !== content) {
            setContent(newContent);
        }
    }, 500), [content]);

    const valueRef = useRef(initialValue);

    const handleChange = useCallback((value: Descendant[]) => {
        if (valueRef.current !== value) {
            onContentChange(value);
            valueRef.current = value;
        }
    }, [onContentChange]);

    const handleSave = useCallback(() => {
        onSave(content);
    }, [content]);

    return <Container>
        <Allotment>
            <CustomSplitPane preferredSize={'50%'}>
                <Title>
                    <span>{t('merge-tool.theirs')}</span>
                    <Button size='sm' onClick={async () => {
                        await confirm({ message: t('merge-tool.use_theirs') });
                        setContent(theirs);
                        setInitialValue(Plain.deserialize(theirs));
                    }}>{t('merge-tool.reset')}</Button>
                </Title>
                <MemoEditor editor={editor} initialValue={initialValue} onChange={handleChange}>
                    <Editable />
                </MemoEditor>
            </CustomSplitPane>
            <CustomSplitPane preferredSize={'50%'}>
                <Title>
                    <span>{t('merge-tool.ours')}</span>
                    <Button size='sm' onClick={async () => {
                        await confirm({ message: t('merge-tool.use_ours') });
                        setContent(ours);
                        setInitialValue(Plain.deserialize(ours));
                    }}>{t('merge-tool.pick')}</Button>
                </Title>
                <Changes>
                    {changes.map((change, index) => {
                        if (change.added) {
                            return null;
                        }
                        if (change.removed) {
                            return <Delete key={index}>{change.value}</Delete>;
                        }
                        return change.value;
                    })}
                </Changes>
            </CustomSplitPane>
        </Allotment>
        <Footer>
            <Button size={'sm'} onClick={handleSave}>{t('merge-tool.save')}</Button>
        </Footer>
    </Container>;
}

const Delete = styled.span`
  background-color: #ffdcdc;
`;

const CustomSplitPane = styled(Allotment.Pane)`
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;

  [data-slate-editor] {
    font-size: 15px;
    line-height: 1.8;
    padding: 16px;
    flex: auto;
    overflow-y: auto;
    outline: none;
  }
`;

const Container = styled.div`
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const Changes = styled.div`
  flex: auto;
  font-size: 15px;
  line-height: 1.8;
  padding: 16px;
  background-color: #f8f8f8;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
`;

const Title = styled.div`
    height: 37px;
    border-bottom: 1px solid var(--ttw-border-color);
    padding: 4px 4px 4px 10px;
    line-height: 28px;
    background: var(--ttw-foreground);
    display: flex;
    align-items: center;

    span {
        flex: 1;
    }
`;

const Footer = styled.div`
    border-top: 1px solid var(--ttw-border-color);
    background: var(--ttw-foreground);
    height: 37px;
    display: flex;
    align-items: center;
    padding: 4px 4px 4px 10px;
`;
