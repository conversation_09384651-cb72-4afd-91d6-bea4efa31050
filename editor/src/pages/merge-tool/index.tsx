import { useEffect, useMemo, useState } from 'react';
import { styled, useSelector } from '@topwrite/common';
import FileIcon from '../../components/file-icon';
import { GoGitMerge } from 'react-icons/go';
import Diff from './diff';
import Loader from '../../components/loader';
import Dimmer from '../../components/dimmer';
import { socket } from '../../lib/socket';
import { FileItem, FileList } from '../../components/file-list';
import Empty from '../../components/empty';
import useFormatMessage from '../../lib/use-format-message';
import { Allotment } from 'allotment';

export default function MergeTool() {

    const t = useFormatMessage();
    const { status: { changes } } = useSelector('workspace');
    const [current, setCurrent] = useState<string>();
    const [solved, setSolved] = useState<{ [index: string]: string }>({});

    const handleClick = (path: string) => {
        return () => {
            setCurrent(path);
        };
    };

    const files = useMemo(() => {
        return Object.entries(changes).filter(([_, change]) => {
            return change === 'U';
        });
    }, [changes]);

    const unsolved = useMemo(() => {
        return files.filter(([path]) => {
            return solved[path] === undefined;
        });
    }, [files, solved]);

    useEffect(() => {
        setSolved({});
    }, [files]);

    useEffect(() => {
        if (files.length > 0 && unsolved.length === 0) {
            socket.resolve(solved);
        }
    }, [unsolved]);

    const handleResolve = (path: string, content: string) => {
        setSolved({
            ...solved,
            [path]: content
        });
        setCurrent(undefined);
    };

    return <Container>
        <Header>
            <GoGitMerge className='me-1' />{t('merge-tool.header')}
        </Header>
        {unsolved.length > 0 ? <Allotment>
                <Allotment.Pane minSize={230} maxSize={900} preferredSize={230}>
                    <Title>{t('merge-tool.title', { nums: unsolved.length })}</Title>
                    <FileList>
                        {unsolved.map(([path]) => {
                            return <FileItem
                                key={path}
                                active={current === path}
                                onClick={handleClick(path)}
                                text={path}
                                space={<FileIcon filename={path} />}
                            />;
                        })}
                    </FileList>
                </Allotment.Pane>
                <Allotment.Pane>
                    {current ? <Diff path={current} onResolve={handleResolve} /> :
                        <Empty className='bg-transparent' message={t('merge-tool.empty')} />}
                </Allotment.Pane>
            </Allotment> :
            <Dimmer.Dimmable className='flex-fill'>
                <Loader />
            </Dimmer.Dimmable>
        }
    </Container>;
}

const Title = styled.div`
    height: 37px;
    border-bottom: 1px solid var(--ttw-border-color);
    padding: 4px 4px 4px 10px;
    line-height: 28px;
    background: var(--ttw-foreground);
    display: flex;
    align-items: center;
`;

const Header = styled.div`
    height: 51px;
    border-bottom: 1px solid var(--ttw-border-color);
    padding: 4px 4px 4px 10px;
    line-height: 28px;
    background: var(--ttw-foreground);
    display: flex;
    align-items: center;
`;

const Container = styled.div`
    display: flex;
    flex-direction: column;
    flex: auto;
    overflow: hidden;
`;
