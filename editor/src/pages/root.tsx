import Main from './main';
import Dimmer from '../components/dimmer';
import { useEffect, useState } from 'react';
import { socket } from '../lib/socket';
import { GoIssueOpened } from 'react-icons/go';

export default function Root() {

    const [abort, setAbort] = useState(null);

    useEffect(() => {
        socket.on('abort', (message) => {
            setAbort(message);
        });
        socket.ready();
    }, []);

    if (abort) {
        return <Dimmer>
            <GoIssueOpened className='text-secondary' size={60} />
            <p className='mt-5 text-secondary h5'> {abort}</p>
        </Dimmer>;
    }

    return <Main />;
};
