import { Book, LetterAvatar, styled, useActions, useDocumentTitle, useSelector } from '@topwrite/common';
import { ChangeEvent, FocusEvent, KeyboardEvent, useMemo, useRef, useState } from 'react';
import useFormatMessage from '../lib/use-format-message';
import { Buffer } from 'buffer';
import { socket } from '../lib/socket';
import LocalImage from '../components/local-image';
import { GoPencil } from 'react-icons/go';
import { FormControl } from 'react-bootstrap';

const defaultTitle = document.title;

export default function Title() {

    const { config } = useSelector('book');
    const [editing, setEditing] = useState(false);
    const { updateConfig } = useActions('book');
    const t = useFormatMessage();
    const inputRef = useRef<HTMLInputElement>(null);
    const title = config.getValue('title') || t('header.untitled');

    const onFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
        e.preventDefault();
        const { files } = e.target;
        if (files && files.length > 0) {
            const file = files[0];
            //重置文件
            e.target.value = '';

            let data: Buffer = await new Promise((resolve) => {
                const fileReader = new FileReader;
                const image = new Image();

                fileReader.onload = async (event) => {
                    if (event.target) {
                        image.src = event.target.result as string;
                    }
                };

                image.onload = () => {
                    const canvas = window.document.createElement('canvas');

                    // 图片原始尺寸
                    const originWidth = image.width;
                    const originHeight = image.height;

                    const size = Math.min(Math.max(originWidth, originHeight), 256);

                    canvas.width = size;
                    canvas.height = size;

                    // 最大尺寸限制
                    const maxWidth = size, maxHeight = size;
                    // 目标尺寸
                    let targetWidth, targetHeight;

                    if (originWidth / originHeight > maxWidth / maxHeight) {
                        targetWidth = maxWidth;
                        targetHeight = Math.round(maxWidth * (originHeight / originWidth));
                    } else {
                        targetHeight = maxHeight;
                        targetWidth = Math.round(maxHeight * (originWidth / originHeight));
                    }

                    const context = canvas.getContext('2d');
                    if (context) {
                        context.fillStyle = 'rgba(255, 255, 255, 0)';
                        context.drawImage(image, (maxWidth - targetWidth) / 2, (maxHeight - targetHeight) / 2, targetWidth, targetHeight);

                        canvas.toBlob(function(blob) {
                            if (blob) {
                                blob.arrayBuffer().then((arrayBuffer) => {
                                    resolve(Buffer.from(arrayBuffer));
                                });
                            }
                        }, 'image/png');
                    }
                };

                fileReader.readAsDataURL(file);
            });

            await socket.writeFile(Book.logo, data);
        }
    };

    const updateTitle = (e: FocusEvent<HTMLInputElement | HTMLTextAreaElement> | KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        if (e.type === 'keydown' && (e as KeyboardEvent<HTMLInputElement>).key !== 'Enter') {
            return;
        }

        const value = e.currentTarget.value;
        updateConfig((config) => {
            config.setValue('title', value);
        });
        setEditing(false);
    };

    const logo = useMemo(() => {
        return LetterAvatar.create(title, 60).toDataUrl();
    }, [title]);

    useDocumentTitle(`${title} | ${defaultTitle}`);

    return <Container>
        <Cover>
            <LocalImage autoReload src={Book.logo} default={logo} />
            <CoverEdit className='icon' onClick={() => {
                if (inputRef.current) {
                    inputRef.current.click();
                }
            }}>
                <GoPencil />
                <input className='d-none' ref={inputRef} type='file' accept={'image/*'} multiple={false}
                       onChange={onFileChange} />
            </CoverEdit>
        </Cover>
        <TitleContainer>
            {editing ?
                <FormControl
                    type='text'
                    onKeyDown={updateTitle}
                    defaultValue={config.getValue('title')}
                    autoFocus
                    onBlur={updateTitle}
                /> :
                <>
                    <TitleText>{title}</TitleText>
                    <TitleEdit onClick={() => setEditing(true)}><GoPencil /></TitleEdit>
                </>
            }
        </TitleContainer>
    </Container>;
};

const TitleEdit = styled.div`
    display: none;
    align-items: center;
    cursor: pointer;
    margin-left: 5px;
    color: var(--ttw-secondary-color);
`;

const TitleText = styled.div`
    flex: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
`;

const TitleContainer = styled.div`
    line-height: 40px;
    flex: auto;
    font-size: 15px;
    display: flex;
    margin-right: 10px;
    overflow: hidden;
    align-items: center;
    padding: 0 5px;

    &:hover {
        ${TitleEdit} {
            display: flex;
        }
    }
`;

const CoverEdit = styled.div`
    color: var(--ttw-secondary-color);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    text-align: center;
    line-height: 40px;
    display: none;
`;

const Cover = styled.div`
    width: 51px;
    height: 40px;
    overflow: hidden;
    margin-right: 10px;
    position: relative;
    cursor: pointer;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
        border-radius: 3px;
        width: 30px;
        height: 30px;
    }

    &:hover {
        ${CoverEdit} {
            display: block;
        }
    }
`;

const Container = styled.div`
    padding: 1px 0;
    display: flex;
    background-color: var(--ttw-foreground);
    border-bottom: 1px solid var(--ttw-border-color);
`;
