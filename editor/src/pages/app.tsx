import { IconContext } from 'react-icons';
import Button from '../components/button';
import Modal from '../components/modal';
import Root from './root';
import { ReactElement, ReactNode, useState } from 'react';
import { Book, createApplication, getPayload, useAsyncEffect } from '@topwrite/common';
import * as models from '../models';
import { createSocket } from '../lib/socket';
import Dimmer from '../components/dimmer';
import { Spinner } from 'react-bootstrap';
import { GoIssueOpened } from 'react-icons/go';
import Message from '../components/modal/message';
import dayjs from 'dayjs';
import '../scss/app.scss';
import AppTour from '../components/tour/app-tour';

interface Message {
    type: 'progress' | 'error' | 'ok';
    title: string;
    text?: string;
    actions?: ReactNode;
}

export default function App() {

    const [message, setMessage] = useState<Message>({
        type: 'progress',
        title: ''
    });

    const [app, setApp] = useState<ReactElement>();

    useAsyncEffect(async () => {
        const settings = getPayload();
        try {
            const socket = await createSocket(settings, (socket) => {
                socket.on('abort', (message) => {
                    setMessage({
                        type: 'error',
                        title: message,
                        text: ''
                    });
                });
            });

            const { version, path, ready } = await socket.getStatus();
            console.log(`[server:version] v${version}`);
            console.log(`[server:path] ${path}`);

            const createApp = async function() {
                try {
                    const { id, workspace, metadata, options, pluginCenter } = await socket.getState();
                    const config = await socket.getConfig();
                    const summary = await socket.getSummary();
                    const book = new Book(id, config, summary, metadata);
                    const initialState = {
                        workspace,
                        options
                    };
                    const Application = await createApplication({
                        name: 'editor',
                        book,
                        models,
                        initialState,
                        localize: async (locale) => {
                            if (locale.startsWith('zh')) {
                                await import(`dayjs/locale/zh-cn`);
                                dayjs.locale('zh-cn');
                                Modal.setLocale('zh-CN');
                                return import('../lang/zh-CN.json');
                            } else {
                                Modal.setLocale('en');
                                return import('../lang/en.json');
                            }
                        },
                        pluginCenter
                    });
                    setApp(<Application>
                        <IconContext.Provider value={{ className: 'bi' }}>
                            <AppTour>
                                <Root />
                            </AppTour>
                            <Message />
                        </IconContext.Provider>
                    </Application>);
                } catch (e: any) {
                    setMessage({
                        type: 'error',
                        title: 'Workspace init failed',
                        actions: <div className={'d-flex gap-3'}>
                            <Button variant='secondary' onClick={() => {
                                window.location.reload();
                            }}>刷新</Button>
                            <Button variant='danger' onClick={async () => {
                                const res = await Modal.confirm({
                                    title: '重置',
                                    message: '此操作将会清空编辑区未提交的内容，可先尝试刷新，确定要重置吗？'
                                });
                                if (res) {
                                    await socket.reset();
                                    window.location.reload();
                                }
                            }}>重置</Button>
                        </div>
                    });
                }
            };

            if (!ready) {
                socket.on('repo.cloning', (msg: string) => {
                    setMessage({
                        type: 'progress',
                        title: 'Init Workspace...',
                        text: msg
                    });
                });
                socket.once('repo.cloned', createApp);
            } else {
                await createApp();
            }
        } catch (e: any) {
            setMessage({
                type: 'error',
                title: e.message,
            });
        }
    }, []);

    if (app) {
        return app;
    }

    return <Dimmer>
        <Message />
        {message.type === 'progress' ? <Spinner variant='primary' animation='border' /> :
            <GoIssueOpened className='text-secondary' size={60} />}
        <p className='mt-5 text-secondary h5'>{message.title}</p>
        <p className='mt-3 text-secondary'>{message.text}</p>
        {message.actions}
    </Dimmer>;
}
