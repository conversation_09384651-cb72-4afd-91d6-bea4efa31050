import { useDrag } from 'react-dnd';
import { SummaryPart } from '@topwrite/common';
import { RefObject } from 'react';

export const PartType = Symbol('catalog.part');

export default function useDragPart(part: SummaryPart, ref: RefObject<HTMLElement>) {
    const [{ isDragging }, drag, preview] = useDrag({
        type: PartType,
        item: { level: part.level },
        collect: monitor => ({
            isDragging: monitor.isDragging(),
        }),
    });

    drag(ref);

    return { isDragging, preview };
}
