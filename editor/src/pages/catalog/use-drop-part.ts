import { useDrop } from 'react-dnd';
import { Level, SummaryArticle, SummaryPart, useActions } from '@topwrite/common';
import { RefObject, useState } from 'react';
import { ItemType } from './use-drag-item';
import { PartType } from './use-drag-part';

interface CollectedProps {
    isOver: boolean;
    canDrop: boolean;
    itemType: string | symbol | null;
}

type Position = 'middle' | 'top' | 'bottom';

interface Options {
    canDrop?: (part: SummaryPart) => boolean;
}

export default function useDropPart(part: SummaryPart, ref: RefObject<HTMLElement>, options: Options) {

    const { updateSummary } = useActions('book');
    const [position, setPosition] = useState<Position>('bottom');

    const [{ isOver, canDrop, itemType }, drop] = useDrop<SummaryPart, void, CollectedProps>({
        accept: [ItemType, PartType],
        collect: monitor => ({
            isOver: monitor.isOver({ shallow: true }),
            canDrop: monitor.canDrop(),
            itemType: monitor.getItemType()
        }),
        canDrop: ({ level }, monitor) => {
            if (options.canDrop && !options.canDrop(part)) {
                return false;
            }

            switch (monitor.getItemType()) {
                case PartType:
                    return !level.eq(part.level);
                case ItemType:
                    return !part.level.isDescendant(level);
            }
            return true;
        },
        hover: (_, monitor) => {
            if (!ref.current || monitor.getItemType() !== PartType || !monitor.canDrop()) {
                return;
            }

            const hoverBoundingRect = ref.current.getBoundingClientRect();

            const clientOffset = monitor.getClientOffset();
            if (!clientOffset) {
                return;
            }
            let position: Position;
            if (clientOffset.y - hoverBoundingRect.top < hoverBoundingRect.height / 2) {
                position = 'top';
            } else {
                position = 'bottom';
            }

            setPosition(position);
        },
        drop: ({ level }, monitor) => {
            if (!monitor.didDrop()) {
                if (monitor.getItemType() === ItemType) {
                    updateSummary(summary => {
                        const article = summary.getByLevel(level);
                        if (article instanceof SummaryArticle) {
                            summary.insertArticle(article, part.createChildLevel());
                            summary.removeArticle(level);
                        }
                    });
                } else {
                    switch (position) {
                        case 'top':
                            updateSummary(summary => {
                                const item = summary.getByLevel(level);
                                if (item instanceof SummaryPart) {
                                    if (Level.compare(part.level, level) < 0) {
                                        summary.removePart(level);
                                        summary.insertPart(item, part.level);
                                    } else {
                                        summary.insertPart(item, part.level);
                                        summary.removePart(level);
                                    }
                                }
                            });
                            break;
                        case 'bottom':
                            updateSummary(summary => {
                                const item = summary.getByLevel(level);
                                if (item instanceof SummaryPart) {
                                    const nextLevel = part.level.getNext();
                                    if (Level.compare(nextLevel, level) < 0) {
                                        summary.removePart(level);
                                        summary.insertPart(item, nextLevel);
                                    } else {
                                        summary.insertPart(item, nextLevel);
                                        summary.removePart(level);
                                    }
                                }
                            });
                            break;
                    }
                }
            }
        }
    }, [part, options]);

    drop(ref);

    return { isOver, canDrop, itemType, position };
}
