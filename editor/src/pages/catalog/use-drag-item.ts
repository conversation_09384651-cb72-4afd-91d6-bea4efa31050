import { SummaryArticle } from '@topwrite/common';
import { useDrag } from 'react-dnd';
import { RefObject } from 'react';

export const ItemType = Symbol('catalog.item');

export default function useDragItem(item: SummaryArticle, ref: RefObject<HTMLElement>) {
    const [{ isDragging }, drag] = useDrag({
        type: ItemType,
        item: { level: item.level },
        collect: monitor => ({
            isDragging: monitor.isDragging(),
        }),
    });

    drag(ref);

    return { isDragging };
}
