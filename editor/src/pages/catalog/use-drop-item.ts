import { useDrop } from 'react-dnd';
import { useActions, Level, SummaryArticle } from '@topwrite/common';
import { RefObject, useState } from 'react';
import { ItemType } from './use-drag-item';

interface CollectedProps {
    isOver: boolean;
    canDrop: boolean;
}

type Position = 'middle' | 'top' | 'bottom';

export default function useDropItem(item: SummaryArticle, ref: RefObject<HTMLElement>) {

    const { updateSummary } = useActions('book');
    //拖动排序
    const [position, setPosition] = useState<Position>('middle');

    const [{ isOver, canDrop }, drop] = useDrop<SummaryArticle, void, CollectedProps>({
        accept: ItemType,
        collect: monitor => ({
            isOver: monitor.isOver(),
            canDrop: monitor.canDrop(),
        }),
        canDrop: ({ level }) => {
            return !level.eq(item.level) && !level.isDescendant(item.level);
        },
        hover: (_, monitor) => {
            if (!ref.current || !monitor.canDrop()) {
                return;
            }
            const hoverBoundingRect = ref.current.getBoundingClientRect();
            const clientOffset = monitor.getClientOffset();
            if (!clientOffset) {
                return;
            }
            let position: Position = 'middle';
            if (clientOffset.y - hoverBoundingRect.top < 10) {
                position = 'top';
            } else if (clientOffset.y - hoverBoundingRect.top > 20) {
                position = 'bottom';
            }
            setPosition(position);
        },
        drop: ({ level }) => {
            switch (position) {
                case 'middle':
                    updateSummary(summary => {
                        const article = summary.getByLevel(level);
                        if (article instanceof SummaryArticle) {
                            summary.insertArticle(article, item.createChildLevel());
                            summary.removeArticle(level);
                        }
                    });
                    break;
                case 'top':
                    updateSummary(summary => {
                        const article = summary.getByLevel(level);
                        if (article instanceof SummaryArticle) {
                            if (Level.compare(item.level, level) < 0) {
                                summary.removeArticle(level);
                                summary.insertArticle(article, item.level);
                            } else {
                                summary.insertArticle(article, item.level);
                                summary.removeArticle(level);
                            }
                        }
                    });
                    break;
                case 'bottom':
                    updateSummary(summary => {
                        const article = summary.getByLevel(level);
                        if (article instanceof SummaryArticle) {
                            const nextLevel = item.level.getNext();
                            if (Level.compare(nextLevel, level) < 0) {
                                summary.removeArticle(level);
                                summary.insertArticle(article, nextLevel);
                            } else {
                                summary.insertArticle(article, nextLevel);
                                summary.removeArticle(level);
                            }
                        }
                    });
                    break;
            }
        }
    });

    drop(ref);

    return { isOver, canDrop, position };
}
