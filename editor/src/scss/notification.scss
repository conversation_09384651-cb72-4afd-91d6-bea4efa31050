.notification {
  position: fixed;
  z-index: 1100;

  &-notice {
    border-radius: 3px 3px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    border: 0 solid rgba(0, 0, 0, 0);;
    background: #fff;
    display: block;
    width: auto;
    line-height: 1.5;
    position: relative;
    margin: 10px 0;
    max-width: 300px;
    white-space: pre-wrap;

    &-close {
      position: absolute;
      right: 5px;
      top: 3px;
      color: #000;
      cursor: pointer;
      outline: none;
      font-size: 16px;
      font-weight: 700;
      line-height: 1;
      text-shadow: 0 1px 0 #fff;
      filter: alpha(opacity=20);
      opacity: .2;
      text-decoration: none;

      &-x:after {
        content: '×';
      }

      &:hover {
        opacity: 1;
        filter: alpha(opacity=100);
        text-decoration: none;
      }
    }
  }

  @mixin fade-effect {
    animation-duration: 0.3s;
    animation-fill-mode: both;
    animation-timing-function: cubic-bezier(0.55, 0, 0.55, 0.2);
  }

  &-fade-appear,
  &-fade-enter {
    opacity: 0;

    @include fade-effect;
    animation-play-state: paused;
  }

  &-fade-leave {
    @include fade-effect;
    animation-play-state: paused;
  }

  &-fade-appear#{&}-fade-appear-active,
  &-fade-enter#{&}-fade-enter-active {
    animation-name: rcNotificationFadeIn;
    animation-play-state: running;
  }

  &-fade-leave#{&}-fade-leave-active {
    animation-name: rcDialogFadeOut;
    animation-play-state: running;
  }

  @keyframes rcNotificationFadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  @keyframes rcDialogFadeOut {
    0% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }
}
