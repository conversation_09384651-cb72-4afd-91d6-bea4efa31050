import { <PERSON><PERSON>er } from 'buffer';
import { debounce } from 'lodash';
import { forwardRef, memo, ReactNode, useCallback, useMemo, useRef, useState } from 'react';
import { VscError } from 'react-icons/vsc';
import { Descendant, Editor as SlateEditor } from 'slate';
import File from '../../entities/file';
import useFormatMessage from '../../lib/use-format-message';
import Button from '../button';
import Empty from '../empty';
import EditorTour from '../tour/editor-tour';
import Document from './editable/document';
import Raw from './editable/raw';
import HoverBar from './hover-bar';
import ModeProvider, { useMode } from './mode-provider';
import Plate from './plate';
import ToolBar from './tool-bar';

interface EditorProps {
    file: File;
    onChange: (buf: Buffer) => void;
}

const Editor = memo(forwardRef<SlateEditor, EditorProps>(({ file, onChange }, ref) => {
    const { isDocument, serializer, plugins, withEditor, toggle } = useMode();
    const t = useFormatMessage();

    const [content, setContent] = useState(() => file.content.toString());

    const initialValue = useMemo<Descendant[] | false>(() => {
        try {
            return serializer.deserialize(content);
        } catch (e) {
            console.error(e);
            return false;
        }
    }, []);

    const onContentChange = useCallback(debounce((value: Descendant[]) => {
        setContent((prevContent) => {
            const nextContent = serializer.serialize(value);
            if (nextContent !== prevContent) {
                onChange(Buffer.from(nextContent));
            }
            return nextContent;
        });
    }, 500), [serializer, onChange]);

    const valueRef = useRef(initialValue);

    const handleChange = useCallback((value: Descendant[]) => {
        if (valueRef.current !== value) {
            onContentChange(value);
            valueRef.current = value;
        }
    }, [onContentChange]);

    const renderEditable = useCallback((editable: ReactNode) => {
        return isDocument ? <Document editable={editable} /> : <Raw editable={editable} />;
    }, [isDocument]);

    if (initialValue === false) {
        return <Empty message={t('editor.markdown.error')} icon={<VscError />}>
            <Button onClick={toggle} variant={'light'}>{t('editor.markdown.switch')}</Button>
        </Empty>;
    }

    return <Plate
        ref={ref}
        initialValue={initialValue}
        plugins={plugins}
        withEditor={withEditor}
        onChange={handleChange}
        renderEditable={renderEditable}
        placeholder={t('editor.placeholder')}
    >
        <ToolBar />
        <HoverBar />
    </Plate>;
}), () => {
    return true;
});

const ModeEditor = forwardRef<SlateEditor, EditorProps>((props, ref) => {
    return <EditorTour>
        <ModeProvider file={props.file}>
            <Editor key={props.file.ctime} {...props} ref={ref} />
        </ModeProvider>
    </EditorTour>;
});

export default ModeEditor;
