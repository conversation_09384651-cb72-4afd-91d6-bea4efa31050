import { css, styled } from '@topwrite/common';
import { ReactComponent as PlusIcon } from '../../../images/plus.svg';
import { MouseEventHandler, useCallback } from 'react';
import { insertNodes } from '../transforms';
import useEditor from '../use-editor';
import { Path } from 'slate';

interface Props {
    path: Path;
    bottom?: boolean;
}

export default function Infix({ path, bottom }: Props) {
    const editor = useEditor();

    const onMouseDown = useCallback<MouseEventHandler>((e) => {
        e.preventDefault();
        const at = bottom ? Path.next(path) : path;
        insertNodes(editor, {
            type: 'paragraph',
            children: [{ text: '' }]
        }, { at, select: true });
    }, [path, editor]);

    return <Container bottom={bottom} contentEditable={false}>
        <Line>
            <Icon onMouseDown={onMouseDown}><PlusIcon /></Icon>
        </Line>
    </Container>;
}


const Icon = styled.div`
    position: absolute;
    top: -9px;
    left: calc(50% - 10px);
    cursor: pointer;
    width: 20px;
    height: 20px;
    background: var(--ttw-background);
    border-radius: 10px;
    border: 1px solid var(--ttw-box-hover-background);
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
        width: 14px;
        height: 14px;
        fill: var(--ttw-box-hover-background);
    }

    &:hover {
        background: var(--ttw-box-active-background);
    }
`;

const Line = styled.div`
    background-color: var(--ttw-box-hover-background);
    position: absolute;
    height: 2px;
    top: 3px;
    right: 0;
    left: 0;
    display: none;
`;

const Container = styled.div<{ bottom?: boolean }>`
    height: 8px;
    position: absolute;
    left: 0;
    right: 0;
    user-select: none;
    top: calc(-.5em - 4px);

    ${props => props.bottom && css`
        top: auto;
        bottom: calc(-.5em - 4px);
    `}
    &:hover {
        ${Line} {
            display: block;
        }
    }
`;
