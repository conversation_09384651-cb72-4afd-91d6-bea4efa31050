import { MouseEventHand<PERSON>, ReactNode, useCallback, } from 'react';
import { css, styled } from '@topwrite/common';
import { useContext } from './context';

export interface ItemProps {
    icon?: ReactNode;
    eventKey?: string;
    text?: string;
    onSelect?: () => void;
}

const Item = ({ icon, text, eventKey, onSelect }: ItemProps) => {

    const { activeKey, onItemSelect, setOpen } = useContext();

    const onMouseDown = useCallback<MouseEventHandler>((e) => {
        e.preventDefault();
        if (onSelect) {
            setOpen(false);
            onSelect();
        } else if (onItemSelect && eventKey) {
            onItemSelect(eventKey);
        }
    }, [onItemSelect, eventKey]);

    return <Container onMouseDown={onMouseDown} active={!!eventKey && activeKey === eventKey}>
        <Icon>{icon}</Icon>
        <Text>{text}</Text>
    </Container>;
};

export default Item;


const Text = styled.div`
    height: 28px;
    line-height: 28px;
    flex: auto;
    margin-left: 10px;
`;

const Icon = styled.div`
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
        width: 18px;
        height: 18px;
        fill: currentColor;
    }
`;

const Container = styled.div<{ active: boolean }>`
    display: flex;
    align-items: center;
    padding: 5px 10px 5px 10px;
    cursor: pointer;

    &:hover {
        background-color: var(--ttw-box-hover-background);
    }

    ${props => props.active && css`
        background-color: var(--ttw-box-active-background);
    `}
`;
