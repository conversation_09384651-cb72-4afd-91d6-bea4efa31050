import { Overlay, PopoverProps } from 'react-bootstrap';
import { Editor, Element } from 'slate';
import { css, styled } from '@topwrite/common';
import {
    ComponentType,
    forwardRef,
    HTMLAttributes,
    MouseEventHandler,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import { ReactComponent as MenuIcon } from '../../../images/menu.svg';
import Tooltip from '../../tooltip';
import useEditor from '../use-editor';
import { ReactEditor } from 'slate-react';
import useHover from '@react-hook/hover';
import { findPath, isLastChild } from '../queries';
import Infix from './infix';
import Item, { ItemProps } from './item';
import { Provider } from './context';
import { removeNodes } from '../transforms';
import { BsTrash } from 'react-icons/bs';
import useFormatMessage from '../../../lib/use-format-message';

export interface ElementMenuProps extends HTMLAttributes<HTMLDivElement> {
    element: Element;
    activeKey?: string;
    infix?: boolean;
    as?: string | ComponentType<any>;
    items?: ItemProps[] | false;
    onItemSelect?: (eventKey: string) => void;
}

const ElementMenu = forwardRef<HTMLDivElement, ElementMenuProps>((
    {
        element,
        children,
        infix = true,
        onItemSelect,
        activeKey,
        items = [],
        ...props
    },
    ref
) => {
    const t = useFormatMessage();
    const editor = useEditor();
    const path = findPath(editor, element);
    const isLast = useMemo(() => {
        const parent = Editor.parent(editor, path);
        return isLastChild(parent, path);
    }, [editor, path]);

    const [open, setOpen] = useState(false);

    const onMouseDown = useCallback<MouseEventHandler>((e) => {
        e.preventDefault();
    }, [open]);

    const onMouseUp = useCallback<MouseEventHandler>((e) => {
        e.preventDefault();
        setOpen(!open);
    }, [open]);

    const [target, setTarget] = useState<HTMLElement | null>(null);

    useEffect(() => {
        setTarget(ReactEditor.toDOMNode(editor, element));
    }, []);

    const triggerRef = useRef(null);
    const hovering = useHover(target, { leaveDelay: 300, enterDelay: 300 });
    const triggerHovering = useHover(triggerRef, { leaveDelay: 300 });

    const onHide = useCallback(() => {
        setOpen(false);
    }, []);

    const handleItemSelect = useCallback((eventKey: string) => {
        setOpen(false);
        if (onItemSelect) {
            onItemSelect(eventKey);
        }
    }, [onItemSelect]);

    return <Container ref={ref} {...props}>
        {infix && <Infix path={path} />}
        {infix && isLast && <Infix path={path} bottom />}
        {children}
        {items && <>
            <Tooltip tooltip={'editor.menu.tooltip'} placement={'top'}>
                <Toggle
                    show={hovering || triggerHovering || open}
                    active={open}
                    onMouseDown={onMouseDown}
                    onMouseUp={onMouseUp}
                    ref={triggerRef}
                    contentEditable={false}
                    depth={path.length}
                >
                    <MenuIcon />
                </Toggle>
            </Tooltip>
            <Overlay
                target={triggerRef}
                onHide={onHide}
                show={open}
                rootClose
                rootCloseEvent={'mousedown'}
                placement={'bottom-start'}
                offset={[0, 8]}
            >
                <Menu>
                    <Provider activeKey={activeKey} setOpen={setOpen} onItemSelect={handleItemSelect}>
                        {items.map((item, index) => <Item key={item.eventKey || index} {...item} />)}
                        {items.length > 0 && <Divider />}
                        <Item
                            onSelect={useCallback(() => {
                                const at = findPath(editor, element);
                                removeNodes(editor, { at });
                            }, [editor, element])}
                            icon={<BsTrash />}
                            text={t('editor.menu.delete')}
                        />
                    </Provider>
                </Menu>
            </Overlay>
        </>}
    </Container>;
});

export default ElementMenu;

export const Divider = styled.hr`
    margin: .5rem 0;
    overflow: hidden;
    border-color: var(--ttw-secondary-color);
`;

const Menu = forwardRef<HTMLDivElement, PopoverProps>(({ popper, ...props }, ref) => {
    useEffect(() => {
        window.requestAnimationFrame(() => {
            popper?.scheduleUpdate?.();
        });
    }, []);
    return <MenuContainer ref={ref} {...props} />;
});

const Container = styled.div`
    position: relative;
`;

const MenuContainer = styled.div`
    position: absolute;
    display: block;
    padding: 0.5rem 0;
    color: var(--ttw-box-color);
    background: var(--ttw-box-background);
    line-height: 1.5;
    box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%);
    border-radius: .25rem;
    min-width: 14rem;
    user-select: none;
`;

const Toggle = styled.div<{ show: boolean, active: boolean, depth: number }>`
    position: absolute;
    left: ${props => -Math.max((43 - (props.depth - 1) * 5), 24)}px;
    top: 0;
    width: 24px;
    height: 24px;
    border: 1px solid var(--ttw-border-color);
    border-radius: 12px;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: var(--ttw-box-background);
    display: flex;
    user-select: none;
    visibility: hidden;

    svg {
        width: 14px;
        height: 14px;
        fill: var(--ttw-secondary-color);
    }

    ${props => props.show && css`
        visibility: visible;
    `}
    ${props => props.active && css`
        svg {
            fill: var(--bs-primary);
        }
    `}
    &:hover {
        background: var(--ttw-box-hover-background);
    }
`;
