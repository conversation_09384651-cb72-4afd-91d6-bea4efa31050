import { create<PERSON>ontext, PropsWithChildren, useContext as useReactContext } from 'react';

interface ContextType {
    activeKey?: string;
    onItemSelect?: (eventKey: string) => void;
    setOpen: (open: boolean) => void;
}

const Context = createContext<ContextType | undefined>(undefined);

export const useContext = () => {
    const context = useReactContext(Context);

    if (!context) {
        throw new Error;
    }
    return context;
};

export const Provider = ({ children, ...value }: PropsWithChildren<ContextType>) => {

    return <Context.Provider value={value}>
        {children}
    </Context.Provider>;
};
