import { Editor, Node, NodeEntry } from 'slate';
import { getQueryOptions, WithQueryOptions } from '../utils/match';
import { unhangRange, UnhangRangeOptions } from '../transforms/unhang-range';

export function getNodes<T extends Node = Node>(editor: Editor, options?: WithQueryOptions<typeof Editor.nodes, 1> & UnhangRangeOptions): Generator<NodeEntry<T>, void, undefined> {
    unhangRange(editor, options);

    return Editor.nodes(editor, getQueryOptions(editor, options));
}
