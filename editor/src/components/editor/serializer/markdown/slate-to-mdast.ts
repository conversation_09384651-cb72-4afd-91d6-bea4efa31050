import {
    BaseElement,
    BlockquoteElement,
    CodeElement,
    ContainerDirectiveElement,
    CustomText,
    DecorationType,
    DefinitionElement,
    Descendant,
    DirectiveLabelElement,
    Element,
    FootnoteDefinitionElement,
    FootnoteElement,
    FootnoteReferenceElement,
    HtmlElement,
    ImageElement,
    ImageReferenceElement,
    LeafDirectiveElement,
    LinkElement,
    LinkReferenceElement,
    ListElement,
    ListItemElement,
    Node,
    ParagraphElement,
    TableCellElement,
    TableElement,
    TableRowElement,
    Text,
    TextDirectiveElement,
    ThematicBreakElement,
    XBlockElement,
    XInlineElement,
    YamlElement
} from 'slate';
import * as mdast from 'mdast';

type TextOrDecoration =
    | mdast.Text
    | mdast.Emphasis
    | mdast.Strong
    | mdast.Delete
    | mdast.InlineCode;

export function slateToMdast(element: BaseElement): mdast.Root {
    return {
        type: 'root',
        children: convertNodes(element.children),
    };
}

function convertNodes(nodes: Descendant[]): mdast.Content[] {
    const mdastNodes: mdast.Content[] = [];
    let textQueue: CustomText[] = [];
    for (let i = 0; i <= nodes.length; i++) {
        const n = nodes[i];
        if (n && Text.isText(n)) {
            textQueue.push(n);
        } else {
            const mdastTexts: TextOrDecoration[] = [];
            const starts: DecorationType[] = [];
            let textTemp: string = '';
            for (let j = 0; j < textQueue.length; j++) {
                const cur = textQueue[j];
                textTemp += cur.text;

                const prev = textQueue[j - 1];
                const next = textQueue[j + 1];
                const ends: DecorationType[] = [];
                if (cur.code) {
                    if (!prev || !prev.code) {
                        starts.push('code');
                    }
                    if (!next || !next.code) {
                        ends.push('code');
                    }
                }
                if (cur.emphasis) {
                    if (!prev || !prev.emphasis) {
                        starts.push('emphasis');
                    }
                    if (!next || !next.emphasis) {
                        ends.push('emphasis');
                    }
                }
                if (cur.strong) {
                    if (!prev || !prev.strong) {
                        starts.push('strong');
                    }
                    if (!next || !next.strong) {
                        ends.push('strong');
                    }
                }
                if (cur.delete) {
                    if (!prev || !prev.delete) {
                        starts.push('delete');
                    }
                    if (!next || !next.delete) {
                        ends.push('delete');
                    }
                }
                if (starts.length > 0) {
                    let res: TextOrDecoration = {
                        type: 'text',
                        value: textTemp,
                    };
                    textTemp = '';
                    const startsReversed = starts.slice().reverse();
                    startsReversed.forEach((k) => {
                        if (k === 'code') {
                            res = {
                                type: 'inlineCode',
                                value: (res as any).value,
                            };
                        } else {
                            res = {
                                type: k,
                                children: [res],
                            };
                        }
                    });
                    mdastTexts.push(res);
                }

                if (starts.length > 0 && ends.length > 0) {
                    const endsToRemove = starts.reduce<{ key: DecorationType; index: number }[]>((acc, k, kIndex) => {
                        if (ends.includes(k)) {
                            acc.push({ key: k, index: kIndex });
                        }
                        return acc;
                    }, []);

                    endsToRemove.reverse().forEach((e) => {
                        starts.splice(e.index, 1);
                    });
                } else {
                    mdastTexts.push({ type: 'text', value: textTemp });
                    textTemp = '';
                }
            }
            if (textTemp) {
                mdastTexts.push({ type: 'text', value: textTemp });
                textTemp = '';
            }

            mdastNodes.push(...mergeTexts(mdastTexts));
            textQueue = [];
            if (!n) continue;
            const node = createMdastNode(n);
            if (node) {
                mdastNodes.push(node);
            }
        }
    }

    return mdastNodes;
}

function createMdastNode(node: Element): mdast.Content | null {
    switch (node.type) {
        case 'paragraph':
            return createParagraph(node);
        case 'thematicBreak':
            return createThematicBreak(node);
        case 'blockquote':
            return createBlockquote(node);
        case 'list':
            return createList(node);
        case 'listItem':
            return createListItem(node);
        case 'table':
            return createTable(node);
        case 'tableRow':
            return createTableRow(node);
        case 'tableCell':
            return createTableCell(node);
        case 'html':
            return createHtml(node);
        case 'yaml':
            if (node.value) {
                //过滤掉空
                return createYaml(node);
            }
            break;
        case 'code':
            return createCode(node);
        case 'definition':
            return createDefinition(node);
        case 'footnoteDefinition':
            return createFootnoteDefinition(node);
        case 'link':
            return createLink(node);
        case 'image':
            return createImage(node);
        case 'linkReference':
            return createLinkReference(node);
        case 'imageReference':
            return createImageReference(node);
        case 'footnote':
            return createFootnote(node);
        case 'footnoteReference':
            return creatFootnoteReference(node);
        case 'xBlock':
            return createXBlock(node);
        case 'xInline':
            return createXInline(node);
        case 'textDirective':
            return createTextDirective(node);
        case 'leafDirective':
            return createLeafDirective(node);
        case 'containerDirective':
            return createContainerDirective(node);
        case 'directiveLabel':
            return createDirectiveLabel(node);
        default:
            break;
    }
    return null;
}

function mergeTexts(nodes: TextOrDecoration[]): TextOrDecoration[] {
    const res: TextOrDecoration[] = [];
    for (const cur of nodes) {
        const last = res[res.length - 1];
        if (last && last.type === cur.type) {
            if (last.type === 'text') {
                last.value += (cur as typeof last).value;
            } else if (last.type === 'inlineCode') {
                last.value += (cur as typeof last).value;
            } else {
                last.children = mergeTexts(
                    last.children.concat(
                        (cur as typeof last).children
                    ) as TextOrDecoration[]
                );
            }
        } else {
            if (cur.type === 'text' && cur.value === '') continue;
            res.push(cur);
        }
    }
    return res;
}

function createParagraph(node: ParagraphElement): mdast.Paragraph | mdast.Heading | mdast.BlankLine {
    const { depth = undefined, children, align } = node;
    const nodes = convertNodes(children) as mdast.Paragraph['children'];
    if (depth) {
        return {
            type: 'heading',
            depth,
            align,
            children: nodes,
        };
    } else {
        if (nodes.length === 0) {
            return {
                type: 'blankLine'
            };
        }

        return {
            type: 'paragraph',
            align,
            children: nodes,
        };
    }
}

function createThematicBreak(node: ThematicBreakElement): mdast.ThematicBreak {
    const { type } = node;
    return {
        type,
    };
}

function createBlockquote(node: BlockquoteElement): mdast.Blockquote {
    const { type, variant, children } = node;
    return {
        type,
        variant,
        children: convertNodes(children) as mdast.Blockquote['children'],
    };
}

function createList(node: ListElement): mdast.List {
    const { type, ordered, start, spread = false, children } = node;

    return {
        type,
        ordered,
        start,
        spread,
        children: convertNodes(children) as mdast.List['children'],
    };
}

function createListItem(node: ListItemElement): mdast.ListItem {
    const { type, checked, spread = false, children } = node;
    return {
        type,
        checked,
        spread,
        children: convertNodes(children) as mdast.ListItem['children'],
    };
}

function createTable(node: TableElement): mdast.Table {
    const { type, align, children } = node;
    return {
        type,
        align,
        children: convertNodes(children) as mdast.Table['children'],
    };
}

function createTableRow(node: TableRowElement): mdast.TableRow {
    const { type, children } = node;
    return {
        type,
        children: convertNodes(children) as mdast.TableRow['children'],
    };
}

function createTableCell(node: TableCellElement): mdast.TableCell {
    const { type, children } = node;
    return {
        type,
        children: convertNodes(children) as mdast.TableCell['children'],
    };
}

function createHtml(node: HtmlElement): mdast.HTML {
    const { type, children } = node;
    return {
        type,
        value: children[0].text,
    };
}

function createYaml(node: YamlElement): mdast.YAML {
    const { type, value } = node;
    return {
        type,
        value
    };
}

function createCode(node: CodeElement): mdast.Code {
    const { type, lang, meta, children } = node;
    return {
        type,
        lang,
        meta,
        value: children.map(c => Node.string(c)).join('\n')
    };
}

function createDefinition(node: DefinitionElement): mdast.Definition {
    const { type, identifier, label, url, title } = node;
    return {
        type,
        identifier,
        label,
        url,
        title,
    };
}

function createFootnoteDefinition(node: FootnoteDefinitionElement): mdast.FootnoteDefinition {
    const { type, identifier, label, children } = node;
    return {
        type,
        identifier,
        label,
        children: convertNodes(children) as mdast.FootnoteDefinition['children'],
    };
}

function createLink(node: LinkElement): mdast.Link {
    const { type, url, title, value } = node;

    return {
        type,
        url,
        title,
        children: [{
            type: 'text',
            value
        }],
    };
}

function createImage(node: ImageElement): mdast.Image | mdast.Link {
    const { type, url, title, alt, width, height, link } = node;

    const image = {
        type,
        url,
        title,
        alt,
        width,
        height
    };

    if (link) {
        return {
            type: 'link',
            url: link,
            children: [image]
        };
    }

    return image;
}

function createLinkReference(node: LinkReferenceElement): mdast.LinkReference {
    const { type, identifier, label, referenceType, children } = node;
    return {
        type,
        identifier,
        label,
        referenceType,
        children: convertNodes(children) as mdast.LinkReference['children'],
    };
}

function createImageReference(node: ImageReferenceElement): mdast.ImageReference {
    const { type, identifier, label, alt, referenceType } = node;
    return {
        type,
        identifier,
        label,
        alt,
        referenceType,
    };
}

function createFootnote(node: FootnoteElement): mdast.Footnote {
    const { type, children } = node;
    return {
        type,
        children: convertNodes(children) as mdast.Footnote['children'],
    };
}

function creatFootnoteReference(node: FootnoteReferenceElement): mdast.FootnoteReference {
    const { type, identifier, label } = node;
    return {
        type,
        identifier,
        label,
    };
}

function createXBlock(node: XBlockElement): mdast.Code {
    const { name, parameter, value } = node;

    return {
        type: 'code',
        lang: `[${name}${parameter ? `:${parameter}` : ''}]`,
        value
    };
}

function createXInline(node: XInlineElement): mdast.InlineCode {
    const { name, value } = node;
    return {
        type: 'inlineCode',
        value: `${name} ${value} ${name}`
    };
}

function createTextDirective(node: TextDirectiveElement): mdast.TextDirective {
    const { name, attributes, children } = node;
    return {
        type: 'textDirective',
        name,
        attributes,
        children: convertNodes(children) as mdast.TextDirective['children'],
    };
}

function createLeafDirective(node: LeafDirectiveElement): mdast.LeafDirective {
    const { name, attributes, children } = node;
    return {
        type: 'leafDirective',
        name,
        attributes,
        children: convertNodes(children) as mdast.TextDirective['children'],
    };
}

function createContainerDirective(node: ContainerDirectiveElement): mdast.ContainerDirective {
    const { name, attributes, children } = node;

    const nodes = convertNodes(children);

    return {
        type: 'containerDirective',
        name,
        attributes,
        children: nodes as mdast.ContainerDirective['children'],
    };
}

function createDirectiveLabel(node: DirectiveLabelElement): mdast.Paragraph {
    const { children } = node;
    return {
        type: 'paragraph',
        data: { directiveLabel: true },
        children: convertNodes(children) as mdast.Paragraph['children'],
    };
}
