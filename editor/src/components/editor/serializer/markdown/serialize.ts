import stringify from 'remark-stringify';
import { markdown } from '@topwrite/common';
import { slateToMdast } from './slate-to-mdast';
import { Descendant } from 'slate';

export default (nodes: Descendant[]) => {

    const md = markdown()
    .use(stringify, {
        bullet: '*',
        listItemIndent: 'one',
        fences: true,
        join: [(left, right) => {
            if (left.type === 'blankLine' && left.type === right.type) {
                return 0;
            }
        }]
    });

    const root = slateToMdast({
        children: nodes,
    });

    return md.stringify(root);
}
