import {
    BlankLine,
    Blockquote,
    Code,
    ContainerDirective,
    Content,
    Definition,
    Footnote,
    FootnoteDefinition,
    FootnoteReference,
    Heading,
    HTML,
    Image,
    ImageReference,
    LeafDirective,
    Link,
    LinkReference,
    List,
    ListItem,
    Paragraph,
    Root,
    Table,
    TableCell,
    TableRow,
    TextDirective,
    ThematicBreak,
    XBlock,
    XInline,
    YAML
} from 'mdast';
import {
    BlockquoteElement,
    CodeElement,
    ContainerDirectiveElement, CustomText,
    Decoration,
    DefinitionElement,
    Descendant,
    DirectiveLabelElement,
    FootnoteDefinitionElement,
    FootnoteElement,
    FootnoteReferenceElement,
    HtmlElement,
    ImageElement,
    ImageReferenceElement,
    LeafDirectiveElement,
    LinkElement,
    LinkReferenceElement,
    ListElement,
    ListItemElement,
    ParagraphElement,
    TableCellElement,
    TableElement,
    TableRowElement,
    Text,
    TextDirectiveElement,
    ThematicBreakElement,
    XBlockElement,
    XInlineElement,
    YamlElement
} from 'slate';
import { toString } from 'mdast-util-to-string';
import { Position } from 'unist';

export function mdastToSlate(node: Root): Descendant[] {
    return createSlateRoot(node);
}

function createSlateRoot(root: Root): Descendant[] {
    return convertNodes(root.children, {});
}

function convertNodes(nodes: Content[], deco: Decoration): Descendant[] {
    if (nodes.length === 0) {
        return [{ text: '' }];
    }
    return nodes.reduce<Descendant[]>((acc, node) => {
        acc.push(...createSlateNode(node, deco));
        return acc;
    }, []);
}

function createSlateNode(node: Content, deco: Decoration): Descendant[] {
    switch (node.type) {
        case 'blankLine':
            return [createBlankLine(node)];
        case 'paragraph':
            return [createParagraph(node, deco)];
        case 'heading':
            return [createHeading(node, deco)];
        case 'thematicBreak':
            return [createThematicBreak(node)];
        case 'blockquote':
            return [createBlockquote(node, deco)];
        case 'list':
            return [createList(node, deco)];
        case 'listItem':
            return [createListItem(node, deco)];
        case 'table':
            return [createTable(node, deco)];
        case 'tableRow':
            return [createTableRow(node, deco)];
        case 'tableCell':
            return [createTableCell(node, deco)];
        case 'html':
            return [createHtml(node)];
        case 'yaml':
            return [createYaml(node)];
        case 'code':
            return [createCode(node)];
        case 'definition':
            return [createDefinition(node)];
        case 'footnoteDefinition':
            return [createFootnoteDefinition(node, deco)];
        case 'text':
            return [createText(node.value, deco, node.position)];
        case 'emphasis':
        case 'strong':
        case 'delete': {
            const { type, children } = node;
            return children.reduce<Text[]>((acc, n) => {
                acc.push(...createSlateNode(n, { ...deco, [type]: true }) as Text[]);
                return acc;
            }, []);
        }
        case 'inlineCode': {
            const { value, position } = node;

            if (position) {
                position.start.column += 1;
                if (position.start.offset != undefined) {
                    position.start.offset += 1;
                }
                position.end.column -= 1;
                if (position.end.offset != undefined) {
                    position.end.offset -= 1;
                }
            }

            return [createText(value, { ...deco, code: true }, position)];
        }
        case 'break':
            return [createBreak()];
        case 'link':
            return [createLink(node)];
        case 'image':
            return [createImage(node)];
        case 'linkReference':
            return [createLinkReference(node, deco)];
        case 'imageReference':
            return [createImageReference(node)];
        case 'footnote':
            return [createFootnote(node, deco)];
        case 'footnoteReference':
            return [createFootnoteReference(node)];
        case 'xBlock':
            return [createXBlock(node)];
        case 'xInline':
            return [createXInline(node)];
        case 'textDirective':
            return [createTextDirective(node, deco)];
        case 'leafDirective':
            return [createLeafDirective(node, deco)];
        case 'containerDirective':
            return [createContainerDirective(node, deco)];
        default:
            break;
    }
    return [{ text: '' }];
}

function createBlankLine(node: BlankLine): ParagraphElement {
    const { position } = node;

    return {
        type: 'paragraph',
        position,
        children: [{ text: '' }],
    };
}

function createParagraph(node: Paragraph, deco: Decoration): ParagraphElement {
    const { type, position, children, align } = node;

    return {
        type,
        position,
        depth: undefined,
        align,
        children: convertNodes(children, deco),
    };
}

function createHeading(node: Heading, deco: Decoration): ParagraphElement {
    const { children, position, depth, align } = node;

    return {
        type: 'paragraph',
        position,
        depth,
        align,
        children: convertNodes(children, deco),
    };
}

function createThematicBreak(node: ThematicBreak): ThematicBreakElement {
    const { type, position } = node;

    return {
        type,
        position,
        children: [{ text: '' }],
    };
}

function createBlockquote(node: Blockquote, deco: Decoration): BlockquoteElement {
    const { type, position, variant } = node;

    return {
        type,
        position,
        variant,
        children: convertNodes(node.children, deco),
    };
}

function createList(node: List, deco: Decoration): ListElement {
    const { type, position, children, ordered, start, spread } = node;

    const task = !node.children.every(n => {
        return n.checked === null;
    });

    return {
        type,
        position,
        ordered,
        start,
        spread,
        task,
        children: convertNodes(children, deco),
    };
}

function createListItem(node: ListItem, deco: Decoration): ListItemElement {
    const { type, position, children, checked, spread } = node;

    return {
        type,
        position,
        checked,
        spread,
        children: convertNodes(children, deco),
    };
}

function createTable(node: Table, deco: Decoration): TableElement {
    const { type, position, children, align } = node;

    return {
        type,
        position,
        align,
        children: convertNodes(children, deco) as TableRowElement[],
    };
}

function createTableRow(node: TableRow, deco: Decoration): TableRowElement {
    const { type, position, children } = node;

    return {
        type,
        position,
        children: convertNodes(children, deco) as TableCellElement[],
    };
}

function createTableCell(node: TableCell, deco: Decoration): TableCellElement {
    const { type, position, children } = node;

    return {
        type,
        position,
        children: convertNodes(children, deco) as CustomText[],
    };
}

function createHtml(node: HTML): HtmlElement {
    const { type, position, value } = node;

    return {
        type,
        value,
        position,
        children: [{ text: '' }],
    };
}

function createCode(node: Code): CodeElement {
    const { type, position, value, lang, meta } = node;

    return {
        type,
        position,
        lang,
        meta,
        children: value.split('\n').map(v => ({
            type: 'codeLine',
            children: [{ text: v }]
        })),
    };
}

function createDefinition(node: Definition): DefinitionElement {
    const { type, position, identifier, label, url, title } = node;

    return {
        type,
        position,
        identifier,
        label,
        url,
        title,
        children: [{ text: '' }],
    };
}

function createFootnoteDefinition(node: FootnoteDefinition, deco: Decoration): FootnoteDefinitionElement {
    const { type, position, children, identifier, label } = node;

    return {
        type,
        position,
        identifier,
        label,
        children: convertNodes(children, deco),
    };
}

function createText(text: string, deco: Decoration, position?: Position): CustomText {
    return {
        text,
        position,
        ...deco,
    };
}

function createBreak() {
    return { text: '\n' };
}

function createLink(node: Link): LinkElement | ImageElement {
    const { type, position, children, url, title } = node;

    //是否为一张图片
    if (children.length == 1 && children[0].type == 'image') {
        const image = createImage(children[0]);
        image.link = url;
        return image;
    }

    return {
        type,
        position,
        url,
        value: toString(children),
        title,
        children: [{ text: '' }],
    };
}

function createImage(node: Image): ImageElement {
    const { type, position, url, title, alt, width, height } = node;

    return {
        type,
        position,
        url,
        title,
        alt,
        width,
        height,
        children: [{ text: '' }],
    };
}

function createLinkReference(node: LinkReference, deco: Decoration): LinkReferenceElement {
    const { type, position, children, referenceType, identifier, label } = node;

    return {
        type,
        position,
        referenceType,
        identifier,
        label,
        children: convertNodes(children, deco),
    };
}

function createImageReference(node: ImageReference): ImageReferenceElement {
    const { type, position, alt, referenceType, identifier, label } = node;

    return {
        type,
        position,
        alt,
        referenceType,
        identifier,
        label,
        children: [{ text: '' }],
    };
}

function createFootnote(node: Footnote, deco: Decoration): FootnoteElement {
    const { type, position, children } = node;

    return {
        type,
        position,
        children: convertNodes(children, deco),
    };
}

function createFootnoteReference(node: FootnoteReference): FootnoteReferenceElement {
    const { type, position, identifier, label } = node;

    return {
        type,
        position,
        identifier,
        label,
        children: [{ text: '' }],
    };
}

function createYaml(node: YAML): YamlElement {
    const { type, position, value } = node;

    return {
        type,
        position,
        value,
        children: [{ text: '' }],
    };
}

function createXBlock(node: XBlock): XBlockElement {
    const { type, position, value, name, parameter } = node;

    return {
        type,
        position,
        name,
        value,
        parameter,
        children: [{ text: '' }],
    };
}

function createXInline(node: XInline): XInlineElement {
    const { type, position, value, name } = node;

    return {
        type,
        position,
        name,
        value,
        children: [{ text: '' }],
    };
}

function createTextDirective(node: TextDirective, deco: Decoration): TextDirectiveElement {
    const { type, position, name, children, attributes } = node;

    return {
        type,
        position,
        name,
        attributes: attributes || {},
        children: convertNodes(children, deco),
    };
}

function createLeafDirective(node: LeafDirective, deco: Decoration): LeafDirectiveElement {
    const { type, position, name, children, attributes } = node;

    return {
        type,
        position,
        name,
        attributes: attributes || {},
        children: convertNodes(children, deco),
    };
}

function createContainerDirective(node: ContainerDirective, deco: Decoration): ContainerDirectiveElement {
    const { type, position, name, children, attributes } = node;

    let directiveLabel: DirectiveLabelElement;

    if (children[0]?.data?.directiveLabel) {
        directiveLabel = {
            type: 'directiveLabel',
            name,
            children: convertNodes((children[0] as Paragraph).children, deco)
        };
        children.shift();
    } else {
        directiveLabel = {
            type: 'directiveLabel',
            name,
            children: [{
                text: ''
            }]
        };
    }

    return {
        type,
        position,
        name,
        attributes: attributes || {},
        children: [
            directiveLabel,
            ...convertNodes(children, deco)
        ],
    };
}
