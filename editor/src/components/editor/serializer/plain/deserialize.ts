import { Descendant } from 'slate';

export default (text: string): Descendant[] => {

    return text.split(/\r\n|\r|\n/).map((line, index) => {

        const position = {
            start: { line: index + 1, column: 1 },
            end: { line: index + 1, column: line.length + 1 },
        };

        return {
            type: 'line',
            position: position,
            children: [
                {
                    text: line,
                    position,
                },
            ],
        };
    });
}
