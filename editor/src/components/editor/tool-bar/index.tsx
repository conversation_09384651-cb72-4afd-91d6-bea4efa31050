import { ButtonGroup, ButtonToolbar } from 'react-bootstrap';
import { styled } from '@topwrite/common';
import { usePlate } from '../use-plate';

export default function ToolBar() {

    const { tools: [leftGroups, rightGroups] } = usePlate();

    return <Container>
        <ButtonToolbar className='justify-content-between flex-nowrap'>
            <ButtonToolbar data-tour={'editor-tools'}>
                {leftGroups.map((group, i) => {
                    return <ButtonGroup key={i}>
                        {group.map((Component, j) => <Component key={j} />)}
                    </ButtonGroup>;
                })}
            </ButtonToolbar>
            <ButtonToolbar>
                {rightGroups.map((group, i) => {
                    return <ButtonGroup key={i}>
                        {group.map((Component, j) => <Component key={j} />)}
                    </ButtonGroup>;
                })}
            </ButtonToolbar>
        </ButtonToolbar>
    </Container>;
}

const Container = styled.div`
    position: relative;
    border-bottom: 1px solid var(--ttw-border-color);
    padding: 4px 5px;
    background: var(--ttw-background);

    .btn {
        line-height: 1;
        padding: 0.5rem 0.75rem;
        height: 34px;
        width: 40px;
        color: var(--ttw-color);

        svg {
            font-size: 16px;
        }
    }

    .btn-toolbar {
        gap: 0.25rem;

        & > .btn-group:empty {
            margin: 0 -0.125rem;
        }
    }
`;
