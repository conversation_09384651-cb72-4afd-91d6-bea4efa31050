import { isMac } from '@topwrite/common';
import { forwardRef, ForwardRefRenderFunction, MouseEvent, useCallback } from 'react';
import { Button, ButtonProps } from 'react-bootstrap';
import Tooltip from '../../tooltip';

export interface ItemProps extends Omit<ButtonProps, 'onMouseDown'> {
    title: string;
    hotKey?: string;
    handler?: (event: MouseEvent) => any;
}

const Item: ForwardRefRenderFunction<HTMLButtonElement, ItemProps> = ({
    children,
    title,
    disabled,
    active,
    hotKey,
    handler,
    ...buttonProps
}, ref) => {
    let tooltip = title;

    const handleMouseDown = useCallback((e: MouseEvent) => {
        let result;
        if (handler) {
            result = handler(e);
        }
        if (result === undefined) {
            e.preventDefault();
        }
    }, [handler]);

    if (hotKey) {
        if (isMac()) {
            hotKey = hotKey.replace('Ctrl', 'Cmd');
        }
        tooltip += `(${hotKey})`;
    }

    const button = <Button
        ref={ref}
        variant='light'
        {...buttonProps}
        onMouseDown={handleMouseDown}
        disabled={disabled}
        active={active}
    >
        {children}
    </Button>;

    if (disabled) {
        return button;
    }

    return <Tooltip
        placement='bottom'
        tooltip={tooltip}
    >
        {button}
    </Tooltip>;
};

export default forwardRef(Item);
