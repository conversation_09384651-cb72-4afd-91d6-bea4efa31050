import Button from '../button';
import { ChangeEvent, useCallback, useRef, useState } from 'react';
import Toast from '../toast';
import useEditor from './use-editor';
import useFormatMessage from '../../lib/use-format-message';
import BrowseButton, { BrowseButtonProps } from './browse-button';
import { BsUpload } from 'react-icons/bs';

export interface UploadButtonProps extends Pick<BrowseButtonProps, 'accept' | 'view'> {
    onSuccess: (file: { path: string, name: string, size: number }) => void;
}

export default function UploadButton({ onSuccess, accept, view }: UploadButtonProps) {
    const t = useFormatMessage();
    const [loading, setLoading] = useState<boolean>(false);
    const [percent, setPercent] = useState(0);
    const inputRef = useRef<HTMLInputElement>(null);
    const { uploadFile, supportBrowseFile } = useEditor();

    const onFileChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
        e.preventDefault();
        const { files } = e.target;
        if (files && files.length > 0) {
            const file = files[0];
            setLoading(true);
            uploadFile(file, {
                onProgress(e) {
                    setPercent(Math.floor(e.loaded / e.total * 100));
                }
            }).then(result => {
                onSuccess(result);
            }).catch(e => {
                Toast.error(e.message);
                console.error(e);
            }).finally(() => {
                setLoading(false);
            });
        }

        //重置文件
        e.target.value = '';
    }, [uploadFile]);

    return <>
        {supportBrowseFile() && <BrowseButton onSelect={onSuccess} accept={accept} view={view} />}
        <input className='d-none' ref={inputRef} type='file' accept={accept} multiple={false} onChange={onFileChange} />
        <Button loading={loading} percent={percent} onClick={() => inputRef.current?.click()} variant='outline-primary'>
            <BsUpload className='me-2' />{t('editor.tool.asset.upload')}
        </Button>
    </>;
}
