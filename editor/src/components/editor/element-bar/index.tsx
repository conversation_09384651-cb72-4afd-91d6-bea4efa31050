import { Element } from 'slate';
import {
    createElement,
    forwardRef,
    ReactNode,
    useCallback,
    useEffect,
    useImperativeHandle,
    useMemo,
    useRef,
    useState
} from 'react';
import { Overlay } from 'react-bootstrap';
import useEditor from '../use-editor';
import { styled } from '@topwrite/common';
import { findPath, isNodeFocused } from '../queries';
import RefHolder from '../../ref-holder';
import { findDOMNode } from 'react-dom';
import { Item, ItemProps } from './item';
import { BsTrash } from 'react-icons/bs';
import { removeNodes, select } from '../transforms';
import useHover from '@react-hook/hover';

type RequireOnlyOne<T, Keys extends keyof T = keyof T> =
    Pick<T, Exclude<keyof T, Keys>>
    & {
        [K in Keys]-?:
        Required<Pick<T, K>>
        & Partial<Record<Exclude<Keys, K>, undefined>>
    }[Keys];

interface ElementBarProps {
    element: Element;
    children: ReactNode;
    zIndex?: number;
    items?: ItemProps[];
    focus?: boolean;
    renderItems?: (Component: typeof Item) => ReactNode;
}

const ElementBar = forwardRef<HTMLElement, RequireOnlyOne<ElementBarProps, 'items' | 'renderItems'>>(({
    children,
    element,
    items,
    renderItems,
    focus = false
}, ref) => {

    const [target, setTarget] = useState<HTMLElement | null>(null);
    const [overlayTarget, setOverlayTarget] = useState<HTMLElement | null>(null);

    const triggerRef = useRef<HTMLElement>(null);

    useEffect(() => {
        if (triggerRef.current) {
            setTarget(findDOMNode(triggerRef.current) as HTMLElement);
        } else {
            setTarget(null);
        }
    }, [triggerRef.current]);

    useImperativeHandle(ref, () => {
        return target!;
    }, [target]);

    const editor = useEditor();
    const path = findPath(editor, element);
    let show: boolean;

    if (focus) {
        show = useMemo(() => {
            return isNodeFocused(editor, element);
        }, [editor.selection]);
    } else {
        const overlayHovering = useHover(overlayTarget, { leaveDelay: 300 });
        const containerHovering = useHover(target, { leaveDelay: 300, enterDelay: 300 });
        show = overlayHovering || containerHovering;
    }

    let popover: ReactNode;
    if (renderItems) {
        popover = renderItems(Item);
    } else {
        popover = items.map((item, index) => <Item key={index} {...item} />);
    }

    return createElement(
        RefHolder,
        { ref: triggerRef },
        children,
        <Overlay
            ref={setOverlayTarget}
            target={target}
            offset={[0, 8]}
            show={show}
        >
            <PopoverBody onMouseDownCapture={useCallback(() => {
                if (!focus) {
                    select(editor, path);
                }
            }, [editor, path])}>
                {popover}
                <Item icon={<BsTrash />} tooltip={'editor.toolbar.delete'} onMouseDown={useCallback(() => {
                    const at = findPath(editor, element);
                    removeNodes(editor, { at });
                }, [editor, element])} />
            </PopoverBody>
        </Overlay>
    );
});

export default ElementBar;

const PopoverBody = styled.div`
    position: absolute;
    display: flex;
    padding: 6px 12px;
    color: var(--ttw-box-color);
    background: var(--ttw-box-background);
    line-height: 1.5;
    box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%);
    border-radius: .3rem;
`;
