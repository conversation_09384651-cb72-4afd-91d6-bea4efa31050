import { forwardR<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ps<PERSON>ith<PERSON><PERSON><PERSON>n, ReactNode, useCallback } from 'react';
import { styled } from '@topwrite/common';
import Tooltip, { TooltipProps } from '../../../components/tooltip';

export interface ItemProps {
    icon?: ReactNode;
    tooltip?: TooltipProps['tooltip'];
    onMouseDown?: () => void;
}

export const Item = forwardRef<any, PropsWithChildren<ItemProps>>(({
    tooltip,
    icon,
    onMouseDown,
    ...props
}, ref) => {

    const handleMouseDown = useCallback<MouseEventHandler>((e) => {
        e.preventDefault();
        if (onMouseDown) {
            onMouseDown();
        }
    }, [onMouseDown]);

    const children = <Container {...props} ref={ref} onMouseDown={handleMouseDown}>{icon}</Container>;

    if (tooltip) {
        return <Tooltip tooltip={tooltip}>
            {children}
        </Tooltip>;
    }
    return children;
});

export const Split = styled.span`
    display: block;
    margin: 4px 6px;
    padding: 0px;
    border-left: 1px solid var(--ttw-box-hover-background);
`;

const Container = styled.span`
    margin: 0px;
    padding: 0 5px;
    align-self: center;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 4px;

    &:hover {
        background-color: var(--ttw-box-hover-background);
    }

    svg {
        font-size: 16px;
        width: 16px;
        height: 16px;
    }
`;
