import { forward<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ps<PERSON>ith<PERSON><PERSON>dren, ReactNode, useCallback } from 'react';
import { css, styled } from '@topwrite/common';
import Tooltip from '../../../components/tooltip';

export interface ItemProps {
    icon?: ReactNode;
    tooltip?: string;
    active?: boolean;
    handler?: () => void;
}

const Item = forwardRef<any, PropsWithChildren<ItemProps>>(({
    tooltip,
    icon,
    active,
    handler,
    ...props
}, ref) => {

    const handleMouseDown = useCallback<MouseEventHandler>((e) => {
        e.preventDefault();
        if (handler) {
            handler();
        }
    }, [handler]);

    const children = <Container {...props} $active={active} ref={ref} onMouseDown={handleMouseDown}>{icon}</Container>;

    if (tooltip) {
        return <Tooltip tooltip={tooltip}>
            {children}
        </Tooltip>;
    }
    return children;
});

export default Item;

export const Split = styled.div`
    display: block;
    margin: 4px 6px;
    padding: 0px;
    border-left: 1px solid var(--ttw-box-hover-background);
`;

export const Group = styled.div`
    display: flex;
    gap: 2px;

    & + &:before {
        content: '';
        display: block;
        width: 1px;
        background-color: var(--ttw-box-hover-background);
        margin: 2px 2px 2px 4px;
    }
`;

const Container = styled.div<{ $active?: boolean; }>`
    margin: 0px;
    padding: 0 5px;
    align-self: center;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 4px;

    ${props => props.$active && css`
        background-color: var(--ttw-box-active-background);
    `};

    &:hover {
        background-color: var(--ttw-box-hover-background);
    }

    svg {
        font-size: 16px;
        width: 16px;
        height: 16px;
    }
`;
