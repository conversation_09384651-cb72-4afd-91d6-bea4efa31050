import { styled } from '@topwrite/common';
import { useMemo } from 'react';
import { useFocused, useSlate, ReactEditor, useSlateStatic } from 'slate-react';
import { Range } from 'slate';
import { Overlay } from 'react-bootstrap';
import useMousedown from '../../../lib/use-mousedown';
import { usePlate } from '../use-plate';
import { Group } from './item';

function Inner() {
    const editor = useSlateStatic();
    const { hoverTools } = usePlate();

    const target = useMemo(() => ({
        getBoundingClientRect: () => {
            const domRange = ReactEditor.toDOMRange(editor, editor.selection!);
            return domRange.getBoundingClientRect();
        },
        contextElement: ReactEditor.toDOMNode(editor, editor)
    }), [editor]);

    if (hoverTools.length === 0) return null;

    return <Overlay
        target={target}
        offset={[0, 8]}
        placement='bottom'
        show
    >
        <Container
            onMouseDown={e => {
                e.stopPropagation();
                e.preventDefault();
            }}
        >
            {hoverTools.map((group, i) => {
                return <Group key={i}>
                    {group.map((Component, j) => <Component key={j} />)}
                </Group>;
            })}
        </Container>
    </Overlay>;
}

export default function HoverBar() {
    const { selection } = useSlate();
    const inFocus = useFocused();
    const mousedown = useMousedown();

    const show = useMemo(() => !mousedown && !!selection && inFocus && !Range.isCollapsed(selection), [selection, inFocus, mousedown]);

    if (!show) {
        return null;
    }

    return <Inner />;
}

const Container = styled.div`
    position: absolute;
    display: flex;
    padding: 4px;
    color: var(--ttw-box-color);
    background: var(--ttw-box-background);
    line-height: 1.5;
    box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%);
    border-radius: .3rem;
    z-index: 36;
`;
