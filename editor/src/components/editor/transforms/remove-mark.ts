import { Editor, Mark, Range, Text, Transforms } from 'slate';

interface Options {
    key: Mark;
    shouldChange?: boolean;
}

export const removeMark = (
    editor: Editor,
    {
        key,
        shouldChange = true,
    }: Options
) => {
    const { selection } = editor;
    if (selection) {
        if (Range.isExpanded(selection)) {
            Transforms.unsetNodes(editor, key, {
                match: Text.isText,
                split: true,
            });
        } else {
            const marks = { ...(Editor.marks(editor) || {}) };
            delete marks[key];
            editor.marks = marks;
            shouldChange && editor.onChange();
        }
    }
};
