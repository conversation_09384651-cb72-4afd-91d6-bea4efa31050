import { createMarkdownPlatePlugin } from '../../plate-plugin';
import isHotkey from 'is-hotkey';
import { getBlockAbove, isBlockEnd, isCollapsed, isStart } from '../../queries';
import { GoMarkdown } from 'react-icons/go';
import { useMode } from '../../mode-provider';
import useFormatMessage from '../../../../lib/use-format-message';
import { getBlockPrevious } from '../../queries/get-block-previous';

export const MarkdownPlugin = createMarkdownPlatePlugin({
    tools() {
        return {
            '-10': ({ Component }) => {
                const { toggle, mode } = useMode();
                const t = useFormatMessage();

                return <Component
                    data-tour={'editor-markdown'}
                    title={t('editor.tool.markdown')}
                    handler={toggle}
                    active={mode === 'raw'}
                >
                    <GoMarkdown />
                </Component>;
            }
        };
    },
    onKeyDown(editor) {
        if (editor.isDocument) {
            return (e) => {
                //禁用所有的enter
                //TODO 禁用slate自带的所有的onKeyDown
                if (isHotkey('shift?+enter', e)) {
                    e.preventDefault();
                }

                if (isHotkey('shift?+backspace', e)) {
                    if (isCollapsed(editor)) {
                        const above = getBlockAbove(editor);
                        if (above) {
                            const [node, path] = above;
                            if (isStart(editor, path)) {
                                const previous = getBlockPrevious(editor, { at: path });
                                if (!previous || previous[0].type != node.type) {
                                    //删除到块状元素顶格时停止
                                    e.preventDefault();
                                }
                            }
                        }
                    } else {
                        //TODO
                    }
                }

                if (isHotkey('shift?+delete', e)) {
                    if (isCollapsed(editor)) {
                        if (isBlockEnd(editor)) {
                            //删除到块状元素顶格时停止
                            e.preventDefault();
                        }
                    } else {

                    }
                }
            };
        }
    }
});
