import { createMarkdownPlatePlugin } from '../../plate-plugin';
import Plain from '../../serializer/plain';
import Markdown from '../../serializer/markdown';
import { Transforms } from 'slate';
import parse from 'rehype-parse';
import rehype2remark from 'rehype-remark';
import stringify from 'remark-stringify';
import { markdown } from '@topwrite/common';

const processor = markdown()
    .use(parse)
    .use(rehype2remark)
    .use(stringify, {
        bullet: '*',
        listItemIndent: 'one',
        fences: true,
        handlers: {
            html: (node) => {
                const value = node.value as string;
                if (['<!--StartFragment-->', '<!--EndFragment-->'].includes(value)) {
                    return '';
                }
                return value || '';
            }
        }
    })
;

export const PasteHtmlPlugin = createMarkdownPlatePlugin({
    withOverrides(editor) {
        const { insertData } = editor;

        editor.insertData = data => {
            if (!data.types.includes('application/x-slate-fragment')) {

                const html = data.getData('text/html');
                if (html) {
                    const result = processor.processSync(html);

                    if (editor.isDocument) {
                        const fragment = Markdown.deserialize(String(result));
                        Transforms.insertFragment(editor, fragment);
                    } else {
                        const fragment = Plain.deserialize(String(result));
                        Transforms.insertFragment(editor, fragment);
                    }
                    return;
                }
            }

            insertData(data);
        };
        return editor;
    }
});
