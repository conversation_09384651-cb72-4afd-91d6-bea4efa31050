import { createPlatePlugin } from '../../plate-plugin';
import useEditor from '../../use-editor';
import useFormatMessage from '../../../../lib/use-format-message';
import { BsArrowClockwise, BsArrowCounterclockwise } from 'react-icons/bs';
import Remote from './remote';
import Toast from '../../../toast';
import isHotkey from 'is-hotkey';

export const HistoryPlugin = createPlatePlugin({
    tools() {
        return {
            '10': function({ Component }) {
                const { history, redo, undo } = useEditor();

                const t = useFormatMessage();

                return <>
                    <Component
                        hotKey='Ctrl+Z'
                        title={t('editor.tool.history.undo')}
                        disabled={history.undos.length === 0}
                        handler={undo}
                    >
                        <BsArrowCounterclockwise />
                    </Component>
                    <Component
                        hotKey='Ctrl+Y'
                        title={t('editor.tool.history.redo')}
                        disabled={history.redos.length === 0}
                        handler={redo}
                    >
                        <BsArrowClockwise />
                    </Component>
                </>;
            },
            '-10': Remote
        };
    },
    onKeyDown(editor) {
        return (e) => {
            if (isHotkey('mod+s', e)) {
                e.preventDefault();
                Toast.info(editor.formatMessage('editor.tool.history.saved'));
            }
        };
    },
});
