import { createMarkdownPlatePlugin } from '../../plate-plugin';
import { styled } from '@topwrite/common';
import isHotkey from 'is-hotkey';
import { insertThematicBreak } from './transforms';
import useFormatMessage from '../../../../lib/use-format-message';
import { useCallback } from 'react';
import { GoHorizontalRule } from 'react-icons/go';
import useEditor from '../../use-editor';
import ElementMenu from '../../element-menu';
import { inParagraph } from '../paragraph/transforms';

export const ThematicBreakPlugin = createMarkdownPlatePlugin({
    voidTypes: 'thematicBreak',
    tools() {
        return {
            70: ({ Component }) => {
                const t = useFormatMessage();
                const editor = useEditor();
                const disabled = !inParagraph(editor);
                const handler = useCallback(() => {
                    insertThematicBreak(editor);
                }, [editor]);

                return <Component
                    disabled={disabled}
                    handler={handler}
                    title={t('editor.tool.thematic-break')}
                    hotKey='Ctrl+H'>
                    <GoHorizontalRule />
                </Component>;
            }
        };
    },
    renderElement() {
        return {
            thematicBreak({ attributes, children, element }) {
                return <ElementMenu element={element} {...attributes}>
                    <Container contentEditable={false}>
                        <hr />
                        {children}
                    </Container>
                </ElementMenu>;
            }
        };
    },
    onKeyDown(editor) {
        return e => {
            if (isHotkey('mod+h', e)) {
                e.preventDefault();
                insertThematicBreak(editor);
            }
        };
    }
});

const Container = styled.div`
    padding: 5px 0;
    margin: 1em 0;

    hr {
        margin: 0;
        opacity: 1;
        height: 3px;
        padding: 0;
        overflow: hidden;
        background-color: var(--ttw-box-background);
        border: none;

        &:before, &:after {
            display: table;
            content: " ";
        }

        &:after {
            clear: both;
        }
    }
`;
