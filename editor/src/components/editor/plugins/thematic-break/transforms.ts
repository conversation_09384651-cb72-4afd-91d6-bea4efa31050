import { Editor } from 'slate';
import { insertNodes } from '../../transforms';
import { removeEmptyLineForBlock } from '../markdown/transforms';
import { someNode } from '../../queries';

export function insertThematicBreak(editor: Editor) {
    removeEmptyLineForBlock(editor, () => {
        if (editor.isDocument) {
            if (someNode(editor, { match: { type: 'paragraph' } })) {
                insertNodes(editor, {
                    type: 'thematicBreak',
                    children: [{ text: '' }]
                });
            }
        } else {
            insertNodes(editor, {
                type: 'line',
                children: [{
                    text: '*****'
                }]
            });
        }
    });

}
