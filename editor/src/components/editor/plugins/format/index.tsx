import { createPlatePlugin } from '../../plate-plugin';
import useFormatMessage from '../../../../lib/use-format-message';
import { useCallback } from 'react';
import { Buffer } from 'buffer';
import { BsJustifyLeft } from 'react-icons/bs';
import { useFile } from '../../../file-provider';
import { socket } from '../../../../lib/socket';
import Toast from '../../../toast';

const SUPPORT_MIMES = {
    'text/css': {
        parser: 'css',
        loadPlugin: () => import('prettier/parser-postcss')
    },
    'application/json': {
        parser: 'json',
        loadPlugin: () => import('prettier/parser-babel')
    }
};

export const FormatPlugin = createPlatePlugin({
    tools() {
        return {
            20: ({ Component }) => {
                const t = useFormatMessage();

                const file = useFile();

                const handler = useCallback(async () => {
                    const options = SUPPORT_MIMES[file.mime];

                    const prettier = await import('prettier');
                    const plugin = await options.loadPlugin();

                    try {
                        const content = prettier.format(file.content.toString(), {
                            parser: options.parser,
                            printWidth: 1000,
                            tabWidth: 4,
                            plugins: [plugin]
                        });
                        socket.writeFileAsync(file.path, Buffer.from(content));
                    } catch (e: any) {
                        Toast.error(e.message);
                    }
                }, [file]);

                return <Component
                    handler={handler}
                    title={t(`editor.tool.format`)}
                >
                    <BsJustifyLeft />
                </Component>;
            }
        };
    },
    available(editor) {
        return !!SUPPORT_MIMES[editor.mime];
    }
});
