import { createMarkdownPlatePlugin } from '../../plate-plugin';
import useEditor from '../../use-editor';
import useEditorActions from '../../use-editor-actions';
import { css, styled } from '@topwrite/common';
import isHotkey from 'is-hotkey';
import { elements, getBlockAbove, getLastChildPath, isAncestorEmpty, isBlockStart, isCollapsed } from '../../queries';
import { isListActive, toggleList, unwrapList } from './transforms';
import { moveNodes, removeNodes, setNodes, splitNodes, wrapNodes } from '../../transforms';
import { Editor, Element, ListElement, ListItemElement, Node, ParagraphElement, Path } from 'slate';
import { ReactNode, useCallback } from 'react';
import useFormatMessage from '../../../../lib/use-format-message';
import { BsListOl, BsListTask, BsListUl } from 'react-icons/bs';
import { inParagraph } from '../paragraph/transforms';
import { getNodeParent } from '../../queries/get-node-parent';

interface List {
    type: 'ordered' | 'bullet' | 'task';
    hotKey: string;
    icon: ReactNode;
}

export const ListPlugin = createMarkdownPlatePlugin({
    tools() {
        return {
            50: function({ Component }) {

                const t = useFormatMessage();

                const editor = useEditor();
                const lists: List[] = [
                    {
                        hotKey: 'Ctrl+Alt+U',
                        icon: <BsListUl />,
                        type: 'bullet'
                    },
                    {
                        hotKey: 'Ctrl+Alt+O',
                        icon: <BsListOl />,
                        type: 'ordered'
                    },
                    {
                        hotKey: 'Ctrl+Alt+T',
                        icon: <BsListTask />,
                        type: 'task'
                    }
                ];

                const disabled = !inParagraph(editor);

                return <>
                    {lists.map(({ type, icon, hotKey }) => {
                        const active = isListActive(editor, type);

                        const handler = useCallback(() => {
                            toggleList(editor, type);
                        }, [editor, type]);

                        return <Component
                            key={type}
                            active={active}
                            hotKey={hotKey}
                            handler={handler}
                            disabled={disabled}
                            title={t(`editor.tool.list.${type}`)}
                        >
                            {icon}
                        </Component>;
                    })}
                </>;


            }
        };
    },
    renderElement() {
        return {
            listItem: function({ element, attributes, children }) {
                const editor = useEditor();
                const { findPath, setNodes } = useEditorActions();
                const path = findPath(editor, element);
                let checkbox: ReactNode = null;
                if (element.checked !== null) {
                    checkbox = <Checkbox contentEditable={false}>
                        <input
                            type='checkbox'
                            tabIndex={-1}
                            defaultChecked={!!element.checked}
                            onMouseDown={(e) => {
                                e.preventDefault();
                                setNodes(editor, {
                                    checked: !element.checked
                                }, {
                                    at: path
                                });
                            }}
                        />
                    </Checkbox>;
                }
                return <Item checked={element.checked !== null} {...attributes}>
                    {checkbox}
                    {children}
                </Item>;
            },
            list: function({ element, attributes, children, ElementMenu }) {
                const as = !element.ordered || element.task ? 'ul' : 'ol';
                return <ElementMenu element={element}  {...attributes}>
                    <Container as={as} task={element.task}>{children}</Container>
                </ElementMenu>;
            }
        };
    },
    onKeyDown(editor) {
        return e => {
            if (isHotkey('mod+alt+u', e)) {
                e.preventDefault();
                toggleList(editor, 'bullet');
            }
            if (isHotkey('mod+alt+o', e)) {
                e.preventDefault();
                toggleList(editor, 'ordered');
            }
            if (isHotkey('mod+alt+t', e)) {
                e.preventDefault();
                toggleList(editor, 'task');
            }

            if (editor.isDocument) {
                const match = { type: ['listItem'] };
                if (isHotkey('enter', e)) {
                    const above = getBlockAbove(editor, { match });
                    if (above) {
                        e.preventDefault();

                        const [node] = above;
                        if (isAncestorEmpty(editor, node)) {
                            unwrapList(editor);
                        } else {
                            splitNodes(editor, { always: true, match });
                        }

                        //重置段落depth
                        const entry = getBlockAbove(editor, { match: { type: 'paragraph' } });
                        if (entry && isAncestorEmpty(editor, entry[0])) {
                            setNodes(editor, {
                                depth: undefined
                            }, {
                                at: entry[1]
                            });
                        }
                    }
                }

                if (isHotkey('backspace', e) && isCollapsed(editor) && isBlockStart(editor, { match })) {
                    e.preventDefault();
                    unwrapList(editor);
                }
            } else {
                if (isHotkey('enter', e) && isCollapsed(editor)) {
                    const [[node,]] = elements(editor);
                    const text = Node.string(node);

                    const inList = text.match(/^(\*\s(\[(x|\s)]\s)?|\d+\.\s)/);
                    if (inList) {
                        e.preventDefault();
                        editor.insertBreak();
                        editor.insertText(inList[0]);
                    }
                }
            }
        };
    },
    withOverrides(editor) {
        if (editor.isDocument) {
            const { normalizeNode } = editor;

            editor.normalizeNode = ([node, path]) => {

                if (Element.isElementType<ListElement>(node, 'list')) {
                    //删除空列表
                    if (!Element.isElementList(node.children)) {
                        removeNodes(editor, { at: path });
                        return;
                    }

                    //确保列表下的元素
                    for (const [child, childPath] of Node.children(editor, path)) {
                        if (!Element.isElementType(child, 'listItem')) {
                            wrapNodes(editor, {
                                type: 'listItem',
                                children: []
                            }, {
                                at: childPath
                            });
                        }
                    }

                    //合并相邻的同类list
                    try {
                        const nextPath = Path.next(path);
                        const nextNode = Node.get(editor, nextPath);
                        if (isSameList(nextNode, node)) {
                            Editor.withoutNormalizing(editor, () => {
                                const lastChildPath = getLastChildPath([node, path]);
                                const to = Path.next(lastChildPath);
                                for (const [, at] of Node.children(editor, nextPath, { reverse: true })) {
                                    moveNodes(editor, { at, to });
                                }
                                removeNodes(editor, { at: nextPath });
                            });
                        }
                    } catch {
                    }

                    try {
                        const prevPath = Path.previous(path);
                        const prevNode = Node.get(editor, prevPath);
                        if (isSameList(prevNode, node)) {
                            editor.normalizeNode([prevNode, prevPath]);
                            return;
                        }
                    } catch {

                    }

                    node = Node.get(editor, path);
                }

                if (Element.isElementType<ListItemElement>(node, 'listItem')) {
                    const parent = getNodeParent(editor, path);
                    if (!parent || !Element.isElementType(parent, 'list')) {
                        removeNodes(editor, { at: path });
                        return;
                    }

                    if (!Element.isElementList(node.children)) {
                        removeNodes(editor, { at: path });
                        return;
                    }

                    //任务列表中只能是段落
                    if (node.checked != undefined) {
                        for (const [child, childPath] of Node.children(editor, path)) {
                            if (!Element.isElementType(child, 'paragraph')) {
                                removeNodes(editor, { at: childPath });
                            }
                        }
                        node = Node.get(editor, path);
                    }
                }

                //列表中禁用标题
                if (Element.isElementType<ParagraphElement>(node, 'paragraph')) {
                    const parent = getNodeParent(editor, path);
                    if (parent && Element.isElementType(parent, 'listItem') && node.depth != undefined) {
                        setNodes(editor, { depth: undefined }, { at: path });
                        node = Node.get(editor, path);
                    }
                }

                normalizeNode([node, path]);
            };
        }
        return editor;
    }
});

const isSameList = (node1: Node, list2: ListElement) => {
    return Element.isElementType<ListElement>(node1, 'list')
        && Boolean(node1.task) === Boolean(list2.task)
        && Boolean(node1.ordered) === Boolean(list2.ordered);
};

const Checkbox = styled.div`
  position: absolute;
  left: 0;
  height: 2em;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2em;

  input[type=checkbox] {
    width: 1em;
    height: 1em;
  }
`;

const Container = styled.ul<{ task?: boolean }>`
  padding-left: 2em;
  margin: 1em 0;

  ${props => props.task && css`
    position: relative;
  `}
`;

const Item = styled.li<{ checked?: boolean }>`
  ${props => props.checked && css`
    list-style: none;
  `}
`;
