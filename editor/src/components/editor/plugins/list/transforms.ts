import { elements, getNodes, someNode } from '../../queries';
import { insertText, setNodes, unwrapNodes, wrapNodes } from '../../transforms';
import { Editor, Element } from 'slate';
import { isParagraphActive } from '../paragraph/transforms';

export const isListActive = (editor: Editor, type: 'ordered' | 'bullet' | 'task') => {
    return editor.isDocument && someNode(editor, {
        match: n => {
            return n.type === 'list' && (!!n.task ? type === 'task' : (type === (n.ordered ? 'ordered' : 'bullet')));
        }
    });
};

export const unwrapList = (editor: Editor) => {
    if (editor.isDocument) {
        Editor.withoutNormalizing(editor, () => {
            unwrapNodes(editor, { match: { type: 'listItem' } });
            unwrapNodes(editor, { match: { type: 'list' }, split: true });
        });
    }
};

export const toggleList = (editor: Editor, type: 'ordered' | 'bullet' | 'task') => {
    if (editor.isDocument) {
        Editor.withoutNormalizing(editor, () => {
            if (isParagraphActive(editor)) {
                const isActive = isListActive(editor, type);
                unwrapList(editor);
                setNodes(editor, {
                    type: 'paragraph',
                });
                if (!isActive) {
                    const options: any = {};
                    if (type === 'task') {
                        options.task = true;
                    } else {
                        options.ordered = type === 'ordered';
                    }
                    const list = { type: 'list', children: [], ...options };
                    wrapNodes(editor, list);

                    const nodes = [...getNodes(editor, { match: { type: 'paragraph' } })];

                    const listItem: Element = {
                        type: 'listItem',
                        checked: type === 'task' ? false : null,
                        children: []
                    };

                    for (const [, path] of nodes) {
                        wrapNodes(editor, listItem, {
                            at: path
                        });
                    }
                }
            }
        });
    } else {
        let index = 0;
        for (const [, path] of elements(editor)) {
            let markup;
            switch (type) {
                case 'bullet':
                    markup = '* ';
                    break;
                case 'ordered':
                    markup = `${++index}. `;
                    break;
                case 'task':
                    markup = '* [ ] ';
                    break;
            }

            const at = { path: path.concat(0), offset: 0 };
            insertText(editor, markup, { at });
        }
    }
};
