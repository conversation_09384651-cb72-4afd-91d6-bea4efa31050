import { createMarkdownPlatePlugin } from '../../plate-plugin';
import { Editor, Element, ParagraphDepth, ParagraphElement, Range, Transforms } from 'slate';
import isHotkey from 'is-hotkey';
import { inParagraph, isParagraphActive, setAlign, toggleParagraph } from './transforms';
import { findNode, isAncestorEmpty, isEnd } from '../../queries';
import { insertNodes } from '../../transforms';
import { css, styled } from '@topwrite/common';
import { AlignType } from 'mdast';
import useEditor from '../../use-editor';
import useFormatMessage from '../../../../lib/use-format-message';
import { ReactNode, useCallback } from 'react';
import { BsTextCenter, BsTextLeft, BsTextRight } from 'react-icons/bs';

export const PLACEHOLDER_SYMBOL = (Symbol('placeholder') as unknown) as string;

interface Direction {
    direction: 'right' | 'center' | 'left';
    hotKey: string;
    icon: ReactNode;
}

export const ParagraphPlugin = createMarkdownPlatePlugin({
    tools() {
        return {
            '20': function({ Component }) {
                const editor = useEditor();
                const t = useFormatMessage();
                const depths: ParagraphDepth[] = [1, 2, 3, 4];

                const disabled = !inParagraph(editor);

                return <>
                    {depths.map(depth => {
                        const title = t(`editor.tool.heading${depth}`);
                        const active = isParagraphActive(editor, { depth });
                        const handler = useCallback(() => {
                            toggleParagraph(editor, depth);
                        }, [editor, depth]);

                        return <Component
                            key={depth}
                            disabled={disabled}
                            active={active}
                            handler={handler}
                            title={title}
                            hotKey={`Ctrl+${depth}`}
                        >H{depth}</Component>;
                    })}
                </>;
            },
            40: function({ Component }) {
                const t = useFormatMessage();
                const editor = useEditor();
                const disabled = !inParagraph(editor);
                const directions: Direction[] = [
                    {
                        hotKey: 'Ctrl+Alt+L',
                        direction: 'left',
                        icon: <BsTextLeft />
                    },
                    {
                        hotKey: 'Ctrl+Alt+E',
                        direction: 'center',
                        icon: <BsTextCenter />
                    },
                    {
                        hotKey: 'Ctrl+Alt+R',
                        direction: 'right',
                        icon: <BsTextRight />
                    }
                ];

                return <>
                    {directions.map(({ icon, direction, hotKey }) => {
                        const active = isParagraphActive(editor, { align: direction });

                        const handler = useCallback(() => {
                            setAlign(editor, direction);
                        }, [editor, direction]);

                        return <Component
                            key={direction}
                            disabled={disabled}
                            hotKey={hotKey}
                            handler={handler}
                            active={active}
                            title={t(`editor.tool.align.${direction}`)}
                        >
                            {icon}
                        </Component>;
                    })}
                </>;
            }
        };
    },
    renderElement() {
        return {
            paragraph: function({ element, attributes, children }) {
                const { depth = 0, align } = element;

                return <Paragraph depth={depth} align={align} {...attributes} >{children}</Paragraph>;
            },
        };
    },
    renderLeaf(editor) {
        if (editor.isDocument) {
            return ({ children, leaf }) => {
                const t = useFormatMessage();
                if (leaf[PLACEHOLDER_SYMBOL]) {
                    return <>
                        <Placeholder contentEditable={false}>
                            {leaf['placeholder'] || t('editor.placeholder')}
                        </Placeholder>
                        {children}
                    </>;
                }
                return children;
            };
        }
    },
    decorate(editor) {
        return ([node, path]) => {
            const ranges: Range[] = [];
            if (Editor.isEditor(node)) {
                if (node.children.length == 2 && isAncestorEmpty(editor, node.children[1])) {
                    const range: Range = {
                        anchor: {
                            path: path.concat(1, 0),
                            offset: 0,
                        },
                        focus: {
                            path: path.concat(1, 0),
                            offset: 0,
                        },
                    };
                    range[PLACEHOLDER_SYMBOL] = true;
                    ranges.push(range);
                }
            }
            return ranges;
        };
    },
    onKeyDown(editor) {
        return e => {
            [1, 2, 3, 4].forEach((depth) => {
                if (isHotkey(`mod+${depth}`, e)) {
                    e.preventDefault();
                    toggleParagraph(editor, depth as ParagraphDepth);
                }
            });
            if (isHotkey('mod+alt+l', e)) {
                e.preventDefault();
                setAlign(editor, 'left');
            }
            if (isHotkey('mod+alt+e', e)) {
                e.preventDefault();
                setAlign(editor, 'center');
            }
            if (isHotkey('mod+alt+r', e)) {
                e.preventDefault();
                setAlign(editor, 'right');
            }

            if (editor.isDocument) {
                if (isHotkey('shift+enter', e) && inParagraph(editor)) {
                    e.preventDefault();
                    Editor.insertSoftBreak(editor);
                }
                if (isHotkey('enter', e) && editor.selection) {
                    const entry = findNode<ParagraphElement>(editor, {
                        at: editor.selection,
                        match: { type: 'paragraph' }
                    });

                    if (entry) {
                        const [node, path] = entry;
                        e.preventDefault();
                        if (node.depth && isEnd(editor, editor.selection?.anchor, path)) {
                            insertNodes(editor, {
                                type: 'paragraph',
                                children: [{
                                    text: ''
                                }]
                            });
                        } else {
                            Editor.insertBreak(editor);
                        }

                        //TODO 改为在slate2mdast中合并相邻的段落  //改进markdown对回车的处理  要处理成段落  不要\n
                    }
                }
            }
        };
    },
    withOverrides(editor) {
        const { normalizeNode } = editor;

        if (editor.isDocument) {
            editor.normalizeNode = (entry) => {
                const [node] = entry;
                if (Editor.isEditor(node)) {
                    for (let i = 0; i < node.children.length; i++) {
                        const child = node.children[i];
                        if (!Element.isElement(child)) {
                            Transforms.wrapNodes(editor, {
                                type: 'paragraph',
                                children: [{ text: '' }]
                            }, {
                                at: [i]
                            });
                        }
                    }

                    const last = node.children[node.children.length - 1];
                    if (Element.isElement(last) && last.type !== 'paragraph') {
                        Transforms.insertNodes(editor, {
                            type: 'paragraph',
                            children: [{ text: '' }]
                        }, {
                            at: [node.children.length]
                        });
                    }
                }


                normalizeNode(entry);
            };
        }
        return editor;
    }
});

const Placeholder = styled.span`
  position: absolute;
  pointer-events: none;
  width: 100%;
  max-width: 100%;
  display: block;
  opacity: 0.333;
  user-select: none;
  text-decoration: none;
`;

const Paragraph = styled.div<{ depth: number, align?: AlignType }>`
  font-size: 1em;
  line-height: 2;
  position: relative;

  & > * {
    vertical-align: middle;
  }

  ${props => props.depth > 0 && css`
    line-height: 1.5;
    font-size: ${Math.max(2 - (props.depth - 1) * 0.25, 1)}em;
    margin: 1rem 0;
  `}

  ${props => props.align && css`
    text-align: ${props.align};
  `}
`;
