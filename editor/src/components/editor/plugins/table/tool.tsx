import { createContext, MouseEvent, useCallback, useContext, useRef, useState } from 'react';
import useFormatMessage from '../../../../lib/use-format-message';
import { Overlay, Popover } from 'react-bootstrap';
import { range } from 'lodash';
import classNames from 'classnames';
import { BsTable } from 'react-icons/bs';
import { insertTable } from './transforms';
import useEditor from '../../use-editor';
import useOverlayState from '../../../../lib/use-overlay-state';
import { ToolBarItemType } from '../../plate-plugin';
import { inParagraph } from '../paragraph/transforms';

const CellContext = createContext({
    position: { x: -1, y: -1 },
    setPosition: ({ }: { x: number, y: number; }) => {
    }
});

const Cell = ({ x, y }: { x: number, y: number; }) => {
    const { position, setPosition } = useContext(CellContext);
    const active = x <= position.x && y <= position.y;
    return <div
        className={classNames('p-2 border ms-1 mt-1', { 'bg-success border-success': active })}
        onMouseEnter={() => setPosition({ x, y })}
    />;
};

const Tool: ToolBarItemType = ({ Component }) => {
    const t = useFormatMessage();
    const editor = useEditor();

    const [position, setPosition] = useState({ x: -1, y: -1 });

    const { visible, show, hide, state } = useOverlayState({
        onHide() {
            setPosition({ x: -1, y: -1 });
        }
    });

    const target = useRef(null);

    const handleCellClick = useCallback((e: MouseEvent) => {
        e.preventDefault();
        const rows = position.x + 1;
        const columns = position.y + 1;

        insertTable(editor, rows, columns);

        hide();
    }, [editor, hide, position]);

    const disabled = !inParagraph(editor);

    return <>
        <Component
            disabled={disabled}
            active={visible}
            handler={show}
            ref={target}
            title={t('editor.tool.table')}
        >
            <BsTable />
        </Component>
        <Overlay
            target={target.current}
            placement='bottom'
            rootClose={true}
            rootCloseEvent='mousedown'
            {...state}
        >
            <Popover>
                <Popover.Body onMouseDown={handleCellClick}>
                    <CellContext.Provider value={{ position, setPosition }}>
                        {range(10).map((x) => {
                            return <div key={`k-${x}`} className='d-flex flex-row flex-wrap me-1'>{
                                range(10).map((y) => {
                                    return <Cell key={`k-${x}-${y}`} x={x} y={y} />;
                                })
                            }</div>;
                        })}
                    </CellContext.Provider>
                    <div className='mt-1 text-center text-muted'>
                        {position.x + 1}<span className='mx-1'>x</span>{position.y + 1}
                    </div>
                </Popover.Body>
            </Popover>
        </Overlay>
    </>;
};

export default Tool;