import useEditor from '../../use-editor';
import { insertTableColumn, insertTableRow, removeTableColumn, removeTableRow } from './transforms';
import {
    AiOutlineDeleteColumn,
    AiOutlineDeleteRow,
    AiOutlineInsertRowBelow,
    AiOutlineInsertRowRight
} from 'react-icons/ai';
import ElementBar from '../../element-bar';
import { styled } from '@topwrite/common';
import { ElementProps } from '../../plate-plugin';
import { TableCellElement, TableElement, TableRowElement } from 'slate';

export function tableRow({ attributes, children, }: ElementProps<TableRowElement>) {
    return <tr {...attributes}>{children}</tr>;
}

export function tableCell({ attributes, children, }: ElementProps<TableCellElement>) {
    return <td {...attributes}>
        <div>{children}</div>
    </td>;
}

export function table({ children, element, attributes, ElementMenu }: ElementProps<TableElement>) {
    const editor = useEditor();
    const onInsertTableRow = () => {
        insertTableRow(editor);
    };
    const onRemoveTableRow = () => {
        removeTableRow(editor);
    };
    const onInsertTableColumn = () => {
        insertTableColumn(editor);
    };
    const onRemoveTableColumn = () => {
        removeTableColumn(editor);
    };
    return <ElementBar
        element={element}
        focus
        items={[
            {
                tooltip: 'editor.toolbar.table.insert_row',
                icon: <AiOutlineInsertRowBelow />,
                onMouseDown: onInsertTableRow
            },
            {
                tooltip: 'editor.toolbar.table.insert_column',
                icon: <AiOutlineInsertRowRight />,
                onMouseDown: onInsertTableColumn
            },
            {
                tooltip: 'editor.toolbar.table.remove_row',
                icon: <AiOutlineDeleteRow />,
                onMouseDown: onRemoveTableRow
            },
            {
                tooltip: 'editor.toolbar.table.remove_column',
                icon: <AiOutlineDeleteColumn />,
                onMouseDown: onRemoveTableColumn
            }
        ]}
    >
        <ElementMenu element={element} {...attributes} items={false}>
            <Container>
                <tbody>{children}</tbody>
            </Container>
        </ElementMenu>
    </ElementBar>;
}


const Container = styled.table`
    display: table;
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    overflow: auto;
    border-top: 1px solid var(--ttw-border-color);
    margin: 1em 0;
    table-layout: fixed;

    tr {
        background-color: var(--ttw-box-background);
        border: none;
        border-bottom: 1px solid var(--ttw-border-color);
        border-right: 1px solid var(--ttw-border-color);

        &:nth-child(2n) {
            background-color: var(--ttw-box-background);
        }

        &:nth-child(2n+1) {
            background-color: var(--ttw-box-hover-background);
        }

        &:first-child {
            background: var(--ttw-foreground);
            font-weight: 500
        }
    }

    td, th {
        padding: 6px 13px;
        border: 1px solid var(--ttw-border-color);
    }

    td {
        word-break: break-all;
        border-left: 1px solid var(--ttw-border-color);
        border-top: none;
        border-bottom: none;
        border-right: none;
    }

    th {
        font-weight: 700
    }
`;
