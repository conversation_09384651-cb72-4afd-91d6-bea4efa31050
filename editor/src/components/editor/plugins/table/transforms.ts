import { Editor, Node, Path, TableCellElement, TableElement, TableRowElement } from 'slate';
import { range } from 'lodash';
import { insertNodes, removeNodes } from '../../transforms';
import { findNode, someNode } from '../../queries';
import { removeEmptyLineForBlock } from '../markdown/transforms';

export const isInTable = (editor: Editor) => {
    return editor.isDocument && someNode(editor, { match: { type: 'table' } });
};

const createCells = (columns: number): TableCellElement[] => {
    return range(columns).map(() => {
        return {
            type: 'tableCell',
            children: [{
                text: ''
            }]
        };
    });
};

const createRows = (rows: number, columns: number): TableRowElement[] => {
    return range(rows).map(() => {
        const cells = createCells(columns);
        return {
            type: 'tableRow',
            children: cells
        };
    });
};

export const insertTable = (editor: Editor, rows: number, columns: number) => {
    removeEmptyLineForBlock(editor, () => {
        if (editor.isDocument) {
            const table: TableElement = {
                type: 'table',
                children: createRows(rows, columns)
            };
            insertNodes(editor, table);
        } else {
            const nodes: Node[] = [];
            range(rows + 1).forEach((x) => {
                let line = '|';
                range(columns).forEach(() => {
                    line += x === 1 ? ' --- |' : '     |';
                });
                nodes.push({
                    type: 'line',
                    children: [{
                        text: line
                    }]
                });
            });
            insertNodes(editor, nodes);
        }
    });
};

export const insertTableRow = (editor: Editor) => {
    if (editor.isDocument) {

        const rowEntry = findNode<TableRowElement>(editor, { match: { type: 'tableRow' } });

        if (rowEntry) {
            const [node, path] = rowEntry;
            const columns = node.children.length;

            const row: TableRowElement = {
                type: 'tableRow',
                children: createCells(columns)
            };

            insertNodes(editor, row, { at: Path.next(path) });
        }
    }
};

export const removeTableRow = (editor: Editor) => {
    if (editor.isDocument) {
        const rowEntry = findNode<TableRowElement>(editor, { match: { type: 'tableRow' } });
        if (rowEntry) {
            const [, path] = rowEntry;
            removeNodes(editor, { at: path });
        }
    }
};

export const insertTableColumn = (editor: Editor) => {
    if (editor.isDocument) {
        const tableEntry = findNode<TableElement>(editor, { match: { type: 'table' } });
        const cellEntry = findNode<TableCellElement>(editor, { match: { type: 'tableCell' } });
        if (cellEntry && tableEntry) {
            const [table, tablePath] = tableEntry;
            const [, path] = cellEntry;
            const index = path[path.length - 1] + 1;
            const rows = table.children.length;

            Editor.withoutNormalizing(editor, () => {
                range(rows).forEach(row => {
                    insertNodes(editor, {
                        type: 'tableCell',
                        children: [{
                            text: ''
                        }]
                    }, {
                        at: [...tablePath, row, index]
                    });
                });
            });
        }
    }
};

export const removeTableColumn = (editor: Editor) => {
    if (editor.isDocument) {
        const tableEntry = findNode<TableElement>(editor, { match: { type: 'table' } });
        const cellEntry = findNode<TableCellElement>(editor, { match: { type: 'tableCell' } });
        if (cellEntry && tableEntry) {
            const [table, tablePath] = tableEntry;
            const [, path] = cellEntry;
            const index = path[path.length - 1];
            const rows = table.children.length;

            Editor.withoutNormalizing(editor, () => {
                range(rows).forEach(row => {
                    removeNodes(editor, { at: [...tablePath, row, index] });
                });
            });
        }
    }
};
