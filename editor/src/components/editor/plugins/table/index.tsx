import { createMarkdownPlatePlugin } from '../../plate-plugin';
import isHotkey, { toKeyCode } from 'is-hotkey';
import { isInTable } from './transforms';
import { getBlockAbove, getNodes, isBlockEnd, isBlockStart, isCollapsed, isRangeAcrossBlocks } from '../../queries';
import { Editor, Element, Node, NodeEntry, Path, Range, TableCellElement, TableElement, Text, Transforms } from 'slate';
import { deleteText, insertNodes, removeNodes } from '../../transforms';
import { table, tableCell, tableRow } from './elements';
import Tool from './tool';
import { getBlockNext } from '../../queries/get-block-next';
import { getBlockPrevious } from '../../queries/get-block-previous';

export const TablePlugin = createMarkdownPlatePlugin({
    tools() {
        return {
            70: Tool
        };
    },
    renderElement() {
        return {
            table,
            tableRow,
            tableCell
        };
    },
    onKeyDown(editor) {
        return e => {
            if (isInTable(editor)) {
                if (isHotkey('shift?+enter', e)) {
                    e.preventDefault();
                    editor.insertText('\n');
                }

                const deleteCode = toKeyCode('delete');

                if (isHotkey(['mod?+shift?+backspace', 'mod?+shift?+delete'], e)) {
                    if (isRangeAcrossBlocks(editor)) {
                        e.preventDefault();
                        const start = Range.start(editor.selection!);
                        const end = Range.end(editor.selection!);

                        const nodes = Array.from(getNodes<TableCellElement>(editor, { match: { type: 'tableCell' } }));
                        Editor.withoutNormalizing(editor, () => {
                            for (const [node, path] of nodes) {
                                if (Path.isDescendant(start.path, path)) {
                                    const length = Node.string(node).length;

                                    const range: Range = {
                                        focus: {
                                            path: [...path, 0],
                                            offset: start.offset,
                                        },
                                        anchor: {
                                            path: [...path, 0],
                                            offset: length,
                                        }
                                    };

                                    if (Range.isExpanded(range)) {
                                        deleteText(editor, { at: range });
                                    }
                                } else if (Path.isDescendant(end.path, path)) {
                                    const range: Range = {
                                        focus: {
                                            path: [...path, 0],
                                            offset: 0,
                                        },
                                        anchor: {
                                            path: [...path, 0],
                                            offset: end.offset,
                                        }
                                    };

                                    if (Range.isExpanded(range)) {
                                        deleteText(editor, { at: range });
                                    }
                                } else {
                                    deleteText(editor, { at: [...path, 0] });
                                }
                            }
                        });

                    } else {
                        if (e.keyCode === deleteCode) {
                            if (isBlockEnd(editor, { match: { type: 'tableCell' } }) && isCollapsed(editor)) {
                                e.preventDefault();
                            }
                        } else {
                            if (isBlockStart(editor, { match: { type: 'tableCell' } }) && isCollapsed(editor)) {
                                e.preventDefault();
                            }
                        }
                    }
                }

                if (isHotkey('shift?+tab', e)) {
                    e.preventDefault();
                    const table = getBlockAbove(editor, { match: { type: 'table' } });
                    const cell = getBlockAbove(editor, { match: { type: 'tableCell' } });
                    if (table && cell) {
                        let target: NodeEntry | undefined;
                        if (e.shiftKey) {
                            target = getBlockPrevious(editor, {
                                at: cell[1],
                                match: { type: 'tableCell' },
                                root: table[1]
                            });
                        } else {
                            target = getBlockNext(editor, {
                                at: cell[1],
                                match: { type: 'tableCell' },
                                root: table[1]
                            },);
                        }

                        if (target) {
                            Transforms.select(editor, target[1]);
                        }
                    }
                }
            }
        };
    },
    withOverrides(editor) {
        const { normalizeNode } = editor;

        editor.normalizeNode = ([node, path]) => {
            //表格
            if (Element.isElementType<TableElement>(node, 'table')) {
                //删除空表格
                if (!Element.isElementList(node.children)) {
                    removeNodes(editor, { at: path });
                    return;
                }

                //确保表格下的元素
                const rows: number[] = [];
                for (const [child, childPath] of Node.children(editor, path)) {
                    if (!Element.isElementType(child, 'tableRow')) {
                        deleteText(editor, { at: childPath });
                    } else {
                        rows.push(child.children.length);
                    }
                }

                const columns = Math.max(...rows);

                rows.forEach((value, row) => {
                    if (value < columns) {
                        //补足列数
                        Array.from({ length: columns - value }).map((_, index) => {
                            insertNodes(editor, {
                                type: 'tableCell',
                                children: [{ text: '' }]
                            }, { at: path.concat(row, value + index) });
                        });
                    }
                });
                node = Node.get(editor, path);
            }

            //行
            if (Element.isElementType<TableElement>(node, 'tableRow')) {
                //删除空表格
                if (!Element.isElementList(node.children)) {
                    removeNodes(editor, { at: path });
                    return;
                }

                //确保表格下的元素
                for (const [child, childPath] of Node.children(editor, path)) {
                    if (!Element.isElementType(child, 'tableCell')) {
                        deleteText(editor, { at: childPath });
                    }
                }
                node = Node.get(editor, path);
            }

            //单元格
            if (Element.isElementType<TableElement>(node, 'tableCell')) {
                //确保单元格下的元素
                for (const [child, childPath] of Node.children(editor, path)) {
                    if (!Text.isText(child) && !editor.isInline(child)) {
                        deleteText(editor, { at: childPath });
                    }
                }
                node = Node.get(editor, path);
            }

            normalizeNode([node, path]);
        };

        return editor;
    }
});
