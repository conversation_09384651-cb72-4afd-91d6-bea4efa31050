import { HistoryPlugin } from './history';
import { ParagraphPlugin } from './paragraph';
import { MarksPlugin } from './marks';
import { ListPlugin } from './list';
import { LinkPlugin } from './link';
import { ImagePlugin } from './image';
import { VideoPlugin } from './video';
import { AudioPlugin } from './audio';
import { AttachmentPlugin } from './attachment';
import { CodePlugin } from './code';
import { ThematicBreakPlugin } from './thematic-break';
import { BlockquotePlugin } from './blockquote';
import { TablePlugin } from './table';
import { FormatPlugin } from './format';
import { XElementPlugin } from './x-element';
import { DirectivePlugin } from './directive';
import { YamlPlugin } from './yaml';
import { MarkdownPlugin } from './markdown';
import { PasteHtmlPlugin } from './paste-html';
import { RawPlugin } from './raw';
import { FullscreenPlugin } from './fullscreen';
import { SearchPlugin } from './search';

export default [
    PasteHtmlPlugin,
    HistoryPlugin,
    MarksPlugin,
    TablePlugin,
    ListPlugin,
    LinkPlugin,
    ImagePlugin,
    AttachmentPlugin,
    VideoPlugin,
    AudioPlugin,
    CodePlugin,
    ThematicBreakPlugin,
    BlockquotePlugin,
    ParagraphPlugin,
    FormatPlugin,
    XElementPlugin,
    DirectivePlugin,
    YamlPlugin,
    MarkdownPlugin,
    FullscreenPlugin,
    SearchPlugin,
    RawPlugin,
];
