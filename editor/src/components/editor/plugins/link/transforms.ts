import { someNode } from '../../queries';
import { Editor, Location } from 'slate';
import { insertNodes, insertText, setNodes, } from '../../transforms';

interface Options {
    url: string;
    text?: string;
    at?: Location;
}

export const setLink = (editor: Editor, { url, text, at }: Options) => {
    setNodes(editor, {
        url,
        value: text || url,
    }, {
        match: { type: 'link' },
        at: at
    });
};

export const insertLink = (editor: Editor, { url, text, at }: Options) => {
    if (editor.isDocument) {
        insertNodes(editor, {
            type: 'link',
            url,
            value: text || url,
            children: [{ text: '' }]
        }, { at });
    } else {
        if (text) {
            insertText(editor, `[${text || url}](<${url}>)`);
        } else {
            insertText(editor, `<${url}>`);
        }
    }
};

export const isInLink = (editor: Editor) => {
    return editor.isDocument && someNode(editor, { match: { type: 'link' } });
};
