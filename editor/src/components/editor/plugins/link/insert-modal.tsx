import useEditor from '../../use-editor';
import useFormatMessage from '../../../../lib/use-format-message';
import { FormEventHandler, useCallback, useEffect, useRef, useState } from 'react';
import { isUrl, styled, useBook } from '@topwrite/common';
import { useFile } from '../../../file-provider';
import { BsFileText } from 'react-icons/bs';
import { OverlayState } from '../../../../lib/use-overlay-state';
import { Form, Modal } from 'react-bootstrap';
import Button from '../../../button';
import { Typeahead } from 'react-bootstrap-typeahead';
import 'react-bootstrap-typeahead/css/Typeahead.bs5.css';
import { insertLink, setLink } from './transforms';
import { Editor, ImageElement, LinkElement } from 'slate';
import { findPath, isExpanded } from '../../queries';
import { setNodes } from '../../transforms';
import { FiLink } from 'react-icons/fi';

interface Option {
    text: string;
    url: string;
    type: 'url' | 'article';
}

interface Props extends OverlayState {
    element?: LinkElement | ImageElement;
}

export default function InsertModal({ onHide, element, ...props }: Props) {
    const t = useFormatMessage();
    const { summary } = useBook();
    const file = useFile();
    const editor = useEditor();

    const [text, setText] = useState<string>('');

    const onShow = useCallback(() => {
        if (isExpanded(editor) && !element) {
            setText(Editor.string(editor, editor.selection!));
        }
    }, [element, editor]);

    const [selected, setSelected] = useState<Option[]>([]);
    const [options, setOptions] = useState<Option[]>([]);

    useEffect(() => {
        if (element) {
            const url = element.type == 'link' ? element.url : element.link;
            if (url) {
                if (isUrl(url)) {
                    if (element.type == 'link') {
                        if (element.url != element.value) {
                            setText(element.value);
                        }
                    }
                    const options: Option[] = [{
                        text: url,
                        url: url,
                        type: 'url'
                    }];

                    setOptions(options);
                    setSelected(options);
                } else {
                    summary.getArticle((article) => {
                        if (file.relative(article.ref) === decodeURI(url)) {
                            const options: Option[] = [{
                                text: article.title,
                                url: article.ref,
                                type: 'article'
                            }];

                            if (element.type == 'link') {
                                if (element.value != article.title) {
                                    setText(element.value);
                                }
                            }
                            setOptions(options);
                            setSelected(options);
                            return true;
                        }
                        return false;
                    });
                }
            }
        }
    }, [element]);

    const onInputChange = useCallback((query: string) => {
        if (isUrl(query)) {
            const options: Option[] = [{
                text: query,
                url: query,
                type: 'url'
            }];
            setOptions(options);
            requestAnimationFrame(() => {
                setSelected(options);
            });
        } else {
            const options: Option[] = [];
            summary.getArticle((article) => {
                if (article.ref && (article.title.includes(query) || file.relative(article.ref) === query)) {
                    options.push({
                        text: article.title,
                        url: article.ref,
                        type: 'article'
                    });
                }
                return false;
            });
            setOptions(options);
            requestAnimationFrame(() => {
                setSelected([]);
            });
        }
    }, [summary, setSelected, setOptions]);

    const onSubmit = useCallback<FormEventHandler>((e) => {
        e.preventDefault();
        if (selected.length > 0) {
            onHide();
            const option = selected[0];

            const link = option.type == 'article' ? {
                url: file.relative(option.url),
                text: text || option.text
            } : {
                url: option.url,
                text: text || undefined
            };

            if (element) {
                const path = findPath(editor, element);
                if (element.type == 'link') {
                    setLink(editor, { ...link, at: path });
                } else {
                    setNodes(editor, { link: link.url }, { at: path });
                }
            } else {
                insertLink(editor, link);
            }
        } else if (element?.type == 'image') {
            onHide();
            const path = findPath(editor, element);
            setNodes(editor, { link: undefined }, { at: path });
        }
    }, [selected, element, editor, text]);

    const form = useRef<HTMLFormElement>(null);

    return <>
        <Modal {...props} onHide={onHide} onShow={onShow}>
            <Modal.Header>{element ? t('editor.tool.link.edit') : t('editor.tool.link.insert')}</Modal.Header>
            <Modal.Body>
                <form ref={form} onSubmit={onSubmit}>
                    <Form.Group className={'mb-3'}>
                        <Form.Label>{t('editor.tool.link.url')}</Form.Label>
                        <Typeahead
                            id='link-search'
                            options={options}
                            minLength={1}
                            autoFocus
                            labelKey={'text'}
                            selected={selected}
                            onChange={useCallback((selected) => {
                                setSelected(selected);
                            }, [setSelected])}
                            onInputChange={onInputChange}
                            renderInput={useCallback(({ inputRef, referenceElementRef, ...inputProps }) => {
                                return <input
                                    {...inputProps}
                                    ref={(node) => {
                                        inputRef(node);
                                        referenceElementRef(node);
                                    }}
                                    type='text'
                                    className='form-control'
                                />;
                            }, [])}
                            emptyLabel={t('editor.tool.link.url.placeholder')}
                            placeholder={t('editor.tool.link.url.placeholder')}
                            renderMenuItemChildren={useCallback((option) => {
                                return <MenuItem>
                                    {option.type == 'article' ? <BsFileText /> : <FiLink />}
                                    {option.text}
                                </MenuItem>;
                            }, [])}
                        />
                    </Form.Group>
                    {(!element || element.type == 'link') && <Form.Group className={'mb-3'}>
                        <Form.Label>{t('editor.tool.link.text')}</Form.Label>
                        <Form.Control
                            placeholder={t('editor.tool.link.text.placeholder')}
                            value={text}
                            onChange={useCallback(e => {
                                setText(e.target.value);
                            }, [setText])}
                        />
                    </Form.Group>}
                    <input type='submit' hidden />
                </form>
            </Modal.Body>
            <Modal.Footer>
                <Button onClick={onHide} variant='secondary'>{t('modal.cancel')}</Button>
                <Button disabled={selected.length == 0 && !(element?.type == 'image')} onClick={(e) => {
                    e.preventDefault();
                    form.current?.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
                }}>{t('modal.ok')}</Button>
            </Modal.Footer>
        </Modal>
    </>;
}

const MenuItem = styled.div`
    display: flex;
    align-items: center;
    line-height: 24px;

    svg {
        margin-right: .5em;
    }
`;
