import { createMarkdownPlatePlugin } from '../../plate-plugin';
import useFormatMessage from '../../../../lib/use-format-message';
import { ComponentProps, useCallback, useEffect, useMemo } from 'react';
import { isUrl, styled, useEventTarget } from '@topwrite/common';
import ElementBar from '../../element-bar';
import { insertLink } from './transforms';
import isHotkey from 'is-hotkey';
import useOverlayState from '../../../../lib/use-overlay-state';
import InsertModal from './insert-modal';
import { Editor, Element, LinkElement, Text } from 'slate';
import { removeNodes } from '../../transforms';
import { VscLinkExternal } from 'react-icons/vsc';
import { BiEditAlt } from 'react-icons/bi';
import useEditor from '../../use-editor';
import { isRangeAcrossBlocks } from '../../queries';
import { FiLink } from 'react-icons/fi';

export const LinkPlugin = createMarkdownPlatePlugin({
    inlineTypes: 'link',
    voidTypes: 'link',
    tools() {
        return {
            60: function({ Component }) {
                const t = useFormatMessage();

                const { show, state } = useOverlayState();

                const et = useEventTarget();
                const editor = useEditor();
                const disabled = useMemo(() => {
                    return isRangeAcrossBlocks(editor) ||
                        Array.from(Editor.nodes(editor, { mode: 'lowest' }))
                             .some(([node]) => !Text.isText(node));
                }, [editor.selection]);

                const openModal = useCallback(() => {
                    if (!disabled) {
                        show();
                    }
                }, [disabled, show]);

                useEffect(() => {
                    et.addEventListener('showLinkModal', openModal);
                    return () => {
                        et.removeEventListener('showLinkModal', openModal);
                    };
                }, [openModal]);

                return <>
                    <Component
                        disabled={disabled}
                        hotKey='Ctrl+L'
                        handler={openModal}
                        title={t(`editor.tool.link`)}
                    >
                        <FiLink />
                    </Component>
                    <InsertModal {...state} />
                </>;
            }
        };
    },
    renderElement() {
        return {
            link({ element, children, attributes }) {
                const { show, state } = useOverlayState();
                const items: ComponentProps<typeof ElementBar>['items'] = [
                    {
                        tooltip: 'editor.toolbar.edit',
                        icon: <BiEditAlt />,
                        onMouseDown: show
                    }
                ];

                if (isUrl(element.url)) {
                    items.push({
                        tooltip: 'editor.toolbar.link.open',
                        icon: <VscLinkExternal />,
                        onMouseDown() {
                            window.open(element.url);
                        }
                    });
                }

                return <ElementBar element={element} items={items}>
                    <Container {...attributes}>
                        {element.value}
                        {children}
                    </Container>
                    <InsertModal element={element} {...state} />
                </ElementBar>;
            }
        };
    },
    onKeyDown() {
        const et = useEventTarget();
        return e => {
            if (isHotkey('mod+l', e)) {
                e.preventDefault();
                et.dispatchEvent(new CustomEvent('showLinkModal'));
            }
        };
    },
    withOverrides(editor) {
        const { insertData, insertText, normalizeNode } = editor;

        editor.insertText = text => {
            if (text && isUrl(text)) {
                insertLink(editor, { url: text });
            } else {
                insertText(text);
            }
        };

        editor.insertData = data => {
            const text = data.getData('text/plain');

            if (text && isUrl(text)) {
                insertLink(editor, { url: text });
            } else {
                insertData(data);
            }
        };

        if (editor.isDocument) {
            editor.normalizeNode = (([node, path]) => {

                if (Element.isElementType<LinkElement>(node, 'link')) {
                    if (!node.value || !node.url) {
                        removeNodes(editor, { at: path });
                        return;
                    }
                }

                normalizeNode([node, path]);
            });
        }

        return editor;
    }
});

const Container = styled.span`
    color: var(--bs-primary);
    cursor: pointer;
    display: inline-block;
    margin: 0 3px;

    &:hover {
        background-color: var(--bs-primary-100);
    }
`;
