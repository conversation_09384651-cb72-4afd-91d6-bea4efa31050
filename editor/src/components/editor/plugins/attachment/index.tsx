import { createMarkdownPlatePlugin } from '../../plate-plugin';
import Attachment from '../../../html/attachment';
import isHotkey from 'is-hotkey';
import { getAbove, isStart, someNode } from '../../queries';
import { Node } from 'slate';
import { GrAttachment } from 'react-icons/gr';
import useOverlayState from '../../../../lib/use-overlay-state';
import useFormatMessage from '../../../../lib/use-format-message';
import InsertModal from './insert-modal';
import { styled } from '@topwrite/common';

export const AttachmentPlugin = createMarkdownPlatePlugin({
    voidTypes: ['text:attachment'],
    tools() {
        return {
            60: ({ Component }) => {
                const t = useFormatMessage();
                const { state, show } = useOverlayState();
                return <>
                    <Component
                        title={t('editor.tool.attachment')}
                        handler={show}
                    >
                        <StyledIcon />
                    </Component>
                    <InsertModal {...state} />
                </>;
            }
        };
    },
    renderElement() {
        return {
            'text:attachment': ({ element, attributes, children }) => {
                return <Attachment {...attributes}>{Node.string(element)}{children}</Attachment>;
            }
        };
    },
    onKeyDown(editor) {
        return e => {
            if (isHotkey('mod?+shift?+backspace', e)) {
                if (editor.isDocument && someNode(editor, {
                    match: {
                        type: 'textDirective',
                        name: 'attachment'
                    }
                })) {
                    const path = getAbove(editor, {
                        match: {
                            type: 'textDirective',
                            name: 'attachment'
                        }
                    })?.[1];
                    if (path) {
                        if (isStart(editor, path)) {
                            e.preventDefault();
                        }
                    }
                }
            }
        };
    }
});

const StyledIcon = styled(GrAttachment)`
    path {
        stroke: currentColor;
    }
`;
