import { Editor } from 'slate';
import path from 'path';
import { insertTextDirective } from '../directive/transforms';

export function insertAttachment(editor: Editor, src: string, name: string = '', size?: string) {

    const attributes = {
        src
    };

    if (size) {
        attributes['size'] = size;
    }

    insertTextDirective(editor, {
        name: 'attachment',
        text: name || path.basename(src),
        attributes
    });
}
