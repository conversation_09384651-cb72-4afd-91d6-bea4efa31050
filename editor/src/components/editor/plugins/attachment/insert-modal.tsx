import { Col, Form, InputGroup, Modal, Row } from 'react-bootstrap';
import useFormatMessage from '../../../../lib/use-format-message';
import Button from '../../../button';
import { useCallback, useState } from 'react';
import useEditor from '../../use-editor';
import { InjectedComponent } from '@topwrite/common';
import { insertAttachment } from './transforms';
import UploadButton, { UploadButtonProps } from '../../upload-button';
import fileSize from 'filesize';
import { OverlayState } from '../../../../lib/use-overlay-state';

export default function InsertModal(props: OverlayState) {
    const t = useFormatMessage();

    const [url, setUrl] = useState<string>('');
    const [name, setName] = useState<string>('');
    const [size, setSize] = useState<string>('');

    const editor = useEditor();
    const handler = useCallback(() => {
        props.onHide();
        insertAttachment(editor, url, name, size);
    }, [editor, url, name, size]);

    const onSuccess = useCallback((result: { path: string, name: string, size: number }) => {
        setUrl(result.path);
        setName(result.name);
        setSize(fileSize(result.size));
    }, []);

    const component = useCallback(function({ onSuccess }: Pick<UploadButtonProps, 'onSuccess'>) {
        if (!editor.supportLargeFile()) {
            return null;
        }
        return <UploadButton onSuccess={onSuccess} />;
    }, [editor]);

    return <Modal {...props}>
        <Modal.Header>{t('editor.tool.attachment.title')}</Modal.Header>
        <Modal.Body>
            <Form.Group className={'mb-3'}>
                <InputGroup>
                    <Form.Control
                        autoFocus
                        placeholder={t('editor.tool.attachment.placeholder')}
                        value={url}
                        onChange={e => {
                            setUrl(e.target.value);
                        }}
                    />
                    <InjectedComponent
                        component={component}
                        props={{ onSuccess }}
                        role={'editor:tool:attachment:upload'}
                    />
                </InputGroup>
            </Form.Group>
            <Row>
                <Col>
                    <Form.Control placeholder={t('editor.tool.attachment.name')} value={name} onChange={e => {
                        setName(e.target.value);
                    }} />
                </Col>
                <Col>
                    <Form.Control placeholder={t('editor.tool.attachment.size')} value={size} onChange={e => {
                        setSize(e.target.value);
                    }} />
                </Col>
            </Row>
        </Modal.Body>
        <Modal.Footer>
            <Button onClick={props.onHide} variant={'secondary'}>{t('modal.cancel')}</Button>
            <Button disabled={!url} variant={'primary'} onClick={handler}>{t('modal.insert')}</Button>
        </Modal.Footer>
    </Modal>;
}
