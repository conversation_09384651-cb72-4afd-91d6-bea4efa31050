import { Editor, Path, Range } from 'slate';
import { end, pointRef, someNode } from '../../queries';
import { insertNodes, setNodes, unwrapNodes, withoutNormalizing, wrapNodes } from '../../transforms';
import { isParagraphActive } from '../paragraph/transforms';

export const isCodeActive = (editor: Editor) => {
    return editor.isDocument && someNode(editor, { match: { type: 'code' } });
};

export const toggleCode = (editor: Editor) => {
    if (editor.isDocument) {

        const isActive = isCodeActive(editor);

        withoutNormalizing(editor, () => {
            setNodes(editor, { type: 'paragraph' }, { match: { type: 'codeLine' } });

            unwrapNodes(editor, {
                match: { type: 'code' },
                split: true,
            });

            if (!isActive && isParagraphActive(editor)) {
                setNodes(editor, { type: 'codeLine', });
                wrapNodes(editor, { type: 'code', children: [] });
            }
        });
    } else {
        const { selection } = editor;
        if (selection) {
            const startPoint = Range.start(selection);
            const endPoint = Range.end(selection);

            const endRef = pointRef(editor, endPoint);

            insertN<PERSON>(editor, {
                type: 'line',
                children: [{
                    text: '```'
                }]
            }, { at: Path.parent(startPoint.path) });

            const endAt = endRef.unref();

            insertNodes(editor, {
                type: 'line',
                children: [{
                    text: '```'
                }]
            }, { at: end(editor, endAt!.path) });
        }
    }
};
