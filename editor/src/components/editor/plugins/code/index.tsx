import { createMarkdownPlatePlugin } from '../../plate-plugin';
import { isCodeActive, toggleCode } from './transforms';
import { getBlockAbove, isCollapsed, isExpanded, isStart, nodes, someNode } from '../../queries';
import isHotkey from 'is-hotkey';
import { Editor, Element, Node, Text } from 'slate';
import { deleteText, insertText, removeNodes, unsetNodes } from '../../transforms';
import { useCallback } from 'react';
import useEditor from '../../use-editor';
import useFormatMessage from '../../../../lib/use-format-message';
import { BsCodeSlash } from 'react-icons/bs';
import { inParagraph } from '../paragraph/transforms';
import { Code } from './elements';

export const CodePlugin = createMarkdownPlatePlugin({
    tools() {
        return {
            70: function({ Component }) {
                const t = useFormatMessage();
                const editor = useEditor();

                const active = isCodeActive(editor);
                const disabled = !inParagraph(editor);
                const handler = useCallback(() => {
                    toggleCode(editor);
                }, [editor]);

                return <Component
                    active={active}
                    handler={handler}
                    disabled={disabled}
                    hotKey='Ctrl+Shift+E'
                    title={t(`editor.tool.code`)}
                >
                    <BsCodeSlash />
                </Component>;
            }
        };
    },
    renderElement() {
        return {
            code: Code
        };
    },
    onKeyDown(editor) {
        return (e) => {
            if (isHotkey('mod+shift+e', e)) {
                e.preventDefault();
                toggleCode(editor);
            }

            if (editor.isDocument) {
                if (isHotkey('shift?+tab', e) && isCodeActive(editor)) {
                    e.preventDefault();
                    if (isExpanded(editor) || e.shiftKey) {
                        for (const [node, path] of nodes(editor, { match: { type: 'codeLine' } })) {
                            const at = { path: path.concat(0), offset: 0 };
                            if (e.shiftKey) {
                                const text = Node.string(node);
                                const isSpace = text.match(/^\s{1,4}/);
                                if (isSpace) {
                                    deleteText(editor, {
                                        at,
                                        distance: isSpace[0].length
                                    });
                                }
                            } else {
                                insertText(editor, '    ', { at });
                            }
                        }
                    } else {
                        editor.insertText('    ');
                    }
                }

                if (isHotkey('shift?+enter', e) && someNode(editor, { match: { type: 'codeLine' } })) {
                    e.preventDefault();
                    Editor.insertBreak(editor);
                }

                if (isHotkey('shift?+backspace', e)) {
                    if (isCollapsed(editor)) {
                        const above = getBlockAbove(editor, { match: { type: 'code' } });
                        if (above) {
                            const [, path] = above;
                            if (isStart(editor, path)) {
                                e.preventDefault();
                                toggleCode(editor);
                            }
                        }
                    }
                }
            }
        };
    },
    withOverrides(editor) {
        const { insertData, normalizeNode } = editor;

        editor.insertData = data => {
            if (someNode(editor, { match: { type: 'codeLine' } })) {
                const text = data.getData('text/plain');
                insertText(editor, text);
                return;
            }
            insertData(data);
        };

        editor.normalizeNode = ([node, path]) => {
            if (Element.isElementType(node, 'codeLine')) {
                for (const [child, childPath] of Node.children(editor, path)) {
                    if (!Text.isText(child)) {
                        removeNodes(editor, { at: childPath });
                    } else {
                        const { text, ...rest } = child;
                        const marks = Object.keys(rest);
                        if (marks.length > 0) {
                            unsetNodes(editor, Object.keys(rest), { at: childPath });
                        }
                    }
                }
            }

            normalizeNode([Node.get(editor, path), path]);
        };
        return editor;
    }
});

