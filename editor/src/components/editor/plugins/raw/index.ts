import { createPlatePlugin } from '../../plate-plugin';
import isHotkey from 'is-hotkey';
import { isExpanded } from '../../queries';
import { Editor, Element, Node, Transforms } from 'slate';

export const RawPlugin = createPlatePlugin({
    available(editor) {
        return !editor.isDocument;
    },
    onKeyDown(editor) {
        return (e) => {
            if (isHotkey(['tab', 'tab+shift'], e)) {
                e.preventDefault();
                if (isExpanded(editor) || e.shiftKey) {
                    for (const [node, path] of Editor.nodes(editor, { match: n => Element.isElement(n) })) {
                        const at = { path: path.concat(0), offset: 0 };
                        if (e.shiftKey) {
                            const text = Node.string(node);
                            const isSpace = text.match(/^\s{1,4}/);
                            if (isSpace) {
                                Transforms.delete(editor, {
                                    at,
                                    distance: isSpace[0].length
                                });
                            }
                        } else {
                            Transforms.insertText(editor, '    ', { at });
                        }
                    }
                } else {
                    editor.insertText('    ');
                }
            }
        };
    }
});
