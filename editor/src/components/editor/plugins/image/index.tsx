import { React<PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';
import { css, styled, useEventTarget } from '@topwrite/common';
import { createMarkdownPlatePlugin } from '../../plate-plugin';
import ElementBar from '../../element-bar';
import useEditor from '../../use-editor';
import Toast from '../../../toast';
import { insertImage } from './transforms';
import Img from '../../../html/img';
import { Resizable, ResizeCallback } from 're-resizable';
import useEditorActions from '../../use-editor-actions';
import { ReactComponent as OriginalSizeIcon } from '../../../../images/original-size.svg';
import { ReactComponent as FullSizeIcon } from '../../../../images/full-size.svg';
import useFormatMessage from '../../../../lib/use-format-message';
import useOverlayState from '../../../../lib/use-overlay-state';
import isHotkey from 'is-hotkey';
import { BsImage } from 'react-icons/bs';
import InsertModal from './insert-modal';
import { isNodeFocused } from '../../queries';
import Anchor from '../../../html/anchor';
import InsertLinkModal from '../link/insert-modal';
import { VscLinkExternal } from 'react-icons/vsc';
import { FiLink } from 'react-icons/fi';

function getPixel(str: string | number | undefined, maxWidth: number): number | undefined {
    if (!str) {
        return undefined;
    }
    if (typeof str === 'number') {
        return str;
    }

    switch (true) {
        case str.endsWith('%'):
            return parseInt(str) / 100 * maxWidth;
        default:
            return parseInt(str);
    }
}

export const ImagePlugin = createMarkdownPlatePlugin({
    voidTypes: 'image',
    inlineTypes: 'image',
    tools() {
        return {
            60: ({ Component }) => {
                const t = useFormatMessage();

                const { show, state } = useOverlayState();

                const et = useEventTarget();

                useEffect(() => {
                    et.addEventListener('showImageModal', show);
                    return () => {
                        et.removeEventListener('showImageModal', show);
                    };
                }, [show]);

                return <>
                    <Component
                        handler={show}
                        hotKey='Ctrl+Alt+Y'
                        title={t('editor.tool.image')}>
                        <BsImage />
                    </Component>
                    <InsertModal {...state} />
                </>;
            }
        };
    },
    renderElement() {
        return {
            image({ element, children, attributes }) {
                const { url, alt, title, link } = element;
                const { findPath, setNodes } = useEditorActions();
                const editor = useEditor();
                const path = findPath(editor, element);

                const [resizable, setResizable] = useState<{ width: number; height: number; }>();
                const [size, setSize] = useState<{ width: number; height: number; }>();

                const [maxWidth, setMaxWidth] = useState(832);

                const width = getPixel(element.width, maxWidth);
                const height = getPixel(element.height, maxWidth);

                const focused = useMemo(() => {
                    return isNodeFocused(editor, element);
                }, [editor.selection]);

                useEffect(() => {
                    if (attributes.ref.current && attributes.ref.current.parentElement) {
                        setMaxWidth(attributes.ref.current.parentElement.clientWidth - 6);
                    }
                }, [attributes.ref]);

                const onLoad = useCallback<ReactEventHandler<HTMLImageElement>>((e) => {
                    const { naturalWidth, naturalHeight } = e.currentTarget;
                    setSize((size) => {
                        if (!size || size.width != naturalWidth || size.height != naturalHeight) {
                            return {
                                width: naturalWidth,
                                height: naturalHeight
                            };
                        }
                        return size;
                    });
                }, []);

                useEffect(() => {
                    if (size) {
                        const { width: naturalWidth, height: naturalHeight } = size;
                        const ratio = naturalWidth / naturalHeight;

                        if (width && height) {
                            if ((width / height) === ratio) {
                                setResizable({
                                    width,
                                    height
                                });
                            }
                        } else if (width) {
                            setResizable({
                                width,
                                height: width / ratio
                            });
                        } else if (height) {
                            setResizable({
                                width: height * ratio,
                                height,
                            });
                        } else {
                            if (naturalWidth < maxWidth) {
                                setResizable({
                                    width: naturalWidth,
                                    height: naturalHeight,
                                });
                            } else {
                                setResizable({
                                    width: maxWidth,
                                    height: maxWidth / ratio,
                                });
                            }
                        }
                    }
                }, [size, width, height, maxWidth]);

                const img = <Img onLoad={onLoad} src={url} alt={alt || ''} title={title} />;

                const onResizeStop = useCallback<ResizeCallback>((...args) => {
                    const [, , ref, change] = args;
                    setResizable(resizable => {
                        if (resizable) {
                            return {
                                width: resizable.width + change.width,
                                height: resizable.height + change.height
                            };
                        }
                        return resizable;
                    });
                    setNodes(
                        editor,
                        {
                            width: ref.clientWidth >= maxWidth ? '100%' : ref.clientWidth,
                            height: undefined
                        },
                        { at: path }
                    );
                }, [editor, path, maxWidth]);

                const { show, state } = useOverlayState();

                return <ElementBar
                    element={element}
                    focus
                    items={[
                        {
                            tooltip: 'editor.toolbar.image.original',
                            icon: <OriginalSizeIcon fill={'currentColor'} />,
                            onMouseDown() {
                                setNodes(
                                    editor,
                                    {
                                        width: undefined,
                                        height: undefined
                                    },
                                    { at: path }
                                );
                            }
                        },
                        {
                            tooltip: 'editor.toolbar.image.full',
                            icon: <FullSizeIcon fill={'currentColor'} />,
                            onMouseDown() {
                                setNodes(
                                    editor,
                                    {
                                        width: '100%',
                                        height: undefined
                                    },
                                    { at: path }
                                );
                            }
                        },
                        {
                            tooltip: 'editor.tool.link.edit',
                            icon: <FiLink />,
                            onMouseDown: show
                        }
                    ]}
                >
                    <Container {...attributes} focused={focused}>
                        {resizable && focused ? <Resizable
                            maxWidth={maxWidth}
                            size={resizable}
                            minHeight={16}
                            onResizeStop={onResizeStop}
                            as={Resize}
                            handleComponent={{
                                topRight: <Circle />,
                                bottomRight: <Circle />,
                                bottomLeft: <Circle />,
                                topLeft: <Circle />,
                            }}
                            lockAspectRatio
                        >
                            {img}
                        </Resizable> : <Resize style={resizable}>{img}</Resize>}
                        {link && <Link href={link}><VscLinkExternal /></Link>}
                        {children}
                        <InsertLinkModal element={element} {...state} />
                    </Container>
                </ElementBar>;
            }
        };
    },
    onKeyDown() {
        const et = useEventTarget();
        return e => {
            if (isHotkey('mod+alt+y', e)) {
                e.preventDefault();
                et.dispatchEvent(new CustomEvent('showImageModal'));
            }
        };
    },
    withOverrides(editor) {
        const { insertData } = editor;

        editor.insertData = (data: DataTransfer) => {
            const { files } = data;
            const { selection } = editor;
            if (selection && files && files.length > 0) {
                const file = files[0];

                const [mime] = file.type.split('/');

                if (mime === 'image') {
                    editor.setLoading(true);
                    editor.uploadFile(file)
                          .then(file => {
                              insertImage(editor, file.path);
                          })
                          .catch(e => {
                              Toast.error(e.message);
                              console.error(e);
                          })
                          .finally(() => {
                              editor.setLoading(false);
                          });

                    return;
                }
            }
            insertData(data);
        };

        return editor;
    }
});

const Link = styled(Anchor)`
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 9px;
    right: 9px;
    padding: 0 4px;
    background: rgba(38, 38, 38, 0.6);
    border-radius: 4px;
    cursor: pointer;
    display: none;

    svg {
        fill: #FFFFFF;
        width: 24px;
        height: 24px;
        padding: 4px;
    }
`;

const Circle = styled.div`
    top: 3px;
    left: 3px;
    width: 14px;
    height: 14px;
    border: 2px solid #FFFFFF;
    background-color: var(--bs-primary);
    border-radius: 10px;
    position: absolute;
`;

const Container = styled.span<{ focused: boolean; }>`
    display: inline-flex;
    max-width: 100%;
    margin: 0 3px;
    position: relative;

    ${props => props.focused && css`
        outline: 1px solid var(--bs-primary-400) !important;
    `}
    &:hover {
        outline: 1px solid var(--bs-primary-200);

        ${Link} {
            display: block;
        }
    }

    img {
        max-width: 100%;
    }
`;

const Resize = styled.span`
    display: inline-flex;

    img {
        width: 100%;
        height: 100%;
    }
`;
