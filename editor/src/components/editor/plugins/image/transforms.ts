import { Editor, Location } from 'slate';
import { insertNodes, insertText } from '../../transforms';

interface Options {
    at?: Location;
}

export function insertImage(editor: Editor, url: string, options: Options = {}) {
    let { at } = options;

    if (editor.isDocument) {
        insertNodes(editor, {
            type: 'image',
            children: [
                { text: '' }
            ],
            url: url
        }, {
            at
        });
    } else {
        if (/[ \t\r\n]/.test(url)) {
            url = `<${url}>`;
        }

        insertText(editor, `![](${url})`, { at });
    }
}
