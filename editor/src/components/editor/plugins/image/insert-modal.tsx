import { Form, InputGroup, Modal } from 'react-bootstrap';
import { InjectedComponent, InjectedComponentSet, styled } from '@topwrite/common';
import Button from '../../../button';
import { useCallback, useState } from 'react';
import useEditor from '../../use-editor';
import { insertImage } from './transforms';
import Img from '../../../html/img';
import useFormatMessage from '../../../../lib/use-format-message';
import { OverlayState } from '../../../../lib/use-overlay-state';
import UploadButton, { UploadButtonProps } from '../../upload-button';

export default function InsertModal({ onHide, ...props }: OverlayState) {
    const t = useFormatMessage();

    const [url, setUrl] = useState<string>('');

    const editor = useEditor();

    const component = useCallback(function({ onSuccess }: Pick<UploadButtonProps, 'onSuccess'>) {
        return <UploadButton onSuccess={onSuccess} view='preview' accept='image/*' />;
    }, []);

    return <>
        <Modal {...props} onHide={onHide} size={'lg'}>
            <Modal.Header>{t('editor.tool.image.title')}</Modal.Header>
            <Modal.Body>
                <Container>
                    <Form.Group>
                        <InputGroup>
                            <Form.Control
                                autoFocus
                                placeholder={t('editor.tool.image.placeholder')}
                                value={url}
                                onChange={e => {
                                    setUrl(e.target.value);
                                }}
                            />
                            <InjectedComponent
                                component={component}
                                props={{
                                    onSuccess: result => setUrl(result.path)
                                }}
                                role={'editor:tool:image:upload'}
                            />
                            <InjectedComponentSet
                                component={Button}
                                context={{
                                    onSuccess: (path: string) => setUrl(path)
                                }}
                                role={'editor:tool:image:button'}
                            />
                        </InputGroup>
                    </Form.Group>
                    <Preview>
                        {url ? <Img src={url} /> : <p>{t('editor.tool.image.preview')}</p>}
                    </Preview>
                </Container>
            </Modal.Body>
            <Modal.Footer>
                <Button onClick={onHide} variant='secondary'>{t('modal.cancel')}</Button>
                <Button disabled={!url} onClick={(e) => {
                    e.preventDefault();
                    if (url) {
                        onHide();
                        insertImage(editor, url);
                    }
                }}>{t('modal.insert')}</Button>
            </Modal.Footer>
        </Modal>
    </>;
}


const Preview = styled.div`
  padding: 60px;
  text-align: center;

  img {
    max-width: 70%;
    border: 1px solid #8c919b;
  }

  p {
    color: var(--bs-gray-500);
    font-size: 20px;
    margin: 0;
  }
`;
const Container = styled.div`

`;
