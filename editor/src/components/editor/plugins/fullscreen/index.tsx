import { createPlatePlugin } from '../../plate-plugin';
import useFormatMessage from '../../../../lib/use-format-message';
import { BsFullscreen } from 'react-icons/bs';
import { useCallback, useEffect, useState } from 'react';

const isActive = () => document.fullscreenElement === document.body;
const isFullscreen = () => window.matchMedia('(display-mode: fullscreen)').matches && !document.fullscreenElement;

export const FullscreenPlugin = createPlatePlugin({
    tools() {
        return {
            '-10': ({ Component }) => {
                const t = useFormatMessage();
                const [active, setActive] = useState(isActive);
                const [fullscreen, setFullscreen] = useState(isFullscreen);

                useEffect(() => {
                    const listener = () => {
                        setActive(isActive());
                    };
                    const resize = () => {
                        setFullscreen(isFullscreen());
                    };
                    document.body.addEventListener('fullscreenchange', listener);
                    window.addEventListener('resize', resize);

                    return () => {
                        document.body.removeEventListener('fullscreenchange', listener);
                        window.removeEventListener('resize', resize);
                    };
                }, []);

                const handler = useCallback(async () => {
                    if (active) {
                        await document.exitFullscreen();
                    } else {
                        await document.body.requestFullscreen();
                    }
                }, [active]);

                return <Component
                    title={t('editor.tool.fullscreen')}
                    handler={handler}
                    active={active}
                    disabled={fullscreen}
                >
                    <BsFullscreen />
                </Component>;
            }
        };
    },
    available() {
        return document.fullscreenEnabled;
    }
});
