import { createPlatePlugin } from '../../plate-plugin';
import { css, styled } from '@topwrite/common';

export const SearchPlugin = createPlatePlugin({
    renderLeaf() {
        return ({ leaf, children }) => {
            return <Container $lowlight={leaf.lowlight} $highlight={leaf.highlight}>
                {children}
            </Container>;
        };
    },
});

const Container = styled.span<{ $highlight?: boolean, $lowlight?: boolean }>`
  ${({ $lowlight }) => $lowlight && css`
    background-color: rgba(234, 152, 0, 0.33);
  `};
  
  ${({ $highlight }) => $highlight && css`
    background-color: rgba(234, 152, 0, 0.33);
    outline: 1px solid var(--bs-orange);
  `};
`;
