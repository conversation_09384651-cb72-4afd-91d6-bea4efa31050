import { createMarkdownPlatePlugin } from '../../plate-plugin';
import { styled } from '@topwrite/common';
import { isContainerDirective, isDirectiveLabel } from './queries';
import { insertNodes } from '../../transforms';
import { Node } from 'slate';

export const DirectivePlugin = createMarkdownPlatePlugin({
    inlineTypes: ['textDirective'],
    renderElement() {
        return {
            textDirective({ element, attributes, children }) {
                return <Text contentEditable={false} {...attributes} >
                    <Leaf>{children}</Leaf>
                    <Name>directive:{element.name}</Name>
                </Text>;
            },
            leafDirective({ element, children, attributes, ElementMenu }) {
                return <ElementMenu element={element} {...attributes} contentEditable={false}>
                    <Container>
                        {children}
                        <Bg>{element.name}</Bg>
                    </Container>
                </ElementMenu>;
            },
            containerDirective({ element, children, attributes, ElementMenu }) {
                return <ElementMenu element={element} {...attributes} contentEditable={false}>
                    <Container>
                        {children}
                        <Bg>{element.name}</Bg>
                    </Container>
                </ElementMenu>;
            },
            directiveLabel({ children, attributes }) {
                return <Label {...attributes}>{children}</Label>;
            }
        };
    },
    withOverrides(editor) {
        const { normalizeNode } = editor;

        editor.normalizeNode = ([node, path]) => {

            if (isContainerDirective(node)) {
                if (node.children.length === 0 || !isDirectiveLabel(node.children[0])) {
                    insertNodes(editor, {
                        type: 'directiveLabel',
                        name: node.name,
                        children: [{ text: '' }]
                    }, { at: path.concat(0) });

                    node = Node.get(editor, path);
                }
            }

            normalizeNode([node, path]);
        };

        return editor;
    },
    priority: 100
});

const Label = styled.div`
  height: 0px;
  color: transparent;
  outline: none;
  position: absolute;
`;

const Name = styled.span`
`;

const Leaf = styled.span`
  display: none;
`;

const Text = styled.span`
  border-radius: 5px;
  display: inline-block;
  color: #FFF;
  background-color: var(--bs-primary-300);
  padding: 0 10px;
`;

const Bg = styled.div`
  color: #FFF;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  background-color: var(--bs-primary-300);
`;

const Container = styled.div`
  height: 50px;
  overflow: hidden;
  border-radius: 5px;
  position: relative;
  margin: 1em 0;

  & > * {
    position: absolute;
  }
`;
