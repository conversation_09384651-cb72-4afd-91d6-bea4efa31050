import { createMarkdownPlatePlugin } from '../../plate-plugin';
import Audio from '../../../html/audio';
import { styled } from '@topwrite/common';
import { MdAudiotrack } from 'react-icons/md';
import useFormatMessage from '../../../../lib/use-format-message';
import useOverlayState from '../../../../lib/use-overlay-state';
import InsertModal from './insert-modal';
import useEditor from '../../use-editor';
import { inParagraph } from '../paragraph/transforms';

export const AudioPlugin = createMarkdownPlatePlugin({
    voidTypes: ['leaf:audio', 'text:audio'],
    tools() {
        return {
            60: ({ Component }) => {
                const t = useFormatMessage();
                const { state, show } = useOverlayState();
                const editor = useEditor();
                const disabled = !inParagraph(editor);
                return <>
                    <Component
                        title={t('editor.tool.audio')}
                        handler={show}
                        disabled={disabled}
                    >
                        <MdAudiotrack />
                    </Component>
                    <InsertModal {...state} />
                </>;
            }
        };
    },
    renderElement() {
        return {
            'text:audio': ({ element, attributes, children }) => {
                return <Container {...attributes}>
                    <Audio {...element.attributes} />
                    {children}
                </Container>;
            },
            'leaf:audio': ({ children, element, attributes, ElementMenu }) => {
                return <ElementMenu element={element} {...attributes} as={BlockContainer} contentEditable={false}>
                    <Audio {...element.attributes} />
                    {children}
                </ElementMenu>;
            }
        };
    },
});

const BlockContainer = styled.div`
  margin: 1em 0;

  audio {
    display: block;
    width: 100%;
    outline: none;
  }
`;


const Container = styled.span`
  &&& {
    width: 100%;
    margin: 0;

    audio {
      width: 100%;
      outline: none;
    }
  }
`;
