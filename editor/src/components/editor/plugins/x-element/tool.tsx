import { InjectedComponent } from '@topwrite/common';
import { BiEditAlt } from 'react-icons/bi';
import { useCallback } from 'react';
import prompt from '../../../modal/prompt';
import { Transforms, XBlockElement, XInlineElement } from 'slate';
import { ReactEditor, useSlate } from 'slate-react';
import useFormatMessage from '../../../../lib/use-format-message';
import { Item } from '../../element-bar/item';

export interface XBlockEditProps {
    element: XBlockElement;
    type: string;
    onMouseDown: () => void;
}

export interface XInlineEditProps {
    element: XInlineElement;
    type: string;
    onMouseDown: () => void;
}

interface XElementToolEditProps {
    element: XBlockElement | XInlineElement;
    type: string;
    onMouseDown: () => void;
}

export function XElementToolEdit({ onMouseDown }: XElementToolEditProps) {
    return <Item tooltip='editor.toolbar.edit' icon={<BiEditAlt />} onMouseDown={onMouseDown} />;
}

export interface XBlockProps {
    element: XBlockElement;
    type: string;
}

export interface XInlineProps {
    element: XInlineElement;
    type: string;
}

export function XElementTool({ element, type }: XBlockProps | XInlineProps) {
    const { name, value } = element;
    const editor = useSlate();
    const t = useFormatMessage();

    const handleEdit = useCallback(async () => {

        const at = ReactEditor.findPath(editor, element);

        const uiSchema = type === 'inline' ? {
            'ui:options': {
                label: false
            },
        } : {
            'ui:widget': 'textarea',
            'ui:options': {
                label: false,
                autofocus: true,
                rows: 5
            },
        };

        const result = await prompt({
            title: t('editor.toolbar.edit'),
            schema: {
                type: 'object',
                properties: {
                    content: {
                        type: 'string'
                    }
                }
            },
            formData: {
                content: value
            },
            uiSchema: {
                content: uiSchema
            },
        });

        if (result) {
            Transforms.setNodes(editor, {
                value: result.content
            }, {
                at
            });
        }
    }, [editor, element]);

    return <InjectedComponent
        role={`editor:${type}:${name}:edit`}
        props={{ onMouseDown: handleEdit, element, type }}
        component={XElementToolEdit}
    />;
}
