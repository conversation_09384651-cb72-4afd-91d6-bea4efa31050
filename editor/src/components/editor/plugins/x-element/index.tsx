import { createMarkdownPlatePlugin } from '../../plate-plugin';
import { InjectedComponent, InjectedComponentSet, styled } from '@topwrite/common';
import ElementBar from '../../element-bar';
import { XElementTool } from './tool';

export interface XComponentProps {
    name: string;
    type: string;
    value: any;
}

export const BlockComponent = ({ value }: XComponentProps) => {
    return <Block>{value}</Block>;
};

export const InlineComponent = ({ value }: XComponentProps) => {
    return <code>{value}</code>;
};

export const XElementPlugin = createMarkdownPlatePlugin({
    voidTypes: ['xBlock', 'xInline'],
    inlineTypes: 'xInline',
    tools() {
        return {
            80: ({ Component }) => {
                return <InjectedComponentSet
                    role='editor:tool'
                    component={Component}
                />;
            }
        };
    },
    renderElement() {
        return {
            xBlock({ attributes, children, element, ElementMenu }) {
                const { value, name, parameter } = element;
                const props = { value, parameter, name, type: 'block' };

                return <ElementMenu
                    contentEditable={false}
                    items={false}
                    element={element}
                    {...attributes}
                >
                    <ElementBar
                        element={element}
                        renderItems={() => <InjectedComponent
                            role={`editor:block:${name}`}
                            props={{
                                element: element,
                                type: 'block',
                            }}
                            component={XElementTool}
                        />}
                    >
                        <BlockContainer>
                            {children}
                            <InjectedComponent
                                role={`block:${name}`}
                                props={props}
                                component={BlockComponent}
                            />
                        </BlockContainer>
                    </ElementBar>
                </ElementMenu>;
            },
            xInline({ attributes, children, element }) {
                const { value, name } = element;
                const props = { value, name, type: 'inline' };

                return <ElementBar
                    element={element}
                    renderItems={() => <InjectedComponent
                        role={`editor:inline:${name}`}
                        props={{
                            element: element,
                            type: 'inline',
                        }}
                        component={XElementTool}
                    />}
                >
                    <InlineContainer contentEditable={false} {...attributes}>
                        {children}
                        <InjectedComponent
                            role={`inline:${name}`}
                            props={props}
                            component={InlineComponent}
                        />
                    </InlineContainer>
                </ElementBar>;
            }
        };
    }
});

const BlockContainer = styled.div`
  margin: 1em 0;

  [data-slate-spacer] {
    left: 50%;
    top: 50%;
  }
`;

const InlineContainer = styled.span`
  display: inline-flex;
  margin: 0 3px;
`;

const Block = styled.div`
  padding: 1.05em;
  overflow: auto;
  line-height: 1.45;
  background-color: #f7f7f7;
  border: 0;
  border-radius: 3px;
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  tab-size: 4;
  word-break: break-all;
`;
