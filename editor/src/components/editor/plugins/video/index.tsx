import { createMarkdownPlatePlugin } from '../../plate-plugin';
import Video from '../../../html/video';
import { styled } from '@topwrite/common';
import { FiVideo } from 'react-icons/fi';
import useFormatMessage from '../../../../lib/use-format-message';
import useOverlayState from '../../../../lib/use-overlay-state';
import InsertModal from './insert-modal';
import { inParagraph } from '../paragraph/transforms';
import useEditor from '../../use-editor';

export const VideoPlugin = createMarkdownPlatePlugin({
    voidTypes: ['leaf:video', 'text:video'],
    tools() {
        return {
            60: ({ Component }) => {
                const t = useFormatMessage();
                const { state, show } = useOverlayState();
                const editor = useEditor();
                const disabled = !inParagraph(editor);
                return <>
                    <Component
                        disabled={disabled}
                        handler={show}
                        title={t('editor.tool.video')}
                    >
                        <FiVideo />
                    </Component>
                    <InsertModal {...state} />
                </>;
            }
        };
    },
    renderElement() {
        return {
            'text:video': ({ element, attributes, children }) => {
                return <Container {...attributes}>
                    {children}
                    <Video {...element.attributes} />
                </Container>;
            },
            'leaf:video': ({ children, element, attributes, ElementMenu }) => {
                return <ElementMenu element={element} {...attributes} as={BlockContainer}>
                    {children}
                    <Video {...element.attributes} />
                </ElementMenu>;
            }
        };
    }
});

const BlockContainer = styled.div`
  margin: 1em 0;

  video {
    display: block;
    width: 100%;
    outline: none;
  }

  [data-slate-spacer] {
    left: 50%;
    top: 50%;
  }
`;

const Container = styled.span`
  &&& {
    width: 100%;
    margin: 0;

    video {
      width: 100%;
      outline: none;
    }

    [data-slate-spacer] {
      left: 50%;
      top: 50%;
    }
  }

`;
