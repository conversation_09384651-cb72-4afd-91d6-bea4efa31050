import { Form, InputGroup, Modal } from 'react-bootstrap';
import useFormatMessage from '../../../../lib/use-format-message';
import Button from '../../../button';
import { useCallback, useState } from 'react';
import useEditor from '../../use-editor';
import { insertVideo } from './transforms';
import { InjectedComponent } from '@topwrite/common';
import UploadButton, { UploadButtonProps } from '../../upload-button';
import { OverlayState } from '../../../../lib/use-overlay-state';

export default function InsertModal(props: OverlayState) {
    const t = useFormatMessage();
    const [url, setUrl] = useState<string>('');
    const editor = useEditor();
    const handler = useCallback(() => {
        props.onHide();
        insertVideo(editor, url);
    }, [editor, url]);

    const component = useCallback(function({ onSuccess }: Pick<UploadButtonProps, 'onSuccess'>) {
        if (!editor.supportLargeFile()) {
            return null;
        }
        return <UploadButton onSuccess={onSuccess} accept='video/*' />;
    }, [editor]);

    return <Modal {...props}>
        <Modal.Header>{t('editor.tool.video.title')}</Modal.Header>
        <Modal.Body>
            <Form.Group>
                <InputGroup>
                    <Form.Control
                        autoFocus
                        placeholder={t('editor.tool.video.placeholder')}
                        value={url}
                        onChange={e => {
                            setUrl(e.target.value);
                        }}
                    />
                    <InjectedComponent
                        component={component}
                        props={{ onSuccess: result => setUrl(result.path) }}
                        role={'editor:tool:video:upload'}
                    />
                </InputGroup>
            </Form.Group>
        </Modal.Body>
        <Modal.Footer>
            <Button onClick={props.onHide} variant={'secondary'}>{t('modal.cancel')}</Button>
            <Button disabled={!url} variant={'primary'} onClick={handler}>{t('modal.insert')}</Button>
        </Modal.Footer>
    </Modal>;
}
