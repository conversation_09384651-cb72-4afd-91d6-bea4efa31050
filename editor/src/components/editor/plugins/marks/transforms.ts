import { <PERSON>, <PERSON> } from 'slate';
import { hasMark } from '../../queries';
import { removeMark } from '../../transforms/remove-mark';
import { wrapText } from '../../transforms';

export function isMarkActive(editor: Editor, mark: Mark) {
    return editor.isDocument && hasMark(editor, mark);
}

const MARKS = {
    strong: '**',
    emphasis: '*',
    delete: '~~',
    code: '`'
};

export function toggleMark(editor: Editor, mark: Mark) {
    if (editor.isDocument) {
        const isActive = isMarkActive(editor, mark);

        if (isActive) {
            removeMark(editor, { key: mark });
        } else {
            editor.addMark(mark, true);
        }
    } else {
        wrapText(editor, MARKS[mark]);
    }
}
