import { createMarkdownPlatePlugin } from '../../plate-plugin';
import isHotkey from 'is-hotkey';
import { isMarkActive, toggleMark } from './transforms';
import { Editor, Text, Transforms } from 'slate';
import { unicodePunctuation, unicodeWhitespace } from 'micromark-util-character';
import useFormatMessage from '../../../../lib/use-format-message';
import useEditor from '../../use-editor';
import { useCallback } from 'react';
import { BsCode, BsTypeBold, BsTypeItalic, BsTypeStrikethrough } from 'react-icons/bs';

const MARKS = [
    {
        format: 'strong',
        hotKey: 'Ctrl+B',
        icon: <BsTypeBold />
    },
    {
        format: 'emphasis',
        hotKey: 'Ctrl+I',
        icon: <BsTypeItalic />
    },
    {
        format: 'delete',
        hotKey: 'Ctrl+Shift+X',
        icon: <BsTypeStrikethrough />
    },
    {
        format: 'code',
        hotKey: 'Ctrl+E',
        icon: <BsCode />
    }
] as const;

export const MarksPlugin = createMarkdownPlatePlugin({
    tools() {
        return {
            30: function ({ Component }) {
                const t = useFormatMessage();
                const editor = useEditor();

                return <>
                    {MARKS.map(({ icon, format, hotKey }) => {
                        const handler = useCallback(() => {
                            toggleMark(editor, format);
                        }, [editor, format]);

                        const active = isMarkActive(editor, format);

                        return <Component
                            key={format}
                            active={active}
                            hotKey={hotKey}
                            title={t(`editor.tool.${format}`)}
                            handler={handler}
                        >
                            {icon}
                        </Component>;
                    })}
                </>;
            }
        };
    },
    hoverTools() {
        return {
            10: function ({ Component }) {
                const t = useFormatMessage();
                const editor = useEditor();

                return <>
                    {MARKS.map(({ icon, format }) => {
                        const handler = useCallback(() => {
                            toggleMark(editor, format);
                        }, [editor, format]);

                        const active = isMarkActive(editor, format);

                        return <Component
                            key={format}
                            active={active}
                            tooltip={t(`editor.tool.${format}`)}
                            handler={handler}
                            icon={icon}
                        />;
                    })}
                </>;
            },
        };
    },
    renderLeaf(editor) {
        if (editor.isDocument) {
            return ({ children, leaf }) => {
                if (leaf.code) {
                    children = <code>{children}</code>;
                }
                if (leaf.strong) {
                    children = <strong>{children}</strong>;
                }
                if (leaf.emphasis) {
                    children = <em>{children}</em>;
                }
                if (leaf.delete) {
                    children = <s>{children}</s>;
                }

                return children;
            };
        }
    },
    onKeyDown(editor) {
        return e => {
            if (isHotkey('mod+b', e)) {
                e.preventDefault();
                toggleMark(editor, 'strong');
            }
            if (isHotkey('mod+i', e)) {
                e.preventDefault();
                toggleMark(editor, 'emphasis');
            }
            if (isHotkey('mod+shift+x', e)) {
                e.preventDefault();
                toggleMark(editor, 'delete');
            }
            if (isHotkey('mod+e', e)) {
                e.preventDefault();
                toggleMark(editor, 'code');
            }
        };
    },
    withOverrides(editor) {

        const { normalizeNode } = editor;

        if (editor.isDocument) {
            editor.normalizeNode = (entry) => {
                const [node, path] = entry;

                if (Text.isText(node)) {
                    if (node.strong || node.emphasis || node.delete) {
                        Editor.withoutNormalizing(editor, () => {
                            const props: string[] = [];
                            node.strong && props.push('strong');
                            node.emphasis && props.push('emphasis');
                            node.delete && props.push('delete');

                            if (node.text) {
                                const start = node.text.charCodeAt(0);
                                if (unicodeWhitespace(start) || unicodePunctuation(start)) {
                                    Transforms.unsetNodes(editor, props, {
                                        match: Text.isText,
                                        at: {
                                            anchor: {
                                                path,
                                                offset: 0
                                            },
                                            focus: {
                                                path,
                                                offset: 1
                                            }
                                        },
                                        split: true,
                                    });
                                }

                                if (node.text.length > 1) {
                                    const end = node.text.charCodeAt(node.text.length - 1);
                                    if (unicodeWhitespace(end) || unicodePunctuation(end)) {
                                        Transforms.unsetNodes(editor, props, {
                                            match: Text.isText,
                                            at: {
                                                anchor: {
                                                    path,
                                                    offset: node.text.length - 1
                                                },
                                                focus: {
                                                    path,
                                                    offset: node.text.length
                                                }
                                            },
                                            split: true,
                                        });
                                    }
                                }
                            }
                        });
                    }
                }
                normalizeNode(entry);
            };

        }

        return editor;
    }

});
