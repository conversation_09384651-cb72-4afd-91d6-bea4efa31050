import { createMarkdownPlatePlugin } from '../../plate-plugin';
import { styled } from '@topwrite/common';
import isHotkey from 'is-hotkey';
import { isBlockquoteActive, toggleBlockquote } from './transforms';
import useFormatMessage from '../../../../lib/use-format-message';
import { useCallback, useMemo } from 'react';
import {
    BsBlockquoteLeft,
    BsCheckCircle,
    BsCircle,
    BsExclamationCircle,
    BsExclamationTriangle,
    BsInfoCircle
} from 'react-icons/bs';
import useEditor from '../../use-editor';
import { elements, findPath, getBlockAbove, isCollapsed, isStart } from '../../queries';
import { setNodes } from '../../transforms';
import { Node } from 'slate';

export const BlockquotePlugin = createMarkdownPlatePlugin({
    tools() {
        return {
            70: ({ Component }) => {
                const t = useFormatMessage();
                const editor = useEditor();

                const active = isBlockquoteActive(editor);

                const handler = useCallback(() => {
                    toggleBlockquote(editor);
                }, [editor]);

                return <Component
                    active={active}
                    title={t('editor.tool.blockquote')}
                    hotKey='Ctrl+Q'
                    handler={handler}
                >
                    <BsBlockquoteLeft />
                </Component>;
            }
        };
    },
    renderElement() {
        return {
            blockquote({ element, children, attributes, ElementMenu }) {
                const { variant } = element;
                const editor = useEditor();

                const items = useMemo(() => ([
                    {
                        eventKey: 'default',
                        icon: <BsCircle />,
                        text: 'Default'
                    },
                    {
                        eventKey: 'info',
                        icon: <BsInfoCircle />,
                        text: 'Info'
                    },
                    {
                        eventKey: 'warning',
                        icon: <BsExclamationCircle />,
                        text: 'Warning'
                    },
                    {
                        eventKey: 'danger',
                        icon: <BsExclamationTriangle />,
                        text: 'Danger'
                    },
                    {
                        eventKey: 'success',
                        icon: <BsCheckCircle />,
                        text: 'Success'
                    }
                ]), []);

                return <ElementMenu
                    element={element}
                    {...attributes}
                    items={items}
                    activeKey={variant || 'default'}
                    onItemSelect={useCallback((eventKey: string) => {
                        const variant = eventKey === 'default' ? undefined : eventKey;
                        const location = findPath(editor, element);
                        setNodes(editor, { variant }, { at: location });
                    }, [editor, element])}
                >
                    <Blockquote className={variant}>{children}</Blockquote>
                </ElementMenu>;
            }
        };
    },
    onKeyDown(editor) {
        return e => {
            if (isHotkey('mod+q', e)) {
                e.preventDefault();
                toggleBlockquote(editor);
            }
            if (editor.isDocument) {
                if (isHotkey('shift?+backspace', e)) {
                    if (isCollapsed(editor)) {
                        const above = getBlockAbove(editor, { match: { type: 'blockquote' } });
                        if (above) {
                            const [, path] = above;
                            if (isStart(editor, path)) {
                                e.preventDefault();
                                toggleBlockquote(editor, true, 'lowest');
                            }
                        }
                    }
                }

                if (isHotkey('shift?+tab', e) && isBlockquoteActive(editor)) {
                    e.preventDefault();
                    if (e.shiftKey) {
                        toggleBlockquote(editor, true, 'lowest');
                    } else {
                        toggleBlockquote(editor, false, 'lowest');
                    }
                }
            } else {
                if (isHotkey('enter', e) && isCollapsed(editor)) {
                    const [[node,]] = elements(editor);
                    const text = Node.string(node);

                    const inBlockquote = text.match(/^(>(\[\w+])?\s)/);
                    if (inBlockquote) {
                        e.preventDefault();
                        editor.insertBreak();
                        editor.insertText(inBlockquote[0]);
                    }
                }
            }
        };
    }
});

const Blockquote = styled.div`
    padding: 5px 5px 5px 15px;
    color: #858585;
    border-left: 4px solid #e5e5e5;
    margin: 1em 0;

    &.info {
        border-left-color: #5bc0de;
        color: #5bc0de;
        background-color: #f4f8fa;
    }

    &.warning {
        background-color: #fcf8f2;
        border-color: #f0ad4e;
        color: #f0ad4e;
    }

    &.danger {
        color: #d9534f;
        background-color: #fdf7f7;
        border-color: #d9534f;
    }

    &.success {
        background-color: #f3f8f3;
        border-color: #50af51;
        color: #50af51;
    }

    [data-theme=dark] & {
        border-left-color: #858585;

        &.info {
            border-left-color: rgb(27, 110, 134);
            color: rgb(98, 195, 223);
            background-color: rgb(19, 34, 42);
        }

        &.warning {
            background-color: rgb(49, 34, 11);
            border-color: rgb(147, 91, 12);
            color: rgb(241, 176, 84);
        }

        &.danger {
            color: rgb(219, 94, 90);
            background-color: rgb(46, 11, 11);
            border-color: rgb(137, 33, 30);
        }

        &.success {
            background-color: rgb(27, 39, 23);
            border-color: rgb(56, 122, 57);
            color: rgb(98, 183, 98);
        }
    }
`;
