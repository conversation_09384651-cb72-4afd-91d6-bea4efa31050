import { Editor, MaximizeMode } from 'slate';
import { elements, someNode } from '../../queries';
import { insertText, unwrapNodes, wrapNodes } from '../../transforms';

export const isBlockquoteActive = (editor: Editor) => {
    return editor.isDocument && someNode(editor, { match: { type: 'blockquote' } });
};

export const toggleBlockquote = (editor: Editor, isActive?: boolean, mode: MaximizeMode = 'highest') => {
    if (editor.isDocument) {
        if (isActive === undefined) isActive = isBlockquoteActive(editor);
        if (isActive) {
            unwrapNodes(editor, { match: { type: 'blockquote' } });
        } else {
            wrapNodes(editor, {
                type: 'blockquote',
                children: []
            }, {
                mode
            });
        }
    } else {
        for (const [, path] of elements(editor)) {
            const at = { path: path.concat(0), offset: 0 };
            insertText(editor, '> ', { at });
        }
    }
};
