import { createMarkdownPlatePlugin } from '../../plate-plugin';
import { css, styled } from '@topwrite/common';
import yaml, { FAILSAFE_SCHEMA } from 'js-yaml';
import { useCallback, useMemo, useState } from 'react';
import { JSONSchema7 } from 'json-schema';
import Form, { FormProps } from '../../../form';
import { insertNodes, setNodes } from '../../transforms';
import { Editor, Element, Node } from 'slate';
import useFormatMessage from '../../../../lib/use-format-message';
import useEditor from '../../use-editor';

export const YamlPlugin = createMarkdownPlatePlugin({
    voidTypes: 'yaml',
    renderElement() {
        return {
            yaml({ children, attributes, element }) {
                const editor = useEditor();
                const [open, setOpen] = useState(false);
                const t = useFormatMessage();

                const schema: JSONSchema7 = {
                    properties: {
                        title: {
                            type: 'string',
                            title: t('editor.frontmatter.title')
                        },
                        keywords: {
                            type: 'string',
                            title: t('editor.frontmatter.keywords')
                        },
                        description: {
                            type: 'string',
                            title: t('editor.frontmatter.description')
                        }
                    },
                    additionalProperties: {
                        type: 'string'
                    },
                };

                const formData = useMemo(() => {
                    return yaml.load(element.value, { schema: FAILSAFE_SCHEMA }) || {};
                }, [element.value]);

                const handleChange: FormProps['onChange'] = useCallback(({ formData }) => {
                    let value = yaml.dump(formData, {
                        skipInvalid: true,
                        noRefs: true
                    }).trim();

                    if (value === '{}') {
                        value = '';
                    }

                    setNodes(editor, { value: value }, { match: { type: 'yaml' } });
                }, [editor]);

                return <Container {...attributes} contentEditable={false}>
                    {children}
                    <FrontMatter open={open}>
                        <Toggle onClick={() => setOpen(!open)}>{open ? 'close' : 'edit metadata'}</Toggle>
                        {open && <Form formData={formData} schema={schema} onChange={handleChange} />}
                    </FrontMatter>
                </Container>;
            }
        };
    },
    withOverrides(editor) {
        if (editor.isDocument) {
            const { normalizeNode } = editor;
            editor.normalizeNode = ([node, path]) => {
                if (Editor.isEditor(node)) {
                    const first = node.children[0];
                    if (!Element.isElement(first) || first.type !== 'yaml') {
                        insertNodes(editor, {
                            type: 'yaml',
                            value: '',
                            children: [{ text: '' }]
                        }, { at: [0] });

                        node = Node.get(editor, path);
                    }
                }
                normalizeNode([node, path]);
            };
        }
        return editor;
    }
});

const Toggle = styled.div`
    position: absolute;
    padding: 2px 7px;
    top: 0;
    right: 0;
    z-index: 1;
    background: var(--ttw-foreground);
    cursor: pointer;
    border-bottom: 1px solid var(--ttw-border-color);
    border-left: 1px solid var(--ttw-border-color);
    color: #737373;
    border-bottom-left-radius: 3px;
    user-select: none;

    &:hover {
        color: var(--bs-primary)
    }
`;


const FrontMatter = styled.div<{ open: boolean }>`
  position: relative;
  margin-left: -30px;
  margin-right: -30px;
  margin-top: -30px;

  ${props => props.open && css`
    background: var(--ttw-foreground);
    border-bottom: 1px solid var(--ttw-border-color);
    padding: 15px 30px;

    ${Toggle} {
        top: 100%;
    }
`}
`;


const Container = styled.div`
  padding-bottom: 30px;
  user-select: none;

  * {
    font-size: 1rem !important;
  }
`;
