import React, { forwardRef, useEffect, useImperative<PERSON>andle, useMemo } from 'react';
import { EditableProps, Slate, SlateProps, withReact } from 'slate-react';
import { PlateContext, PlateContextProps } from './use-plate';
import pipe from './utils/pipe';
import { createEditor, Editor } from 'slate';
import { withHistory } from 'slate-history';
import pipeDecorate from './utils/pipe-decorate';
import pipeRenderElement from './utils/pipe-render-element';
import pipeRenderLeaf from './utils/pipe-render-leaf';
import { DOMHandlerNames } from './utils/dom-handlers';
import pipeHandler from './utils/pipe-handler';
import Editable from './editable';
import defaultPlugins from './plugins';
import { flatMapByKey } from './utils/flat-map-by-key';
import PlatePlugin from './plate-plugin';
import withInlineVoid from './utils/with-inline-void';
import pipeTools from './utils/pipe-tools';
import { useActions } from '@topwrite/common';
import pipeHoverTools from './utils/pipe-hover-tools';

interface PlateProps extends Omit<SlateProps, 'editor'> {
    renderEditable?: (editable: React.ReactNode) => React.ReactNode;
    readOnly?: boolean;
    placeholder?: string;
    plugins?: PlatePlugin[];
    withEditor?: (editor: Editor) => Editor;
}

const Plate = forwardRef<Editor, PlateProps>(({
    children,
    initialValue,
    onChange,
    onSelectionChange,
    plugins = [],
    renderEditable,
    placeholder,
    withEditor
}, ref) => {

    const { setInstance } = useActions('editor');

    const [editor, plate, key] = useMemo(() => {
        let editor = pipe(createEditor(), withReact, withHistory);

        if (withEditor) {
            editor = withEditor(editor);
        }

        plugins = [
            ...defaultPlugins,
            ...plugins
        ].filter(plugin => (!plugin.available || plugin.available(editor)))
         .sort(({ priority: priorityA = 0 }, { priority: priorityB = 0 }) => priorityA - priorityB);

        const editable: EditableProps = {
            decorate: pipeDecorate(editor, plugins),
            renderElement: pipeRenderElement(editor, plugins),
            renderLeaf: pipeRenderLeaf(editor, plugins),
        };

        DOMHandlerNames.forEach((handlerName) => {
            editable[handlerName] = pipeHandler(editor, plugins, handlerName);
        });

        const plate: PlateContextProps = {
            editable: editable as PlateContextProps['editable'],
            tools: pipeTools(editor, plugins),
            hoverTools: pipeHoverTools(editor, plugins),
        };

        plugins.push({
            withOverrides: withInlineVoid(plugins),
        });

        const withOverrides = flatMapByKey(plugins, 'withOverrides');

        editor = pipe(editor, ...withOverrides);

        //设置软回车
        editor.insertSoftBreak = () => {
            editor.insertText('\n');
        };

        return [editor, plate, (new Date()).toISOString()];
    }, [plugins, withEditor]);

    useEffect(() => {
        setInstance(editor);
        return () => {
            setInstance(null);
        };
    }, [editor]);

    useImperativeHandle(ref, () => editor, [editor]);

    const editable = useMemo(() => {
        const editable = <Editable placeholder={placeholder} />;
        return renderEditable ? renderEditable(editable) : editable;
    }, [renderEditable]);

    return <PlateContext.Provider key={key} value={plate}>
        <Slate editor={editor} onChange={onChange} onSelectionChange={onSelectionChange} initialValue={initialValue}>
            {children}
            {editable}
        </Slate>
    </PlateContext.Provider>;
});

export default Plate;
