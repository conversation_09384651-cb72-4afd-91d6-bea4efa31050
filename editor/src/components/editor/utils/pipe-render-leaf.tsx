import PlatePlugin from '../plate-plugin';
import { DefaultLeaf, RenderLeafProps } from 'slate-react';
import { Editor } from 'slate';
import { createElement } from 'react';

export default function pipeRenderLeaf(editor: Editor, plugins: PlatePlugin[]) {

    const renderLeaves = plugins.flatMap(
        (plugin) => plugin.renderLeaf?.(editor) ?? []
    );

    return (props: RenderLeafProps) => {
        let { children } = props;

        children = renderLeaves.reduce((children, renderLeaf) => {
            return createElement(renderLeaf, props, children);
        }, children);

        return <DefaultLeaf {...props} >
            {children}
        </DefaultLeaf>;
    };
}
