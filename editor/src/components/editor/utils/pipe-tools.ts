import { Editor } from 'slate';
import PlatePlugin from '../plate-plugin';
import { ComponentType, createElement } from 'react';
import ToolItem from '../tool-bar/item';

type Groups = {
    [group: number]: {
        [index: number]: ComponentType;
        '#': ComponentType[];
    };
};

function formatGroup(groups: Groups) {
    return Object.entries(groups).map(([, items]) => {
        return Object.entries(items).flatMap(([path, item]) => {
            if (path === '#') {
                return item;
            }
            return [item] as ComponentType[];
        });
    });
}

export default function pipeTools(editor: Editor, plugins: PlatePlugin[]): [ComponentType[][], ComponentType[][]] {
    const tools = plugins.flatMap(
        (plugin) => {
            if (plugin.tools) {
                return Object.entries(plugin.tools(editor));
            }
            return [];
        }
    );

    const leftGroups = {};
    const rightGroups = {};

    for (const [position, tool] of tools) {
        const [group, index] = position.split(':');
        if (group !== undefined) {
            const groups = Number(group) > 0 ? leftGroups : rightGroups;

            if (!groups[group]) {
                groups[group] = {};
            }

            if (!index) {
                if (!groups[group]['#']) {
                    groups[group]['#'] = [];
                }
                groups[group]['#'].push(function () {
                    return createElement(tool, { Component: ToolItem });
                });
            } else {
                groups[group][index] = function () {
                    return createElement(tool, { Component: ToolItem });
                };
            }
        }
    }

    return [formatGroup(leftGroups), formatGroup(rightGroups)];
}


