import { Editor } from 'slate';
import PlatePlugin from '../plate-plugin';
import { ComponentType, createElement } from 'react';
import ToolItem from '../hover-bar/item';

type Groups = {
    [group: number]: {
        [index: number]: ComponentType;
        '#': ComponentType[];
    };
};

function formatGroup(groups: Groups) {
    return Object.entries(groups).map(([, items]) => {
        return Object.entries(items).flatMap(([path, item]) => {
            if (path === '#') {
                return item;
            }
            return [item] as ComponentType[];
        });
    });
}

export default function pipeHoverTools(editor: Editor, plugins: PlatePlugin[]): ComponentType[][] {
    const tools = plugins.flatMap(
        (plugin) => {
            if (plugin.hoverTools) {
                return Object.entries(plugin.hoverTools(editor));
            }
            return [];
        }
    );

    const groups = {};

    for (const [position, tool] of tools) {
        const [group, index] = position.split(':');
        if (group !== undefined) {
            if (!groups[group]) {
                groups[group] = {};
            }

            if (!index) {
                if (!groups[group]['#']) {
                    groups[group]['#'] = [];
                }
                groups[group]['#'].push(function () {
                    return createElement(tool, { Component: ToolItem });
                });
            } else {
                groups[group][index] = function () {
                    return createElement(tool, { Component: ToolItem });
                };
            }
        }
    }

    return formatGroup(groups);
}


