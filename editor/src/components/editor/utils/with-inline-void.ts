import PlatePlugin, { WithOverride } from '../plate-plugin';
import { castArray } from 'lodash';

export default function withInlineVoid(plugins: PlatePlugin[]): WithOverride {
    return editor => {
        const { isInline } = editor;
        const { isVoid } = editor;

        let allInlineTypes: string[] = [];
        let allVoidTypes: string[] = [];

        plugins.forEach((plugin) => {
            if (plugin.inlineTypes) {
                allInlineTypes = allInlineTypes.concat(castArray(plugin.inlineTypes));
            }

            if (plugin.voidTypes) {
                allVoidTypes = allVoidTypes.concat(castArray(plugin.voidTypes));
            }
        });

        editor.isInline = (element) => {
            if (allInlineTypes.includes(element.type)) {
                return true;
            }
            return isInline(element);
        };

        editor.isVoid = (element) => {
            if (element.type == 'textDirective' && allVoidTypes.includes(`text:${element.name}`)) {
                return true;
            }

            if (element.type == 'containerDirective' && allVoidTypes.includes(`container:${element.name}`)) {
                return true;
            }

            if (element.type == 'leafDirective' && allVoidTypes.includes(`leaf:${element.name}`)) {
                return true;
            }

            if (element.type == 'directiveLabel' && allVoidTypes.includes(`label:${element.name}`)) {
                return true;
            }

            if (allVoidTypes.includes(element.type)) {
                return true;
            }
            return isVoid(element);
        };


        return editor;
    };
}
