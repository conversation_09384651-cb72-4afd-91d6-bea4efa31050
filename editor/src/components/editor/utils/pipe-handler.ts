import { Editor } from 'slate';
import { ReactEventHandler, SyntheticEvent } from 'react';
import PlatePlugin from '../plate-plugin';
import { DOMHandlers } from './dom-handlers';

export const isEventHandled = <EventType extends SyntheticEvent<unknown, unknown>>(
    event: EventType,
    handler?: (event: EventType) => void | boolean
) => {
    if (!handler) {
        return false;
    }
    // The custom event handler may return a boolean to specify whether the event
    // shall be treated as being handled or not.
    const shouldTreatEventAsHandled = handler(event);

    if (shouldTreatEventAsHandled != null) {
        return shouldTreatEventAsHandled;
    }

    return event.isPropagationStopped() || event.isDefaultPrevented();
};


export default function pipeHandler(editor: Editor, plugins: PlatePlugin[], handlerName: keyof DOMHandlers): ReactEventHandler {
    let pluginsHandlers: ReactEventHandler[] = [];
    if (plugins) {
        pluginsHandlers = plugins.flatMap((plugin) => plugin[handlerName]?.(editor) ?? []);
    }

    return (event: any) => {
        const eventIsHandled = pluginsHandlers.some((handler) =>
            isEventHandled(event, handler)
        );
        if (eventIsHandled) return true;
    };
}
