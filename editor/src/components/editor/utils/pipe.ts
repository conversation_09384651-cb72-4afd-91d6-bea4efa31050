function pipe<T0>(x0: T0): T0;
function pipe<T0, T1>(x0: T0, f1?: (x: T0) => T1): T1;
function pipe<T0, T1, T2>(
    x0: T0,
    f1?: (x: T0) => T1,
    f2?: (x: T1) => T2
): T2;
function pipe<T0, T1, T2, T3>(
    x0: T0,
    f1?: (x: T0) => T1,
    f2?: (x: T1) => T2,
    f3?: (x: T2) => T3
): T3;
function pipe<T0, T1, T2, T3, T4>(
    x0: T0,
    f1?: (x: T0) => T1,
    f2?: (x: T1) => T2,
    f3?: (x: T2) => T3,
    f4?: (x: T3) => T4
): T4;
function pipe(x: any, ...fns: any[]) {
    return fns.reduce((y: any, fn) => fn(y), x);
}

export default pipe;
