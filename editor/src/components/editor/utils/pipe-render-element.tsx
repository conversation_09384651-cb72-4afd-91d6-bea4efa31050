import PlatePlugin, { ElementProps } from '../plate-plugin';
import { DefaultElement, RenderElementProps } from 'slate-react';
import { ComponentType, createElement, useMemo } from 'react';
import { Editor } from 'slate';
import ElementMenu from '../element-menu';

export default function pipeRenderElement(editor: Editor, plugins: PlatePlugin[]) {

    const components = plugins.flatMap(
        (plugin) => {
            if (plugin.renderElement) {
                const renderElement = plugin.renderElement(editor);
                if (renderElement) {
                    return Object.entries(renderElement);
                }
            }
            return [];
        }
    );

    return (props: RenderElementProps) => {
        const { element } = props;

        const component = useMemo<ComponentType<ElementProps> | undefined>(() => {
            const entry = components.find(([type]) => {
                return type === element.type
                    || (element.type == 'textDirective' && type == `text:${element.name}`)
                    || (element.type == 'containerDirective' && type == `container:${element.name}`)
                    || (element.type == 'leafDirective' && type == `leaf:${element.name}`)
                    || (element.type == 'directiveLabel' && type == `label:${element.name}`);
            });

            if (entry) {
                const [, component] = entry;
                return component as ComponentType<ElementProps>;
            }
        }, [element]);

        const newProps = {
            ...props,
            element,
            ElementMenu,
        };
        if (component) {
            return createElement(component, newProps);
        }

        return createElement(DefaultElement, newProps);
    };
}
