import { Editor, NodeEntry, Range } from 'slate';
import PlatePlugin from '../plate-plugin';

export default function pipeDecorate(editor: Editor, plugins: PlatePlugin[]) {
    const decorates = plugins.flatMap(
        (plugin) => plugin.decorate?.(editor) ?? []
    );

    return (entry: NodeEntry) => {
        let ranges: Range[] = [];

        const addRanges = (newRanges?: Range[]) => {
            if (newRanges?.length) ranges = [...ranges, ...newRanges];
        };

        decorates.forEach((decorate) => {
            addRanges(decorate(entry));
        });

        return ranges;
    };
}
