import { styled, useLocalStorageState } from '@topwrite/common';
import { Allotment } from 'allotment';
import { ReactNode } from 'react';
import { AiOutlineEye, AiOutlineEyeInvisible } from 'react-icons/ai';
import { ScrollSync, ScrollSyncNode } from 'scroll-sync-react';
import { useFile } from '../../file-provider';
import Markdown from '../../markdown';
import { useMode } from '../mode-provider';

const Preview = () => {
    const file = useFile();

    return <PreviewContainer>
        <Markdown>{file.content.toString()}</Markdown>
    </PreviewContainer>;
};

interface RawProps {
    editable: ReactNode;
}

export default function Raw({ editable }: RawProps) {
    const [show, setShow] = useLocalStorageState<boolean | number>('editor.preview', true);

    const { isMarkdown } = useMode();

    if (isMarkdown) {
        return <ScrollSync>
            <Container>
                <Allotment onDragEnd={([first, second]) => {
                    if (show && second) {
                        setShow(Math.floor(second / (first + second) * 100));
                    }
                }}>
                    <Allotment.Pane minSize={300} preferredSize={'50%'}>
                        <ScrollSyncNode scroll={'syncer-only'} selfLockAxis={'X'}>
                            <ScrollContainer>
                                {editable}
                            </ScrollContainer>
                        </ScrollSyncNode>
                    </Allotment.Pane>
                    {show &&
                        <Allotment.Pane preferredSize={typeof show === 'number' ? `${show}%` : '50%'} minSize={300}>
                            <ScrollSyncNode selfLockAxis={'X'}>
                                <ScrollContainer>
                                    <Preview />
                                </ScrollContainer>
                            </ScrollSyncNode>
                        </Allotment.Pane>}
                </Allotment>
                <PreviewToggle className='btn btn-light' onClick={() => setShow(!show)}>
                    {show ? <AiOutlineEyeInvisible /> : <AiOutlineEye />}
                </PreviewToggle>
            </Container>
        </ScrollSync>;
    }

    return <Container>
        <ScrollContainer>
            {editable}
        </ScrollContainer>
    </Container>;
}


const PreviewToggle = styled.button`
    position: absolute;
    right: 15px;
    top: 10px;
    line-height: 1;
    padding: 0.5rem 0.75rem;
`;

const PreviewContainer = styled.div`
    padding: 16px;
    background: var(--ttw-priview-background);
    min-height: 100%;
    font-size: 16px;
`;

const ScrollContainer = styled.div`
    overflow-y: auto;
    width: 100%;
    height: 100%;
`;

const Container = styled.div`
    height: 100%;
    overflow: hidden;
    position: relative;
    background: var(--ttw-editor-background);

    [data-slate-editor] {
        font-size: 16px;
        line-height: 1.8;
        padding: 16px 16px calc(100vh - 113px);
        outline: none;
    }
`;
