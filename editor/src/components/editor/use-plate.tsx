import { ComponentType, createContext, useContext } from 'react';
import { DOMHandlers } from './utils/dom-handlers';
import { EditableProps } from 'slate-react';

export interface PlateContextProps {
    tools: [ComponentType[][], ComponentType[][]];
    hoverTools: ComponentType[][];
    editable: Required<DOMHandlers> & Pick<EditableProps, 'renderElement' | 'renderLeaf' | 'decorate'>;
}

export const PlateContext = createContext<PlateContextProps | undefined>(undefined);

export function usePlate(): PlateContextProps {
    const plate = useContext(PlateContext);
    if (!plate) {
        throw new Error();
    }
    return plate;
}
