import { useCallback, useEffect } from 'react';
import { Editable as SlateEditable, useFocused, useSlateStatic } from 'slate-react';
import { Editor, NodeEntry, Range } from 'slate';
import { usePlate } from './use-plate';
import { useSelector } from '@topwrite/common';

interface Props {
    placeholder?: string;
}

const Editable = ({ placeholder }: Props) => {
    const editor = useSlateStatic();
    const { editable: { decorate, ...props } } = usePlate();
    const { decorates } = useSelector('editor');

    useEffect(() => {
        //TODO 优化第一次格式化
        window.requestAnimationFrame(() => {
            Editor.normalize(editor, { force: true });
        });
    }, []);

    //需要调用下此行，在编辑器重获焦点时能恢复之前的光标位置（比如编辑器内弹窗后关闭）
    useFocused();

    return <SlateEditable
        {...props}
        decorate={useCallback((entry: NodeEntry) => {
            let ranges = decorate?.(entry) ?? [];

            const addRanges = (newRanges?: Range[]) => {
                if (newRanges?.length) ranges = [...ranges, ...newRanges];
            };

            decorates.forEach((decorate) => {
                addRanges(decorate(entry));
            });
            return ranges;
        }, [decorates, decorate])}
        placeholder={placeholder}
        autoFocus
        spellCheck={false}
    />;
};

export default Editable;
