import { css, styled, useAsync } from '@topwrite/common';
import { Modal } from 'react-bootstrap';
import Button from '../button';
import mime from 'mime/lite';
import useFormatMessage from '../../lib/use-format-message';
import useOverlayState from '../../lib/use-overlay-state';
import { BsFiles } from 'react-icons/bs';
import match from 'mime-match';
import Tooltip from '../tooltip';
import FileIcon from '../file-icon';
import fileSize from 'filesize';
import dayjs from 'dayjs';
import Empty from '../empty';
import Img from '../html/img';
import path from 'path';
import useEditor from './use-editor';

interface File {
    path: string;
    size: number;
    ctime: number;
}


export interface BrowseButtonProps {
    onSelect: (file: { path: string, name: string, size: number }) => void;
    accept?: string;
    view?: 'preview' | 'list';
}

export default function BrowseButton({ onSelect, accept = '*', view = 'list' }: BrowseButtonProps) {

    const { browseFile } = useEditor();
    const t = useFormatMessage();

    const { show, hide, state } = useOverlayState();

    const { result: files = [] } = useAsync<File[]>(async () => {
        const files = await browseFile();

        return files.filter(file => {
            try {
                return match(mime.getType(file.path) || 'application/octet-stream', accept);
            } catch (e) {
                return false;
            }
        });
    }, [browseFile]);

    const selectFile = (item: File) => {
        return () => {
            onSelect({
                ...item,
                name: path.basename(item.path)
            });
            hide();
        };
    };

    return <>
        <Button onClick={show} variant='outline-primary'>
            <BsFiles className='me-2' />{t('editor.tool.asset.browse')}
        </Button>
        <Modal {...state} scrollable size={'lg'}>
            <Modal.Header>{t('editor.tool.asset.browse')}</Modal.Header>
            <Modal.Body>
                {files.length > 0 ?
                    <ListContainer preview={view === 'preview'}>
                        {files.map((file) => {

                            const Item = view === 'list' ? ListItem : PreviewItem;

                            return <Item file={file} onClick={selectFile(file)} key={file.path} />;
                        })}
                    </ListContainer> :
                    <EmptyContainer><Empty /></EmptyContainer>
                }
            </Modal.Body>
            <Modal.Footer>
                <Button onClick={hide} variant='secondary'>{t('modal.cancel')}</Button>
            </Modal.Footer>
        </Modal>
    </>;
}

const EmptyContainer = styled.div`
  padding: 100px;
`;

interface ItemProps {
    file: File;
    onClick: () => void;
}

const ListItem = ({ file, onClick }: ItemProps) => {
    return <ListItemContainer onClick={onClick}>
        <span>
            <FileIcon filename={file.path} />
            {path.basename(file.path)}
        </span>
        <span>{fileSize(file.size)}</span>
        <span className='text-end'>{dayjs(file.ctime * 1000).format('YYYY-MM-DD HH:mm')}</span>
    </ListItemContainer>;
};

const PreviewItem = ({ file, onClick }: ItemProps) => {
    return <Tooltip
        tooltip={`${path.basename(file.path)}\n${fileSize(file.size)}, ${dayjs(file.ctime * 1000)
        .format('YYYY-MM-DD HH:mm')}`}>
        <PreviewItemContainer onClick={onClick}>
            <ImageContainer>
                <Img src={file.path} />
            </ImageContainer>
            <p>{path.basename(file.path)}</p>
        </PreviewItemContainer>
    </Tooltip>;
};

const ImageContainer = styled.div`
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    max-width: 100%;
    max-height: 100%;
  }
`;

const ListItemContainer = styled.li`
  display: table-row;
  cursor: pointer;

  & > span {
    display: table-cell;
    vertical-align: middle;
    line-height: 24px;
    padding: 5px;

    svg {
      width: 16px;
      height: 16px;
      margin-right: 5px;
    }
  }

  &:hover {
    background: #f1f2f2;
  }
`;

const PreviewItemContainer = styled.li`
  width: calc(20% - 8px);
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  cursor: pointer;
  background: #f1f2f2;

  &:hover {
    outline: 2px solid var(--bs-primary);
  }

  p {
    margin-top: 5px;
    margin-bottom: 0;
    padding: 0 5px 5px;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
`;

const ListContainer = styled.ul<{ preview: boolean }>`
  list-style: none;
  margin: 0;
  padding: 0;
  display: table;
  width: 100%;

  ${props => props.preview && css`
    flex-wrap: wrap;
    gap: 10px;
    display: flex;
  `}
`;
