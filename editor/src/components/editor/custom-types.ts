import { BaseEditor, BaseText, Descendant } from 'slate';
import { Editable, ReactEditor, Slate } from 'slate-react';
import { HistoryEditor } from 'slate-history';
import { AlignType, Alternative, Association, Reference, Resource } from 'mdast';
import { ComponentProps } from 'react';
import { Position } from 'unist';
import Serializer from './serializer';
import { MessageFormater } from '../../lib/use-format-message';

declare module 'slate' {

    export interface ModeEditor {
        filename: string;
        mime: string;
        isDocument: boolean;
        isMarkdown: boolean;
        uploadFile: (file: File, options?: { onProgress?: (event: ProgressEvent) => void; }) => Promise<{
            path: string;
            name: string;
            size: number;
        }>;
        browseFile: () => Promise<{ path: string, size: number, ctime: number; }[]>;
        supportLargeFile: () => boolean;
        supportBrowseFile: () => boolean;
        setLoading: (loading: boolean | string) => void;
        gotoLine: (line: number) => void;
        serializer: Serializer;
        formatMessage: MessageFormater;
    }

    interface BaseCustomElement {
        type: string;
        position?: Position;
    }

    export interface CodeLineElement extends BaseCustomElement {
        type: 'codeLine',
        children: [BaseText];
    }

    export interface CodeElement extends BaseCustomElement {
        type: 'code';
        lang?: string | null | undefined;
        meta?: string | null | undefined;
        children: CodeLineElement[];
    }

    export interface BlockquoteElement extends BaseCustomElement {
        type: 'blockquote';
        variant?: string;
        children: Descendant[];
    }

    export interface ListElement extends BaseCustomElement {
        type: 'list';
        task?: boolean;
        ordered?: boolean | null | undefined;
        start?: number | null | undefined;
        spread?: boolean | null | undefined;
        children: Descendant[];
    }

    export interface ListItemElement extends BaseCustomElement {
        type: 'listItem';
        checked?: boolean | null | undefined;
        spread?: boolean | null | undefined;
        children: Descendant[];
    }

    export interface ThematicBreakElement extends BaseCustomElement {
        type: 'thematicBreak';
        children: BaseText[];
    }

    export interface ImageElement extends BaseCustomElement, Resource, Alternative {
        type: 'image';
        title?: string | undefined;
        width?: string | number;
        height?: string | number;
        link?: string;
        children: BaseText[];
    }

    export interface LinkElement extends BaseCustomElement, Resource {
        type: 'link';
        value: string;
        children: BaseText[];
    }

    export type ParagraphDepth = 1 | 2 | 3 | 4 | 5 | 6;

    export interface ParagraphElement extends BaseCustomElement {
        type: 'paragraph';
        children: Descendant[];
        align?: AlignType;
        depth?: ParagraphDepth;
    }

    export interface TableElement extends BaseCustomElement {
        type: 'table';
        align?: AlignType[] | null | undefined;
        children: TableRowElement[];
    }

    export interface TableRowElement extends BaseCustomElement {
        type: 'tableRow';
        children: TableCellElement[];
    }

    export interface TableCellElement extends BaseCustomElement {
        type: 'tableCell';
        children: CustomText[];
    }

    export interface HtmlElement extends BaseCustomElement {
        type: 'html';
        value: string;
        children: BaseText[];
    }

    export interface YamlElement extends BaseCustomElement {
        type: 'yaml';
        value: string;
        children: BaseText[];
    }

    export interface LinkReferenceElement extends BaseCustomElement, Reference {
        type: 'linkReference';
        children: Descendant[];
    }

    export interface DefinitionElement extends BaseCustomElement, Association, Resource {
        type: 'definition';
        children: BaseText[];
    }

    export interface FootnoteDefinitionElement extends BaseCustomElement, Association {
        type: 'footnoteDefinition';
        children: Descendant[];
    }

    export interface ImageReferenceElement extends BaseCustomElement, Reference, Alternative {
        type: 'imageReference';
        children: BaseText[];
    }

    export interface FootnoteElement extends BaseCustomElement {
        type: 'footnote';
        children: Descendant[];
    }

    export interface FootnoteReferenceElement extends BaseCustomElement, Association {
        type: 'footnoteReference';
        children: BaseText[];
    }

    export interface XBlockElement extends BaseCustomElement {
        type: 'xBlock';
        name: string;
        parameter?: string;
        value: string;
        children: BaseText[];
    }

    export interface XInlineElement extends BaseCustomElement {
        type: 'xInline';
        name: string;
        value: string;
        children: BaseText[];
    }

    export interface TextDirectiveElement extends BaseCustomElement {
        type: 'textDirective';
        name: string;
        attributes: Record<string, string | null | undefined>;
        children: Descendant[];
    }

    export interface LeafDirectiveElement extends BaseCustomElement {
        type: 'leafDirective';
        name: string;
        attributes: Record<string, string | null | undefined>;
        children: Descendant[];
    }

    export interface ContainerDirectiveElement extends BaseCustomElement {
        type: 'containerDirective';
        name: string;
        attributes: Record<string, string | null | undefined>;
        children: Descendant[];
    }

    export interface DirectiveLabelElement extends BaseCustomElement {
        type: 'directiveLabel';
        name: string;
        children: Descendant[];
    }

    export interface LineElement extends BaseCustomElement {
        type: 'line';
        children: BaseText[];
    }

    type CustomElement =
        | BlockquoteElement
        | ListElement
        | ListItemElement
        | ImageElement
        | LinkElement
        | ParagraphElement
        | TableElement
        | TableRowElement
        | TableCellElement
        | CodeElement
        | CodeLineElement
        | ThematicBreakElement
        | HtmlElement
        | YamlElement
        | DefinitionElement
        | FootnoteElement
        | FootnoteReferenceElement
        | FootnoteDefinitionElement
        | LinkReferenceElement
        | ImageReferenceElement
        | XBlockElement
        | XInlineElement
        | TextDirectiveElement
        | LeafDirectiveElement
        | ContainerDirectiveElement
        | DirectiveLabelElement
        | LineElement;

    export type Decoration = {
        emphasis?: boolean;
        strong?: boolean;
        delete?: boolean;
        code?: boolean;
    };

    export type DecorationType = keyof Decoration;

    export type CustomText = BaseText & Decoration & {
        position?: Position;
        [index: string]: any;
    };

    type Mark = keyof Decoration;

    export type CustomEditor = BaseEditor & ReactEditor & HistoryEditor & ModeEditor;

    interface CustomTypes {
        Editor: CustomEditor;
        Element: CustomElement;
        Text: CustomText;
    }
}

declare module 'slate-react' {
    type SlateProps = ComponentProps<typeof Slate>;
    type EditableProps = ComponentProps<typeof Editable>;
}
