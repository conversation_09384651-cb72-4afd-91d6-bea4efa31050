import { BsInbox } from 'react-icons/bs';
import Dimmer from './dimmer';
import useFormatMessage from '../lib/use-format-message';
import { ReactNode } from 'react';
import { styled } from '@topwrite/common';

interface EmptyProps {
    message?: string;
    children?: ReactNode;
    icon?: ReactNode;
    className?: string;
}

export default function Empty({ message, icon, className, children }: EmptyProps) {
    const t = useFormatMessage();
    return <Container className={className}>
        <Icon>
            {icon || <BsInbox />}
        </Icon>
        <p className='mt-2'>{message || t('data.empty')}</p>
        <div className='mt-2'>
            {children}
        </div>
    </Container>;
}

const Container = styled(Dimmer)`
    color: var(--ttw-secondary-color);
`;

const Icon = styled.div`
    svg {
        width: 40px;
        height: 40px;
    }

    margin-bottom: 1em;
`;
