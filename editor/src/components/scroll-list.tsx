import { styled } from '@topwrite/common';
import {
    ForwardedRef,
    forwardRef,
    ReactNode,
    useCallback,
    useEffect,
    useImperativeHandle,
    useRef,
    useState
} from 'react';
import { Spinner } from 'react-bootstrap';
import InfiniteScroll from 'react-infinite-scroll-component';
import useImmer, { Updater } from '../lib/use-immer';
import Empty from './empty';
import Loader from './loader';

interface ScrollListData<T> {
    data: T[];
    hasMore: boolean;
    page: number;
}

interface ScrollListProps<R> {
    className?: string;
    fetchData: (result: ScrollListData<R>) => Promise<ScrollListData<R>>;
    renderList?: (props: { data: R[], setData: Updater<R[]> }) => ReactNode;
    renderItem?: (item: R, index: number) => ReactNode;
    deps?: any[];
}

interface ScrollList {
    refresh: Function;
}

type ScrollListType = <T>(
    props: ScrollListProps<T> & { ref?: ForwardedRef<ScrollList> }
) => ReturnType<typeof ScrollListInner>

const ScrollListInner = function <R>({
    className,
    fetchData,
    renderItem,
    renderList,
    deps = []
}: ScrollListProps<R>, ref: ForwardedRef<ScrollList>) {

    const [loading, setLoading] = useState(false);
    const [state, setState] = useImmer<ScrollListData<R>>({
        data: [],
        hasMore: true,
        page: 0
    });

    const loadData = useCallback(async (refresh: boolean = false) => {
        if (!loading) {
            setLoading(true);

            try {
                const result = await fetchData({
                    ...state,
                    page: refresh ? 0 : state.page
                });

                setState(draft => {
                    return {
                        ...result,
                        data: refresh ? result.data : [
                            ...draft.data,
                            ...result.data
                        ]
                    };
                });
            } finally {
                setLoading(false);
            }
        }
    }, [fetchData, state, loading]);

    useEffect(() => {
        loadData(true);
    }, deps);

    useImperativeHandle(ref, () => ({
        refresh: () => {
            loadData(true);
        }
    }));

    const scrollRef = useRef<HTMLDivElement>(null);

    const setData = useCallback((callback) => {
        setState(draft => {
            const result = callback(draft.data);
            if (result) {
                draft.data = result;
            }
        });
    }, [setState]);

    useEffect(() => {
        //组件的bug, 没有滚动条时,但还有数据需要手动触发一次next
        if (scrollRef.current && state && state.hasMore) {
            const element = scrollRef.current.getElementsByClassName('infinite-scroll-component').item(0);

            if (element && element.scrollHeight == element.clientHeight) {
                loadData();
            }
        }
    }, [state]);

    if (state.page === 0) {
        return <Loader />;
    }

    if (state.data.length === 0) {
        return <Empty />;
    }

    return <ScrollContainer className={className} ref={scrollRef}>
        <InfiniteScroll
            height={'100%'}
            next={loadData}
            hasMore={state.hasMore}
            loader={<div className='text-center my-2'>
                <Spinner animation='border' variant='success' />
            </div>}
            endMessage={
                <EmptyText className='text-center my-2'>
                    Yay! You have seen it all
                </EmptyText>
            }
            dataLength={state.data.length}
        >
            {renderList ?
                renderList({ data: state.data, setData }) :
                state.data.map((item, index) => renderItem?.(item, index))}
        </InfiniteScroll>
    </ScrollContainer>;
};

const ScrollList: ScrollListType = forwardRef(ScrollListInner) as ScrollListType;

export default ScrollList;

const EmptyText = styled.p`
    color: var(--ttw-gray-color);
`;

const ScrollContainer = styled.div`
    height: 100%;

    .infinite-scroll-component__outerdiv {
        height: 100%;
    }
`;
