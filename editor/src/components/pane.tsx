import { styled } from '@topwrite/common';
import { PropsWithChildren, ReactNode } from 'react';

const Common = styled.div`
    height: 35px;
    padding: 4px 4px 4px 10px;
    line-height: 26px;
    background: var(--ttw-foreground);
    display: flex;
    flex-shrink: 0;
`;

const PaneHeaderContainer = styled(Common)`
    border-bottom: 1px solid var(--ttw-border-color);

    .btn {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        padding: 0 6px;

        --bs-btn-bg: transparent;
        --bs-btn-border-color: transparent;
        --bs-btn-disabled-bg: transparent;
        --bs-btn-disabled-border-color: transparent;
    }

    .dropdown {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        .btn {
            flex: 1;
        }
    }

    .dropdown-menu {
        --bs-dropdown-item-padding-y: 0.2em !important;
    }
`;

const PaneHeaderText = styled.span`
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-grow: 1;
    display: flex;
    align-items: center;
    font-weight: 600;

    .bi {
        margin-right: .25em;
    }
`;

const PaneFooter = styled(Common)`
    border-top: 1px solid var(--ttw-border-color);
`;

interface PaneHeaderProps {
    action?: ReactNode;
}

const PaneHeader = ({ action, children, ...props }: PropsWithChildren<PaneHeaderProps>) => {
    return <PaneHeaderContainer {...props}>
        <PaneHeaderText>{children}</PaneHeaderText>
        {action}
    </PaneHeaderContainer>;
};

const PaneBody = styled.div`
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
`;

export {
    PaneHeader,
    PaneBody,
    PaneFooter
};
