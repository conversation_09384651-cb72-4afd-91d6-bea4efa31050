import { Change, DiffFile } from 'repo';
import { styled } from '@topwrite/common';
import FileIcon from './file-icon';
import DiffViewer from './diff-viewer';

interface Props {
    files: DiffFile[];
    type?: 'unified' | 'split';
    className?: string;
    loadChanges: (filename: string) => Promise<Change[]>;
}

export default function DiffFiles({ files, className, type, loadChanges }: Props) {

    return <Container className={className}>
        {files.map(file => {
            const filename = file.new_name || file.old_name;
            return <File key={filename}>
                <FileHeader><FileIcon filename={filename} />{filename}</FileHeader>
                <DiffViewer loadChanges={loadChanges} file={file} type={type} />
            </File>;
        })}
    </Container>;
}

const FileHeader = styled.div`
    padding: 4px 10px;
    line-height: 22px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    background-color: var(--ttw-foreground);
    color: var(--ttw-color);
    border-bottom: 1px solid var(--ttw-border-color);
    display: flex;
    align-items: center;

    .bi {
        margin-right: .25em;
    }
`;

const File = styled.div`
    background: var(--ttw-background);
    margin-top: 20px;
    border-top: 1px solid var(--ttw-border-color);
`;

const Container = styled.div`
  flex: auto;
  overflow-y: auto;
  padding-bottom: 20px;
  position: relative;
`;
