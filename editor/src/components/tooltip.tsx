import { OverlayTrigger, OverlayTriggerProps, Tooltip as BsTooltip } from 'react-bootstrap';
import { useIntl } from '@topwrite/common';
import { ReactElement } from 'react';
import { MessageType } from '../lib/use-format-message';

export interface TooltipProps {
    tooltip: string | MessageType;
    children: ReactElement;
    placement?: OverlayTriggerProps['placement'];
    wrap?: boolean;
}

export default function Tooltip({ tooltip, children, placement = 'bottom', wrap = false }: TooltipProps) {
    const intl = useIntl();

    if (intl.messages[tooltip]) {
        const descriptor = {
            id: tooltip
        };
        tooltip = intl.formatMessage(descriptor);
    }

    if (wrap) {
        children = <span>{children}</span>;
    }

    return <OverlayTrigger
        placement={placement}
        overlay={<BsTooltip>{tooltip}</BsTooltip>}
    >
        {children}
    </OverlayTrigger>;
}
