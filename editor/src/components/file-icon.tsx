import { createElement, useMemo } from 'react';
import mime from 'mime/lite';
import { find } from 'lodash';
import { IconBaseProps } from 'react-icons';
import { BsFile, BsFileEarmarkRuled, BsFileRichtext, BsFileText } from 'react-icons/bs';

interface FileIconProps extends IconBaseProps {
    filename: string
}

const ICONS = {
    'image/': BsFileRichtext,
    'application/': BsFileEarmarkRuled,
    'text/': BsFileText
};

export default function FileIcon({ filename, ...rest }: FileIconProps) {
    const comp = useMemo(() => {
        const type = mime.getType(filename) || '';

        return find(ICONS, function(_, index) {
            return 0 === type.indexOf(index);
        }) || BsFile;
    }, [filename]);

    return createElement(comp, { ...rest });
}
