import { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { Pagination as BsPagination } from 'react-bootstrap';

function noop() {
}


function isInteger(value: any): value is number {
    return (
        typeof value === 'number' && isFinite(value) && Math.floor(value) === value
    );
}

export interface PaginationProps {
    className?: string
    total: number
    current?: number
    defaultCurrent?: number
    pageSize?: number
    defaultPageSize?: number,
    onChange?: (current: number, pageSize: number) => void
}

export default function Pagination({
    total = 0,
    onChange = noop,
    defaultCurrent = 1,
    defaultPageSize = 10,
    className,
    ...props
}: PaginationProps) {

    const [current, setCurrent] = useState(defaultCurrent);
    const [pageSize, setPageSize] = useState(defaultPageSize);

    useEffect(() => {
        if (isInteger(props.current)) {
            setCurrent(props.current);
        }
    }, [props.current]);

    useEffect(() => {
        if (isInteger(props.pageSize)) {
            setPageSize(props.pageSize);
        }
    }, [props.pageSize]);

    const allPages = useMemo(() => {
        return Math.floor((total - 1) / pageSize) + 1;
    }, [total, pageSize]);

    const handleChange = useCallback((p: number) => {
        return () => {
            if (p !== current) {
                setCurrent(p);
                onChange(p, pageSize);
            }
        };
    }, [onChange, current, pageSize]);

    const pageBufferSize = 2;
    const prevPage = current - 1 > 0 ? current - 1 : 0;
    const nextPage = current + 1 < allPages ? current + 1 : allPages;

    const pagerList: ReactNode[] = [];
    let jumpPrev: ReactNode = null;
    let jumpNext: ReactNode = null;
    let firstPager: ReactNode = null;
    let lastPager: ReactNode = null;

    if (allPages <= 3 + pageBufferSize * 2) {
        for (let i = 1; i <= allPages; i += 1) {
            const active = current === i;
            pagerList.push(
                <BsPagination.Item key={i} active={active} onClick={handleChange(i)}>{i}</BsPagination.Item>
            );
        }
    } else {
        lastPager = <BsPagination.Last onClick={handleChange(allPages)} />;
        firstPager = <BsPagination.First onClick={handleChange(1)} />;
        jumpPrev = <BsPagination.Prev onClick={handleChange(prevPage)} />;
        jumpNext = <BsPagination.Next onClick={handleChange(nextPage)} />;

        let left = Math.max(1, current - pageBufferSize);
        let right = Math.min(current + pageBufferSize, allPages);

        if (current - 1 <= pageBufferSize) {
            right = 1 + pageBufferSize * 2;
        }

        if (allPages - current <= pageBufferSize) {
            left = allPages - pageBufferSize * 2;
        }

        for (let i = left; i <= right; i += 1) {
            const active = current === i;
            pagerList.push(
                <BsPagination.Item key={i} active={active} onClick={handleChange(i)}>{i}</BsPagination.Item>
            );
        }

        if (current - 1 >= pageBufferSize * 2 && current !== 1 + 2) {
            pagerList.unshift(jumpPrev);
        }
        if (
            allPages - current >= pageBufferSize * 2 &&
            current !== allPages - 2
        ) {
            pagerList.push(jumpNext);
        }

        if (left !== 1) {
            pagerList.unshift(firstPager);
        }
        if (right !== allPages) {
            pagerList.push(lastPager);
        }
    }

    return <BsPagination className={className}>
        {pagerList}
    </BsPagination>;

}
