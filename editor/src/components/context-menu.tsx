import { styled } from '@topwrite/common';
import { Dropdown } from 'react-bootstrap';
import { DropdownMenuProps } from 'react-bootstrap/DropdownMenu';
import { DropdownItemProps } from 'react-bootstrap/DropdownItem';
import { ReactNode } from 'react';

export default function ContextMenu(props: DropdownMenuProps) {
    return <DropdownMenu {...props} />;
}

export interface MenuItemProps extends DropdownItemProps {
    icon?: ReactNode;

    [key: string]: any;
}

export function MenuItem({ children, icon, ...props }: MenuItemProps) {
    return <DropdownItem {...props}>
        <Icon>{icon}</Icon>
        <Text>{children}</Text>
    </DropdownItem>;
}

const Text = styled.div`
  height: 28px;
  line-height: 28px;
  flex: auto;
`;

const Icon = styled.div`
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const DropdownItem = styled(Dropdown.Item)`
  display: flex;
  align-items: center;
  padding: 2px 10px 2px 5px;
`;

const DropdownMenu = styled(Dropdown.Menu)`
  padding: 0.5rem 0;
  font-size: 1rem;
  width: 150px;
`;
