import { Change, DiffFile } from 'repo';
import { Fragment } from 'react';
import Table, { ContentLine, NumberLine } from './table';

const applyLineGrouping = (lines: Change['lines']) => {
    const groups: [string[], string[], string[]][] = [];
    let oldLines: string[] = [];
    let newLines: string[] = [];
    for (let [mode, text] of lines) {
        if (
            (mode !== 1 && newLines.length) ||
            (mode === 0 && oldLines.length > 0)
        ) {
            groups.push([[], oldLines, newLines]);
            oldLines = [];
            newLines = [];
        }

        if (mode === 0) {
            groups.push([[text], [], []]);
        } else if (mode === 1 && oldLines.length === 0) {
            groups.push([[], [], [text]]);
        } else if (mode === 1 && oldLines.length > 0) {
            newLines.push(text);
        } else if (mode === -1) {
            oldLines.push(text);
        }
    }

    if (oldLines.length || newLines.length) {
        groups.push([[], oldLines, newLines]);
    }
    return groups;
};

export default function SplitTable({ changes }: { changes: DiffFile['changes'] }) {
    return <Table>
        <colgroup>
            <col width='44' />
            <col />
            <col width='44' />
            <col />
        </colgroup>
        <tbody>
        {changes.map((change, i) => {
            let oldLine = change.range_old_start;
            let newLine = change.range_new_start;

            const groups = applyLineGrouping(change.lines);

            let lines: { left?: string, right?: string }[] = [];

            for (let [contextLines, oldLines, newLines] of groups) {
                if (contextLines.length > 0) {
                    lines = lines.concat(contextLines.map(line => ({
                        left: line,
                        right: line
                    })));
                } else {
                    for (let i = 0; i < Math.max(oldLines.length, newLines.length); i++) {
                        lines.push({
                            left: oldLines[i],
                            right: newLines[i]
                        });
                    }
                }
            }

            return <Fragment key={`line-${i}`}>
                <tr key={`line-${i}-match`}>
                    <NumberLine>...</NumberLine>
                    <ContentLine match colSpan={3}>
                        @@
                        -{change.range_old_start},{change.range_old_count} +{change.range_new_start},{change.range_new_count} @@
                    </ContentLine>
                </tr>
                {lines.map(({ left, right }, j) => {

                    const hasOld = left !== undefined && left !== right;
                    const hasNew = right !== undefined && left !== right;

                    return <tr key={`line-${i}-${j}`}>
                        <NumberLine old={hasOld}>{left !== undefined && oldLine++}</NumberLine>
                        <ContentLine old={hasOld}>{left || ''}</ContentLine>
                        <NumberLine new={hasNew}>{right !== undefined && newLine++}</NumberLine>
                        <ContentLine new={hasNew}>{right || ''}</ContentLine>
                    </tr>;
                })}
            </Fragment>;
        })}
        </tbody>
    </Table>;
}
