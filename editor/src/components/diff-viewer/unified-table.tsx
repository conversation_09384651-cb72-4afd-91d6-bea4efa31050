import { DiffFile } from 'repo';
import { Fragment } from 'react';
import Table, { ContentLine, NumberLine } from './table';

export default function UnifiedTable({ changes }: { changes: DiffFile['changes'] }) {
    return <Table>
        <colgroup>
            <col width='44' />
            <col width='44' />
            <col />
        </colgroup>
        <tbody>
        {changes.map((change, i) => {

            let old_line = change.range_old_start;
            let new_line = change.range_new_start;

            return <Fragment key={`line-${i}`}>
                <tr key={`line-${i}-match`}>
                    <NumberLine colSpan={2}>...</NumberLine>
                    <ContentLine match>
                        @@
                        -{change.range_old_start},{change.range_old_count} +{change.range_new_start},{change.range_new_count} @@
                    </ContentLine>
                </tr>
                {change.lines.map((line, j) => {
                    let hasNew = false, hasOld = false;

                    switch (line[0]) {
                        case -1:
                            old_line++;
                            hasOld = true;
                            break;
                        case 1:
                            new_line++;
                            hasNew = true;
                            break;
                        case 0:
                            new_line++;
                            old_line++;
                            break;
                    }

                    return <tr key={`line-${i}-${j}`}>
                        <NumberLine old={hasOld} new={hasNew}>{line[0] !== 1 && old_line - 1}</NumberLine>
                        <NumberLine old={hasOld} new={hasNew}>{line[0] !== -1 && new_line - 1}</NumberLine>
                        <ContentLine old={hasOld} new={hasNew}>{line[1]}</ContentLine>
                    </tr>;
                })}
            </Fragment>;
        })}
        </tbody>
    </Table>;
}
