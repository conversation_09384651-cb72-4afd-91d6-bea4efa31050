import { css, styled } from '@topwrite/common';

const Table = styled.table`
    width: 100%;
    border: 0;
    margin: 0;
    padding: 0;
    table-layout: fixed;
    border-spacing: 0;

    td {
        line-height: 1.6;
        vertical-align: top;
    }
`;

interface BaseLineProps {
    match?: boolean;
    old?: boolean;
    new?: boolean;
}

const BaseLine = styled.td<BaseLineProps>`
    ${props => props.match && css`
        color: var(--ttw-diff-number-color);
        background-color: var(--ttw-foreground);
    `};

    ${props => props.new && css`
        background-color: var(--ttw-diff-new-background);
    `};
    
    ${props => props.old && css`
        background-color: var(--ttw-diff-old-background);
    `};
`;

export const NumberLine = styled(BaseLine)`
    user-select: none;
    margin: 0;
    padding: 0 10px 0 5px;
    border-right-width: 1px;
    border-right-style: solid;
    text-align: right;
    width: 1%;
    min-width: 50px;
    position: relative;
    white-space: nowrap;
    border-color: var(--ttw-border-color);
    color: var(--ttw-diff-number-color);
    background-color: var(--ttw-foreground);
`;

export const ContentLine = styled(BaseLine)`
    margin: 0;
    padding: 0 1.5em;
    border: 0;
    position: relative;
    white-space: pre-wrap;
    word-break: break-all;

    ${props => props.new && css`
        &:before {
            content: '+';
            position: absolute;
            left: 0.5em;
            color: #9bb0a1;
        }
    `}

    ${props => props.old && css`
        &:before {
            content: '-';
            position: absolute;
            left: 0.5em;
            color: #ae979a;
        }
    `}
`;

export default Table;
