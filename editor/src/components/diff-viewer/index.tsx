import Button from '../button';
import { Change, DiffFile } from 'repo';
import useFormatMessage from '../../lib/use-format-message';
import { styled } from '@topwrite/common';
import SplitTable from './split-table';
import UnifiedTable from './unified-table';
import { useCallback, useState } from 'react';

interface DiffViewerProps {
    file: DiffFile;
    loadChanges?: (filename: string) => Promise<Change[]>;
    type?: 'unified' | 'split';
    className?: string;
}

export default function DiffViewer({ loadChanges, file, type = 'split', className }: DiffViewerProps) {

    const t = useFormatMessage();
    const Table = type === 'split' ? SplitTable : UnifiedTable;

    const [changes, setChanges] = useState(file.changes);

    const onLoadChanges = useCallback(async () => {
        if (loadChanges) {
            const filename = file.new_name || file.old_name;
            setChanges(await loadChanges(filename));
        }
    }, [loadChanges, file]);

    return <Container className={className}>
        {file.is_binary ? <BinaryFile>
                <h3>Binary file</h3>
                <p>No preview for this file</p>
            </BinaryFile> :
            <TextFile>
                {changes.length === 0 && loadChanges ?
                    <Collapsed>
                        {t('diff-viewer.collapse')}
                        <Button onClick={onLoadChanges} variant='link'>{t('diff-viewer.expand')}</Button>
                    </Collapsed> :
                    <Table changes={changes} />
                }
            </TextFile>}
    </Container>;
}

const Collapsed = styled.div`
    display: flex;
    align-items: center;
    justify-content: center;

    .btn-link {
        text-decoration: none;
    }
`;

const TextFile = styled.div`
    background: var(--ttw-background);
    border-bottom: 1px solid var(--ttw-border-color);
`;

const BinaryFile = styled.div`
    text-align: center;
    width: 100%;
    color: #666;
    padding: 20px;
    background: var(--ttw-background);
    border-bottom: 1px solid var(--ttw-border-color);

    h3 {
        margin: 0 0 10px;
        font-size: 22px;
        font-weight: 500;
    }

    p {
        margin: 10px 0;
    }
`;

const Container = styled.div`
    background: var(--ttw-foreground);
`;
