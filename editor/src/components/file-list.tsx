import { css, styled } from '@topwrite/common';
import { forwardRef, HTM<PERSON>ttributes, MouseEvent<PERSON><PERSON>ler, ReactNode, RefObject } from 'react';

interface FileListProps {
    children: ReactNode;
}

export function FileList({ children }: FileListProps) {
    return <Container>
        {children}
    </Container>;
}

interface FileItemProps extends ItemWrapProps, Omit<HTMLAttributes<HTMLDivElement>, 'prefix'> {
    space?: ReactNode;
    suffix?: ReactNode;
    prefix?: ReactNode;
    text: ReactNode;
    onSpaceClick?: MouseEventHandler;
    wrapRef?: RefObject<HTMLLIElement>;
    active?: boolean;
    changed?: boolean;
    disabled?: boolean;
}

export const FileItem = forwardRef<HTMLDivElement, FileItemProps>((
    {
        children,
        space,
        onClick,
        onSpaceClick,
        suffix,
        prefix,
        text,
        open,
        wrapRef,
        active,
        changed,
        disabled,
        ...props
    },
    ref
) => {
    return <ItemWrap
        open={open}
        ref={wrapRef}
    >
        <Item
            {...props}
            $active={active}
            $changed={changed}
            $disabled={disabled}
            ref={ref}
        >
            <Whole onClick={onClick} />
            <ItemContent>
                {space && <Space onClick={onSpaceClick || onClick}>{space}</Space>}
                {prefix}
                <Text onClick={onClick}>{text}</Text>
                {suffix}
            </ItemContent>
        </Item>
        {children}
    </ItemWrap>;
});

export const Actions = styled.div`
    height: 32px;
    padding: 5px;
    display: flex;

    .btn {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 13px;
        padding: 0 5px;

        --bs-btn-bg: transparent;
        --bs-btn-border-color: transparent;
        --bs-btn-disabled-bg: transparent;
        --bs-btn-disabled-border-color: transparent;
    }

    .dropdown {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        .btn {
            flex: 1 1;
        }
    }
`;

const Whole = styled.div`
    width: 100%;
    position: absolute;
    left: 0;
    height: 32px;
`;

export const Icon = styled.span`
    width: 24px;
    display: flex;
    align-items: center;
    height: 32px;
    flex-shrink: 0;
    justify-content: center;
`;

const Space = styled.span`
    position: relative;
    width: 24px;
    height: 32px;
    line-height: 32px;
    text-decoration: none;
    text-align: center;
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
`;

const Text = styled.span`
    line-height: 32px;
    word-break: keep-all;
    white-space: nowrap;
    color: var(--ttw-color);
    vertical-align: top;
    height: 32px;
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;

    &:last-child {
        padding-right: 8px;
    }
`;

const Container = styled.ul`
    list-style: none;
    padding: 0;
    margin: 0;
`;

interface ItemWrapProps {
    open?: boolean;
}

const ItemWrap = styled.li<ItemWrapProps>`
    cursor: pointer;
    white-space: nowrap;

    ${Container} {
        display: none;

        > li {
            margin-left: 24px;
        }
    }

    ${({ open }) => open && css`
        > ${Container} {
            display: block;
        }
    `}
`;

interface ItemProps {
    $active?: boolean;
    $changed?: boolean;
    $disabled?: boolean;
}

const ItemContent = styled.div`
    position: relative;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
`;

const Item = styled.div<ItemProps>`
    ${({ $active }) => $active && css`
        > ${Whole} {
            background: var(--ttw-file-active-background);
        }
    `};

    ${({ $changed }) => $changed && css`
        > ${ItemContent} > ${Text} {
            color: var(--bs-success);
        }
    `};

    ${({ $disabled }) => $disabled && css`
        > ${ItemContent} > ${Text} {
            color: var(--ttw-file-disabled-color);
        }
    `};

    &:hover {
        > ${Whole} {
            background: var(--ttw-file-hover-background);
        }
    }
`;
