import { Commit } from 'repo';
import dayjs from 'dayjs';
import { css, styled } from '@topwrite/common';
import { MouseEventHandler } from 'react';

interface Props {
    commit: Commit;
    onClick?: MouseEventHandler;
    active?: boolean;
}

export default function CommitItem({ commit, onClick, active }: Props) {

    const date = dayjs(commit.author.date);

    return <Container onClick={onClick} active={active}>
        <Message>{commit.message}</Message>
        <Meta>
            <span className='me-1' title={date.format()}>
                {date.fromNow()}
            </span>
            by {commit.author.name}
        </Meta>
    </Container>;
}

const Meta = styled.div`
    margin: 0;
    color: var(--ttw-secondary-color)
`;

const Message = styled.div`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 15px;
  line-height: 1.5;
`;

const Container = styled.div<{ active?: boolean }>`
    border-bottom: 1px solid var(--ttw-border-color);
    padding: 8px;
    cursor: pointer;

    &:hover {
        background-color: var(--ttw-file-hover-background);
    }

    ${props => props.active && css`
        background-color: var(--ttw-file-active-background);
    `};
`;
