import { Provider<PERSON><PERSON>, TourProvider, useTour, } from '@reactour/tour';
import { styled } from '@topwrite/common';
import { HiCheck } from 'react-icons/hi';
import { useEffect } from 'react';

interface Props {
    open: boolean;
}

const TourAction = ({ open }: Props) => {
    const { setIsOpen, setCurrentStep } = useTour();
    useEffect(() => {
        setIsOpen(open);
        if (open) {
            setCurrentStep(0);
        }
    }, [open]);
    return null;
};

export default function Tour({ steps, open, children, ...props }: ProviderProps & Props) {

    return <TourProvider
        steps={steps}
        padding={5}
        styles={{
            close() {
                return {
                    position: 'absolute',
                    top: 12,
                    right: 12,
                    width: 9,
                    height: 9,
                };
            },
            maskWrapper() {
                return {
                    opacity: 0.5,
                    left: 0,
                    top: 0,
                    position: 'fixed',
                    zIndex: 99999,
                    pointerEvents: 'none',
                    color: '#000',
                };
            },
            popover() {
                return {
                    position: 'fixed',
                    maxWidth: 353,
                    backgroundColor: '#fff',
                    padding: '24px 30px 16px 30px',
                    boxShadow: '0 0.5em 3em rgba(0, 0, 0, 0.3)',
                    color: 'inherit',
                    zIndex: 100000,
                    transition: 'transform 0.3s',
                    top: 0,
                    left: 0,
                    borderRadius: 'var(--bs-border-radius, 0.5rem)',
                };
            },
            svg() {
                return {
                    display: 'block',
                    color: '#646464'
                };
            }
        }}
        nextButton={({ currentStep, stepsLength, Button, setIsOpen }) => {
            if (currentStep === stepsLength - 1) {
                return <DoneButton>
                    <HiCheck size={20} onClick={() => setIsOpen(false)} color={'#646464'} />
                </DoneButton>;
            }
            return <Button kind='next' />;
        }}
        onClickMask={() => {
        }} // disable mask click
        disableKeyboardNavigation={true}
        {...props}
    >
        <TourAction open={open} />
        {children}
    </TourProvider>;
}

const DoneButton = styled.button`
  display: block;
  padding: 0px;
  border: 0px;
  background: none;
  cursor: pointer;
`;
