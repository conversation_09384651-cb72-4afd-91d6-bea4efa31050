import { StepType, useTour } from '@reactour/tour';
import { ReactNode } from 'react';
import Tour from './index';
import { useLocalStorageState, useModel, useSelector } from '@topwrite/common';
import useFormatMessage from '../../lib/use-format-message';

interface Props {
    children: ReactNode;
}

const VERSION = '0.0.1';

export default function EditorTour({ children }: Props) {
    const { preview } = useSelector('options');
    const t = useFormatMessage();

    const steps: StepType[] = [
        {
            selector: '[data-tour="editor-tools"]',
            content: <div dangerouslySetInnerHTML={{ __html: t('tour.editor.tools') }} />,
        },
        {
            selector: '[data-tour="editor-history"]',
            content: <div dangerouslySetInnerHTML={{ __html: t('tour.editor.history') }} />,
        },
        {
            selector: '[data-tour="editor-markdown"]',
            content: <div dangerouslySetInnerHTML={{ __html: t('tour.editor.markdown') }} />,
        },
    ];

    if (preview) steps.push({
        selector: '[data-tour="editor-preview"]',
        content: <div dangerouslySetInnerHTML={{ __html: t('tour.editor.preview') }} />,
    });

    const { isOpen } = useTour();

    const [{ user: { tourVersion } }, { setTourVersion }] = useModel('workspace');

    const [version, setVersion] = useLocalStorageState('tour.editor', tourVersion.editor);

    return <Tour
        open={!isOpen && version !== VERSION}
        steps={steps}
        beforeClose={() => {
            setVersion(VERSION);
            setTourVersion('editor', VERSION);
        }}
    >{children}</Tour>;
}
