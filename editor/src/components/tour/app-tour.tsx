import { StepType } from '@reactour/tour';
import { ReactNode } from 'react';
import Tour from './index';
import { useLocalStorageState, useModel } from '@topwrite/common';
import useFormatMessage from '../../lib/use-format-message';

interface Props {
    children: ReactNode;
}

const VERSION = '0.0.1';

export default function AppTour({ children }: Props) {

    const t = useFormatMessage();

    const steps: StepType[] = [
        {
            selector: '[data-tour="catalog"]',
            content: <div dangerouslySetInnerHTML={{ __html: t('tour.app.catalog') }} />,
            highlightedSelectors: ['[data-tour="catalog-create"]'],
            mutationObservables: ['[data-tour="catalog-pane"]'],
        },
        {
            selector: '[data-tour="commit"]',
            content: <div dangerouslySetInnerHTML={{ __html: t('tour.app.commit') }} />
        },
        {
            selector: '[data-tour="plugin"]',
            content: <div dangerouslySetInnerHTML={{ __html: t('tour.app.plugin') }} />,
        }
    ];

    const [{ user: { tourVersion } }, { setTourVersion }] = useModel('workspace');

    const [version, setVersion] = useLocalStorageState('tour.app', tourVersion.app);

    return <Tour
        open={version !== VERSION}
        steps={steps}
        beforeClose={() => {
            setVersion(VERSION);
            setTourVersion('app', VERSION);
        }}
    >
        {children}
    </Tour>;
}
