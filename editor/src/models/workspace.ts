import { ActionsType, Model } from '@topwrite/common';
import { ReactNode } from 'react';
import { socket } from '../lib/socket';
import { ReleaseLog, State } from 'repo';

export interface WorkspaceType {
    extra: ReactNode;
    loading: boolean;
    lineNumber: number;
    branch: string;
    current: string | null;
    treeEntries: State['workspace']['treeEntries'];
    status: State['workspace']['status'];
    user: State['workspace']['user'];
    syncing: State['workspace']['syncing'];
    release: State['workspace']['release'];
}

class Workspace extends Model<WorkspaceType> {

    initialState = {
        extra: null,
        current: null,
        lineNumber: 0,
        loading: false,
        syncing: false,
        release: 'idle'
    } as const;

    subscription({ updateStatus, updateTreeEntries, setSyncing, setRelease }: ActionsType<Workspace>) {
        socket.on('status.change', (status) => {
            updateStatus(status);
        });
        socket.on('tree.change', (entries) => {
            updateTreeEntries(entries);
        });

        socket.on('workspace.syncing', (syncing) => {
            setSyncing(syncing);
        });

        socket.on('workspace.release', (release) => {
            setRelease(release);
        });
    }

    *updateTreeEntries(entries: State['workspace']['treeEntries']) {
        yield this.setState((state) => {
            state.treeEntries = entries;
        });
    }

    *updateStatus(status: State['workspace']['status']) {
        yield this.setState((state) => {
            state.status = status;
        });
    }

    *setExtra(extra: ReactNode) {
        yield this.setState(state => {
            state.extra = extra;
        });
    }

    async *setCurrent(current: string | null, lineNumber: number = 0) {
        yield this.setState(state => {
            state.current = current;
            state.lineNumber = lineNumber;
        });
        await socket.setCurrent(current);
    }

    async *removeFile(path: string) {
        await socket.removeFile(path);
        const { current } = yield *this.getState('workspace');

        if (current === path) {
            await (yield *this.setCurrent(null));
        }
    }

    async *renameFile(oldPath: string, newPath: string) {
        await socket.renameFile(oldPath, newPath);

        const { current } = yield *this.getState('workspace');

        if (current === oldPath) {
            await (yield *this.setCurrent(newPath));
        }
    }

    *sync() {
        yield this.setState(state => {
            state.syncing = true;
        });
        socket.sync();
    }

    *commit(message: string, release: boolean) {
        yield this.setState(state => {
            state.syncing = true;
        });
        socket.commit(message, release);
    }

    *setSyncing(syncing: boolean) {
        yield this.setState(state => {
            state.syncing = syncing;
        });
    }

    *setRelease(release: ReleaseLog['status_text']) {
        yield this.setState(state => {
            state.release = release;
        });
    }

    *setTourVersion(name: string, version: string) {
        yield this.setState(state => {
            state.user.tourVersion[name] = version;
        });
        socket.setTourVersion(name, version);
    }
}

export const workspace = new Workspace();
