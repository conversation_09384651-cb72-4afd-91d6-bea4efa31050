import { Model } from '@topwrite/common';

interface SettingsType {
    useDocument: boolean,
}

const DEFAULT: SettingsType = {
    useDocument: false
};

class Settings extends Model<SettingsType> {

    initialState = () => {
        try {
            let setting = localStorage.getItem('editor.setting');
            if (setting) {
                return Object.assign(DEFAULT, JSON.parse(setting));
            }
        } catch (e) {
        }

        return DEFAULT;
    };

    *update(updater: (settings: SettingsType) => void) {
        yield this.setState(state => {
            updater(state);
        });
        const settings = yield *this.getState('settings');
        localStorage.setItem('editor.setting', JSON.stringify(settings));
    }
}

export const settings = new Settings();
