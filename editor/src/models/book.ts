import { ActionsType, Book as BookType, Config, Model, Summary } from '@topwrite/common';
import { socket } from '../lib/socket';

class Book extends Model<BookType> {

    lock = false;

    subscription({ checkRefresh }: ActionsType<Book>) {
        socket.on('file.change', (files) => {
            if (!this.lock) {
                checkRefresh(files);
            } else {
                this.lock = false;
            }
        });
    }

    async *checkRefresh(files: string[]) {
        if (files.includes(Config.file)) {
            const config = await socket.getConfig();
            yield this.setState(state => {
                state.config = config;
            });
        }
        if (files.includes(Summary.file.json) || files.includes(Summary.file.markdown)) {
            const summary = await socket.getSummary();
            yield this.setState(state => {
                state.summary = summary;
            });
        }
    }

    async *updateSummary(updater: (summary: Summary) => void) {
        this.lock = true;
        yield this.setState(state => {
            updater(state.summary);
        });

        const { summary } = yield *this.getState('book');
        if (summary.isJson() || summary.hasMetadata()) {
            socket.setSummary(summary.toObject());
        } else {
            socket.setSummary(summary.toText());
        }
    }

    async *updateConfig(updater: (config: Config) => void) {
        this.lock = true;
        yield this.setState(state => {
            updater(state.config);
        });
        const { config } = yield *this.getState('book');
        socket.setConfig(config.toObject());
    }
}

export const book = new Book();
