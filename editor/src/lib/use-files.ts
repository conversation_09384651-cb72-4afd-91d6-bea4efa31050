import { useSelector, useBook, Config } from '@topwrite/common';
import { useMemo } from 'react';
import { BaseEntry, TreeEntries } from 'repo';
import path from 'path';
import getFullPath from './get-full-path';

const walkEntries = (func: (entry: BaseEntry, dir: string) => void, entries: TreeEntries, root: string = '') => {
    Object.values(entries).forEach((entry) => {
        if (entry.type === 'tree') {
            walkEntries(func, entry.children, path.join(root, entry.name));
        } else {
            func(entry, root);
        }
    });
};

export default function useFiles() {
    const { status: { changes }, treeEntries } = useSelector('workspace');
    const { config } = useBook();

    return useMemo(() => {
        const root = config.getValue('root', '');

        const files = Object.entries(changes).filter(([name, type]) => {
            if (root && !name.startsWith(`${root}/`) && name !== Config.file) {
                return false;
            }
            return type === 'A';
        }).map(([name]) => {
            if (root) {
                return name.slice(root.length + 1);
            }
            return name;
        });

        walkEntries((entry, dir) => {
            const filename = path.join(dir, entry.name);
            const fullPath = getFullPath(filename, root);
            if (fullPath in changes && changes[fullPath] === 'D') {
                return;
            }
            files.push(filename);
        }, treeEntries);

        return files;
    }, [changes, treeEntries]);
}

export type FileEntry = {
    type: 'file'
    path: string
} | {
    type: 'dir'
    path: string
    children: FileEntries
}

export interface FileEntries {
    [index: string]: FileEntry;
}

export function useFileEntries(): FileEntries {
    const files = useFiles();

    return useMemo(() => {

        const root = {
            children: {}
        };

        const rootPath = '';

        for (const file of files) {
            const rPath = path.relative('.', file);
            const pathList = rPath.split('/');

            let current = root;
            let currentPath = rootPath;

            pathList.forEach((item, index) => {
                const isLast = index === pathList.length - 1;

                currentPath = path.join(currentPath, item);

                if (!isLast) {
                    item += '/';
                }

                if (!current.children[item]) {
                    current.children[item] = isLast ? { path: currentPath, type: 'file' } : {
                        path: currentPath,
                        type: 'dir',
                        children: {}
                    };
                }

                current = current.children[item];
            });
        }
        return root.children;
    }, [files]);
}
