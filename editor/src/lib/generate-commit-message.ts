/**
 * 根据文件变更生成默认的提交消息
 * @param changes 文件变更对象，键为文件路径，值为变更类型（'A'|'M'|'D'|'U'）
 * @returns 生成的提交消息
 */
export function generateCommitMessage(changes: Record<string, string>): string {
    const update: string[] = [];
    const remove: string[] = [];
    
    for (const [path, change] of Object.entries(changes)) {
        if (change === 'D') {
            remove.push(path);
        } else {
            update.push(path);
        }
    }
    
    let message = '';
    if (update.length > 0) {
        message += `Update ` + update.join(', ');
    }
    if (remove.length > 0) {
        if (message.length > 0) {
            message += '\n';
        }
        message += 'Delete ' + remove.join(', ');
    }
    
    return message;
}
