import { PluginMeta } from '@topwrite/common';

export function getInstalledTheme() {
    return getInstalledPlugins().find(plugin => plugin.type === 'theme');
}

export function getInstalledExtensions() {
    return getInstalledPlugins().filter(plugin => plugin.type === 'extension');
}

export function isDevOrPresetPlugin(meta: PluginMeta) {
    return !!meta.preset || !!window.TopWritePlugins[meta.name]?.meta;
}

export function updatePlugin(updater: (meta: PluginMeta) => PluginMeta | void) {
    window.TopWritePluginsMeta = window.TopWritePluginsMeta.map((meta) => {
        const newMeta = updater(meta);

        return newMeta || meta;
    });
}

export function isBrokenPlugin(meta: PluginMeta) {

    const installed = getInstalledPlugins().map(plugin => plugin.name);

    return installed.includes(meta.name) && !meta.disabled && !window.TopWritePlugins[meta.name];
}

export function getCurrentTheme() {
    return window.TopWritePluginsMeta.filter(meta => !meta.builtin && meta.type === 'theme')[0];
}

export function getCurrentExtensions() {
    return window.TopWritePluginsMeta.filter(meta => !meta.builtin && meta.type === 'extension').sort((a, b) => {

        if (a.preset !== b.preset) {
            return a.preset ? -1 : 1;
        }

        return 0;
    });
}

export function getInstalledPlugins() {
    return window.TopWritePluginsMeta.filter(meta => !meta.builtin).sort((a, b) => {
        if (a.type !== b.type) {
            return a.type === 'theme' ? -1 : 1;
        }

        if (a.preset !== b.preset) {
            return a.preset ? -1 : 1;
        }

        return 0;
    });
}
