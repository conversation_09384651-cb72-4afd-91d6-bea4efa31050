import { useIntl } from '@topwrite/common';
import { ReactElement, useCallback } from 'react';
import localeZh from '../lang/zh-CN.json';

type MessageFormatPrimitiveValue = string | number | boolean | null | undefined;

type MessageFormatValues = Record<string, MessageFormatPrimitiveValue | ReactElement>;

export type MessageType = keyof typeof localeZh

export type MessageFormater = (message: MessageType | string, values?: MessageFormatValues) => string;

export default function useFormatMessage() {
    const intl = useIntl();

    return useCallback<MessageFormater>(function(message, values) {
        const descriptor = {
            id: message
        };
        return intl.formatMessage(descriptor, values) as string;
    }, [intl]);
}
