import typescript from "rollup-plugin-typescript2";
import babel from "@rollup/plugin-babel";
import commonjs from "@rollup/plugin-commonjs";
import { terser } from "rollup-plugin-terser";
import resolve from "@rollup/plugin-node-resolve";
import json from "@rollup/plugin-json";
import pkg from "./package.json";
import { DEFAULT_EXTENSIONS } from "@babel/core";

const external = [
    ...Object.keys(pkg.dependencies || {}),
    ...Object.keys(pkg.peerDependencies || {})
].map((name) => new RegExp(`^${name}`));

const isProduction = process.env.NODE_ENV === "production";

export default {
    input  : "src/index.ts",
    output : {file: "dist/index.js", sourcemap: true, format: "cjs", indent: false},
    external,
    plugins: [
        resolve({
            preferBuiltins: true
        }),
        typescript({clean: true, useTsconfigDeclarationDir: true}),
        babel({
            extensions  : [
                ...DEFAULT_EXTENSIONS,
                "ts",
                "tsx"
            ],
            plugins     : ["@babel/plugin-transform-runtime"],
            babelHelpers: "runtime"
        }),
        json(),
        commonjs(),
        isProduction && terser()
    ].filter(Boolean)
};
