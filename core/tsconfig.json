{"compilerOptions": {"target": "esnext", "module": "esnext", "lib": ["dom", "es2017"], "jsx": "react", "declaration": true, "declarationDir": "./types", "sourceMap": true, "removeComments": false, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "moduleResolution": "node", "baseUrl": "./src", "esModuleInterop": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "skipLibCheck": true}, "include": ["./src/**/*"]}