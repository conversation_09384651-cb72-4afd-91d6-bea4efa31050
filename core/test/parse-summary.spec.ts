import parseSummary from '../src/lib/parse-summary';

describe('parseSummary', () => {
    it('should parse summary if exists', () => {

        const content = `
# Summary

gegege
## fefe[Hello](hello.md)
* [Hello](hello.md)
    * [bbbb](bb.md)
    * gegexx
    
    
    * [bbbb11](bb111.md)
    * gegexx222
* uuuu

## xxxx
* feafae

* feafae222

---
* [Hello](hello.md)
`;

        console.dir(parseSummary(content), { depth: null });
    });
});
