import { AxiosStatic } from 'axios';
import Book from './entities/book';
import SummaryArticle from './entities/summary-article';
import fs, { ReadStream, WriteStream } from 'fs-extra';

interface Dirent {
    type: 'File' | 'Directory';
    path: string;
}

interface Logger {
    fatal(message: any, ...args: any[]): void;

    error(message: any, ...args: any[]): void;

    warn(message: any, ...args: any[]): void;

    log(message: any, ...args: any[]): void;

    info(message: any, ...args: any[]): void;

    start(message: any, ...args: any[]): void;

    success(message: any, ...args: any[]): void;

    ready(message: any, ...args: any[]): void;

    debug(message: any, ...args: any[]): void;

    trace(message: any, ...args: any[]): void;
}

interface FS {
    createReadStream(filename: string, options?: Parameters<typeof fs.createReadStream>[1]): ReadStream;

    readFile(filename: string): Promise<Buffer>;

    readAsObject(filename: string): Promise<object>;

    readAsString(filename: string): Promise<string>;

    createWriteStream(filename: string, options?: Parameters<typeof fs.createWriteStream>[1]): WriteStream;

    writeFile(filename: string, data: any): Promise<void>;

    appendFile(filename: string, data: any): Promise<void>;

    exist(filename: string): boolean;

    unlink(filename: string): Promise<void>;

    readDir(path?: string): Promise<Dirent[]>;

    archive(output: WriteStream, format?: 'tar' | 'zip'): Promise<void>;
}

export interface HookContext {
    book: Book;
    tmpFS: FS;
    sourceFS: FS;
    request: AxiosStatic;
    logger: Logger;
    format: string;
}

type HookFunction<Args extends Array<any> = [], Result = void> = (this: HookContext, ...args: Args) => Promise<Result>

export interface Hooks {
    init?: HookFunction;
    summary?: HookFunction;
    article?: HookFunction<[SummaryArticle]>;
    articles?: HookFunction;
    beforeFinish?: HookFunction<[], boolean>;
    finish?: HookFunction;
}
