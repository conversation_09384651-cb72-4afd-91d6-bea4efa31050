{"title": "TopWrite Configuration", "type": "object", "properties": {"root": {"type": "string", "title": "Path from the root folder containing the book's content"}, "title": {"type": "string", "title": "Title of the book"}, "author": {"type": "object", "title": "attr of the author", "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "home": {"type": "string"}}}, "theme": {"type": "string", "title": "Name of the theme", "default": "default"}, "themeConfig": {"type": "object", "title": "Configuration for theme", "properties": {"defaultMode": {"type": "string", "title": "Default mode", "default": "light"}, "primaryColor": {"type": "string", "title": "Primary Color", "format": "color", "default": "#3c60ff"}, "expandLevel": {"type": "integer", "title": "Expand level", "default": 0}, "navs": {"type": "array", "title": "Nav configures", "items": {"type": "object", "properties": {"title": {"title": "Label", "type": "string"}, "url": {"title": "Link", "type": "string"}}, "required": ["title", "url"], "additionalProperties": false}, "default": []}}, "default": {}}, "plugins": {"type": "array", "items": {"type": "string"}, "default": []}, "pluginsConfig": {"type": "object", "title": "Configuration for plugins", "default": {}}, "pdf": {"type": "object", "title": "PDF specific configurations", "properties": {"fontFamily": {"type": "string", "default": "Microsoft Yahei", "title": "Font family for the PDF output"}, "margin": {"type": "object", "title": "Margins (unit: pt)", "properties": {"right": {"type": "integer", "title": "Right Margin", "minimum": 0, "maximum": 100, "default": 30}, "left": {"type": "integer", "title": "Left Margin", "minimum": 0, "maximum": 100, "default": 30}, "top": {"type": "integer", "title": "Top Margin", "minimum": 0, "maximum": 100, "default": 40}, "bottom": {"type": "integer", "title": "Bottom Margin", "minimum": 0, "maximum": 100, "default": 40}}, "default": {}}, "paperSize": {"type": "string", "enum": ["a0", "a1", "a2", "a3", "a4", "a5", "a6", "b0", "b1", "b2", "b3", "b4", "b5", "b6", "legal", "letter"], "default": "a4", "title": "Paper size for the PDF"}, "chapterMark": {"type": "string", "enum": ["pagebreak", "rule", "both", "none"], "default": "pagebreak", "title": "How to mark detected chapters"}, "pageBreaksBefore": {"type": "string", "default": "/", "title": "An XPath expression. Page breaks are inserted before the specified elements. To disable use the expression: \"/\""}, "pageNumbers": {"type": "boolean", "default": true, "title": "Add page numbers to the bottom of every page"}}, "default": {}}, "release": {"type": "object", "title": "Release specific configurations", "properties": {"pathEncode": {"type": "boolean", "title": "Whether to encode the chapter path", "default": true}}, "default": {}}, "assistant": {"type": "object", "title": "AI Assistant configurations", "properties": {"token": {"type": "string", "title": "ThinkAI Token", "encrypt": true}}, "default": {}}}, "required": []}