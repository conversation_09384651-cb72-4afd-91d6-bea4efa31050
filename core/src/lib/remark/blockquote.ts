import blockQuote from '../micromark/block-quote';
import { Plugin } from 'unified';
import { visit } from 'unist-util-visit';
import { blockquote } from 'mdast-util-to-markdown/lib/handle/blockquote';
import { Blockquote, Root } from 'mdast';
import { codes } from 'micromark-util-symbol/codes';

const remarkBlockquote: Plugin<void[], Root, Root> = function() {

    const data = this.data();

    data.toMarkdownExtensions.push({
        handlers: {
            blockquote: (node, parent, context, safeOptions) => {

                const text = blockquote(node, parent, context, safeOptions);

                if (node.variant) {
                    return text.replace(/(^|\s+)>\s/, `$1> [${node.variant}] `);
                }
                return text;
            }
        }
    });

    data.micromarkExtensions.push({
        document: {
            [codes.greaterThan]: blockQuote
        },
    });

    data.fromMarkdownExtensions.push({
        enter: {
            blockQuoteType: function() {
                this.buffer();
            }
        },
        exit: {
            blockQuoteType: function() {
                const variant = this.resume();
                const node = this.stack[this.stack.length - 1] as Blockquote;
                node.variant = variant;
            }
        }
    });

    return function(tree) {
        visit(tree, 'blockquote', (node: Blockquote) => {
            if (node.variant) {
                node.data = {
                    ...node.data,
                    hProperties: { className: node.variant }
                };
            }
        });
    };

};

export default remarkBlockquote;
