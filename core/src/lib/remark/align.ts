import { Plugin } from 'unified';
import { visit } from 'unist-util-visit';
import { select } from 'unist-util-select';
import { paragraph } from 'mdast-util-to-markdown/lib/handle/paragraph';
import { heading } from 'mdast-util-to-markdown/lib/handle/heading';
import { Heading, Paragraph, Root, Text } from 'mdast';
import { Node } from 'unist';

const buildAlign = (align: any) => {
    let markup;
    switch (align) {
        case 'right':
            markup = '--: ';
            break;
        case 'center':
            markup = ':-: ';
            break;
        default:
            markup = '';
    }
    return markup;
};

const isParagraph = (node: Node): node is Heading | Paragraph => {
    return node.type === 'paragraph' || node.type === 'heading';
};

const remarkAlign: Plugin<void[], Root, Root> = function() {
    const data = this.data();

    data.toMarkdownExtensions.push({
        handlers: {
            paragraph: (node, parent, context, safeOptions) => {
                const text = paragraph(node, parent, context, safeOptions);

                return buildAlign(node.align) + text;
            },
            heading: (node, parent, context, safeOptions) => {
                const text = heading(node, parent, context, safeOptions);

                return text.replace(/^#+\s/, `$&${buildAlign(node.align)}`);
            }
        }
    });

    return function(tree) {

        visit(tree, (node, _, parent) => {
            if (isParagraph(node)) {
                const text = select('text', node) as Text | null;

                if (text) {
                    const value = text.value;
                    if (value.startsWith('--: ')) {
                        node.align = 'right';
                    } else if (value.startsWith(':-: ')) {
                        node.align = 'center';
                    } else if (value.startsWith(':-- ')) {
                        node.align = 'left';
                    }
                    if (node.align) {
                        text.value = value.substr(4);
                        if (parent && parent.type === 'listItem') {
                            parent.data = {
                                ...parent.data,
                                hProperties: { className: `align-${node.align}` }
                            };
                        } else {
                            node.data = {
                                ...node.data,
                                hProperties: { className: `align-${node.align}` }
                            };
                        }
                    }
                }
            }
        });
    };
};

export default remarkAlign;
