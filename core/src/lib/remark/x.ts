import { Plugin } from 'unified';
import { visit } from 'unist-util-visit';
import { Code, InlineCode, Root, XBlock, XInline } from 'mdast';
import { Node } from 'unist';

const isCode = (node: Node): node is Code => {
    return node.type === 'code';
};

const isInlineCode = (node: Node): node is InlineCode => {
    return node.type === 'inlineCode';
};

const remarkX: Plugin<void[], Root, Root> = function() {
    const data = this.data();

    data.toHastExtensions.push({
        xBlock(h, node) {
            return h({}, 'x-block', {
                name: node.name as string,
                value: node.value as string,
                parameter: node.parameter as string
            });
        },
        xInline(h, node) {
            return h({}, 'x-inline', { name: node.name as string, value: node.value as string });
        },
    });

    return function(tree) {

        visit(tree, (node, index, parent) => {
            if (index !== null && parent !== null) {
                if (isCode(node) && node.lang) {
                    const match = /\[([\w-_]+)(:([\w-_]+))?]/g.exec(node.lang);
                    if (match) {
                        const name = match[1];
                        const parameter = match[3] || '';

                        const child: XBlock = {
                            type: 'xBlock',
                            name,
                            parameter,
                            value: node.value,
                            position: node.position
                        };

                        parent.children.splice(index, 1, child);
                    }
                } else if (isInlineCode(node)) {
                    const match = /^([^\s]+)\s(.*?)\s\1$/.exec(node.value as string);
                    if (match) {
                        const name = match[1];
                        const value = match[2];

                        const child: XInline = {
                            type: 'xInline',
                            name,
                            value,
                            position: node.position
                        };

                        parent.children.splice(index, 1, child);
                    }
                }
            }
        });
    };
};

export default remarkX;
