import { Plugin } from 'unified';
import { codes } from 'micromark-util-symbol/codes';
import { blankLine } from '../micromark/blank-line';
import { Root } from 'mdast';

const blankLinePlugin: Plugin<void[], Root, Root> = function() {
    const data = this.data();

    data.toHastExtensions.push({
        blankLine(h) {
            return h({}, 'p', [h({}, 'br')]);
        },
    });

    data.micromarkExtensions.push({
        flow: {
            [codes.caret]: blankLine
        },
    });

    data.toMarkdownExtensions.push({
        handlers: {
            blankLine: () => {
                return '^';
            }
        }
    });

    data.fromMarkdownExtensions.push({
        enter: {
            blankLine: function(token) {
                this.enter({ type: 'blankLine' }, token);
            }
        },
        exit: {
            blankLine: function(token) {
                this.exit(token);
            }
        }
    });

};
export default blankLinePlugin;
