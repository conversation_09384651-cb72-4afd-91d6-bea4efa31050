import { Plugin } from 'unified';
import { encode } from 'mdurl';
import { image } from 'mdast-util-to-markdown/lib/handle/image';
import labelEnd from '../micromark/label-end';
import { codes } from 'micromark-util-symbol/codes';
import { Image, Root } from 'mdast';

const imageHandler: typeof image = (node, parent, context, safeOptions) => {
    const value = image(node, parent, context, safeOptions);
    if (node.width || node.height) {
        return value.replace(/\)$/, ` =${node.width || ''}${node.height ? `x${node.height}` : ''})`);
    }
    return value;
};

imageHandler.peek = image.peek;

const remarkImageSize: Plugin<void[], Root, Root> = function() {
    const data = this.data();

    data.micromarkExtensions.push({
        text: {
            [codes.rightSquareBracket]: labelEnd
        },
    });

    data.fromMarkdownExtensions.push({
        enter: {
            resourceSizeWidth: function() {
                this.buffer();
            },
            resourceSizeHeight: function() {
                this.buffer();
            }
        },
        exit: {
            resourceSizeWidth: function() {
                const width = this.resume();
                const node = this.stack[this.stack.length - 1] as Image;
                node.width = width;
            },
            resourceSizeHeight: function() {
                const height = this.resume();
                const node = this.stack[this.stack.length - 1] as Image;
                node.height = height;
            }
        }
    });

    data.toMarkdownExtensions.push({
        handlers: {
            image: imageHandler
        }
    });

    data.toHastExtensions.push({
        image: (h, node) => {
            const props: any = { src: encode(node.url), alt: node.alt };

            if (node.title !== null && node.title !== undefined) {
                props.title = node.title;
            }

            props.width = node.width;
            props.height = node.height;

            return h(node, 'img', props);
        }
    });
};

export default remarkImageSize;
