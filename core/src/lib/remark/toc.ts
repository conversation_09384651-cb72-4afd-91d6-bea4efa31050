import { Plugin } from 'unified';
import { SKIP, visit } from 'unist-util-visit';
import toString from '../unist/unist-util-to-string';
import { Root } from 'mdast';

const REGEXP = /^\[toc(?:=(\d)(?:,(\d))?)?]$/im;

const remarkToc: Plugin<void[], Root, Root> = function() {
    return function(tree) {
        visit(tree, (node, index, parent) => {
            if (parent && parent.type === 'root' && index !== null) {
                const text = toString(node);
                const match = text.match(REGEXP);
                if (match) {
                    parent.children.splice(index, 1);
                    return [SKIP, index];
                }
            }
        });
    };
};

export default remarkToc;
