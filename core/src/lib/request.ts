import request, { AxiosInstance, AxiosRequestConfig, EventSourceMessage, Cancel } from 'axios';
import rax, { RetryConfig } from 'retry-axios';
import isRecord from './is-record';

export type Errors = string | {
    [key: string]: string
}

declare module 'retry-axios' {
    interface RetryConfig {
        retryDecider?: () => boolean;
    }
}
declare module 'axios' {
    interface AxiosError {
        errors: Errors;
    }

    interface EventSourceMessage {
        /** The event ID to set the EventSource object's last event ID value. */
        id: string;
        /** A string identifying the type of event described. */
        event: string;
        /** The event data */
        data: string;
        /** The reconnection interval (in milliseconds) to wait before retrying the connection */
        retry?: number;
    }

    interface AxiosRequestConfig {
        raxConfig?: RetryConfig;
        onMessage?: (message: EventSourceMessage) => void;
    }
}

rax.attach();
request.defaults.raxConfig = {
    retryDelay: 2000,
    backoffType: 'static',
    shouldRetry: ({ config, response }) => {
        const raxConfig = config.raxConfig || {};

        if (raxConfig?.retryDecider && !raxConfig.retryDecider()) {
            return false;
        }

        if (!config.method || !raxConfig.httpMethodsToRetry?.includes(config.method.toUpperCase())) {
            return false;
        }

        return response?.status === 449;
    }
};
request.defaults.maxContentLength = Infinity;
request.defaults.maxBodyLength = Infinity;
request.interceptors.request.use(config => {
    if (config.onMessage) {
        let loaded = 0;
        let buffer = '';
        let message: EventSourceMessage | null = null;

        const onMessage = config.onMessage;
        config.onDownloadProgress = (progressEvent) => {
            const xhr = progressEvent.target;
            const { responseText } = xhr;

            while (loaded < progressEvent.loaded) {
                const text = responseText.substring(loaded, ++loaded);
                buffer += text;
                if (text === '\n') {
                    if (!message) {
                        message = { id: '', event: '', data: '', };
                    }
                    if (buffer === '\n') {
                        //单条消息结束
                        onMessage(message);
                        message = null;
                    } else {
                        const match = buffer.match(/^data:(?<data>.*)/);
                        if (match) {
                            message.data = match.groups!.data.trim();
                        }
                    }
                    buffer = '';
                }
            }
        };
    }
    return config;
});
request.interceptors.response.use(
    response => {
        return response;
    },
    e => {
        if (request.isAxiosError(e)) {
            if (e.response) {
                const { data, status } = e.response;
                if (status === 401) {
                    e.errors = 'Unauthorized';
                } else {
                    if (isRecord(data)) {
                        if (status === 422) {
                            e.errors = data;
                        } else if ('message' in data) {
                            e.errors = data['message'];
                        }
                    } else {
                        e.errors = data as string;
                    }
                }

                if (typeof e.errors !== 'string') {
                    e.message = Object.values(e.errors).join('\n');
                } else {
                    e.message = e.errors;
                }
            }
        }
        return Promise.reject(e);
    }
);

export default request;

export type RequestConfig = AxiosRequestConfig;
export type RequestInstance = AxiosInstance;
export const isRequestError = request.isAxiosError;
export const isCancel = request.isCancel;
export type {
    Cancel
};
