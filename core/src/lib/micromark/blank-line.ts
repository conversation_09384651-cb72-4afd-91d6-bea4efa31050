import { Code, State, Tokenizer } from 'micromark-util-types';
import { codes } from 'micromark-util-symbol/codes';
import assert from 'assert';
import { markdownLineEnding, markdownSpace } from 'micromark-util-character';
import { factorySpace } from 'micromark-factory-space';
import { types } from 'micromark-util-symbol/types';

const blankLineType = 'blankLine';
const blankLineSequenceType = 'blankLineSequence';

const tokenizeBlankLine: Tokenizer = (effects, ok, nok) => {

    let marker: NonNullable<Code>;

    const sequence: State = code => {
        if (code === marker) {
            effects.consume(code);
            return sequence;
        }

        effects.exit(blankLineSequenceType);
        return atBreak(code);
    };

    const atBreak: State = code => {
        if (code === marker) {
            effects.enter(blankLineSequenceType);
            return sequence(code);
        }

        if (markdownSpace(code)) {
            return factorySpace(effects, atBreak, types.whitespace)(code);
        }

        if (code !== codes.eof && !markdownLineEnding(code)) {
            return nok(code);
        }

        effects.exit(blankLineType);
        return ok(code);
    };

    const start: State = code => {
        assert(
            code === codes.caret,
            'expected `^`'
        );

        effects.enter(blankLineType);
        marker = code;
        return atBreak(code);
    };


    return start;
};

export const blankLine = {
    name: 'blankLine',
    tokenize: tokenizeBlankLine
};
