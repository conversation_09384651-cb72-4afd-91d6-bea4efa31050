import { Code, Token, Tokenizer } from 'micromark-util-types';
import { labelEnd } from 'micromark-core-commonmark';
import { types } from 'micromark-util-symbol/types';
import { codes } from 'micromark-util-symbol/codes';
import { constants } from 'micromark-util-symbol/constants';
import assert from 'assert';
import { normalizeIdentifier } from 'micromark-util-normalize-identifier';
import { factoryWhitespace } from 'micromark-factory-whitespace';
import { factoryDestination } from 'micromark-factory-destination';
import { markdownLineEndingOrSpace } from 'micromark-util-character';
import { factoryLabel } from 'micromark-factory-label';
import { factoryTitle } from 'micromark-factory-title';

const tokenizeResource: Tokenizer = function(effects, ok, nok) {

    function start(code: Code) {
        effects.enter(types.resource);
        effects.enter(types.resourceMarker);
        effects.consume(code);
        effects.exit(types.resourceMarker);
        return factoryWhitespace(effects, open);
    }

    function open(code: Code) {
        if (code === codes.rightParenthesis) {
            return end(code);
        }

        return factoryDestination(
            effects,
            destinationAfter,
            nok,
            types.resourceDestination,
            types.resourceDestinationLiteral,
            types.resourceDestinationLiteralMarker,
            types.resourceDestinationRaw,
            types.resourceDestinationString,
            constants.linkResourceDestinationBalanceMax
        )(code);
    }

    function destinationAfter(code: Code) {
        return markdownLineEndingOrSpace(code)
            ? factoryWhitespace(effects, between)(code)
            : end(code);
    }

    function between(code: Code) {
        if (
            code === codes.quotationMark ||
            code === codes.apostrophe ||
            code === codes.leftParenthesis
        ) {
            return factoryTitle(
                effects,
                factoryWhitespace(effects, size),
                nok,
                types.resourceTitle,
                types.resourceTitleMarker,
                types.resourceTitleString
            )(code);
        }

        return size(code);
    }

    function size(code: Code) {
        if (code === codes.equalsTo) {
            effects.enter('resourceSize');
            effects.enter('resourceSizeMarker');
            effects.consume(code);
            effects.exit('resourceSizeMarker');
            return startWith;
        }

        return end(code);
    }

    function startWith(code: Code) {
        if (code === codes.lowercaseX) {
            effects.enter('blockQuoteTypeMarker');
            effects.consume(code);
            effects.exit('blockQuoteTypeMarker');
            return startHeight;
        }
        if (code === codes.rightParenthesis || markdownLineEndingOrSpace(code)) {
            effects.exit('resourceSize');
            return factoryWhitespace(effects, end)(code);
        }
        effects.enter('resourceSizeWidth');
        effects.enter(types.chunkString, { contentType: 'string' });
        return width(code);
    }

    function width(code: Code) {
        if (code === codes.rightParenthesis || markdownLineEndingOrSpace(code)) {
            effects.exit(types.chunkString);
            effects.exit('resourceSizeWidth');
            effects.exit('resourceSize');
            return factoryWhitespace(effects, end)(code);
        }
        if (code === codes.lowercaseX) {
            effects.exit(types.chunkString);
            effects.exit('resourceSizeWidth');
            effects.enter('resourceSizeMarker');
            effects.consume(code);
            effects.exit('resourceSizeMarker');
            return startHeight;
        }
        effects.consume(code);
        return width;
    }

    function startHeight(code: Code) {
        if (code === codes.rightParenthesis || markdownLineEndingOrSpace(code)) {
            effects.exit('resourceSize');
            return factoryWhitespace(effects, end)(code);
        }
        effects.enter('resourceSizeHeight');
        effects.enter(types.chunkString, { contentType: 'string' });
        return height(code);
    }

    function height(code: Code) {
        if (code === codes.rightParenthesis || markdownLineEndingOrSpace(code)) {
            effects.exit(types.chunkString);
            effects.exit('resourceSizeHeight');
            effects.exit('resourceSize');
            return factoryWhitespace(effects, end)(code);
        }

        effects.consume(code);
        return height;
    }

    function end(code: Code) {
        if (code === codes.rightParenthesis) {
            effects.enter(types.resourceMarker);
            effects.consume(code);
            effects.exit(types.resourceMarker);
            effects.exit(types.resource);
            return ok;
        }

        return nok(code);
    }

    return start;
};

const tokenizeFullReference: Tokenizer = function(effects, ok, nok) {
    const self = this;

    function start(code: Code) {
        assert.strictEqual(code, codes.leftSquareBracket, 'expected left bracket');
        return factoryLabel.call(
            self,
            effects,
            afterLabel,
            nok,
            types.reference,
            types.referenceMarker,
            types.referenceString
        )(code);
    }

    function afterLabel(code: Code) {
        return self.parser.defined.includes(
            normalizeIdentifier(
                self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1)
            )
        )
            ? ok(code)
            : nok(code);
    }

    return start;
};

const tokenizeCollapsedReference: Tokenizer = function(effects, ok, nok) {

    function start(code: Code) {
        effects.enter(types.reference);
        effects.enter(types.referenceMarker);
        effects.consume(code);
        effects.exit(types.referenceMarker);
        return open;
    }

    function open(code: Code) {
        if (code === 93) {
            effects.enter(types.referenceMarker);
            effects.consume(code);
            effects.exit(types.referenceMarker);
            effects.exit(types.reference);
            return ok;
        }

        return nok(code);
    }

    return start;
};

const resourceConstruct = { tokenize: tokenizeResource };
const fullReferenceConstruct = { tokenize: tokenizeFullReference };
const collapsedReferenceConstruct = { tokenize: tokenizeCollapsedReference };

const tokenizeLabelEnd: Tokenizer = function(effects, ok, nok) {
    const self = this;
    let index = self.events.length;
    let labelStart: Token;
    let defined: boolean;

    // Find an opening.
    while (index--) {
        if (
            (self.events[index][1].type === types.labelImage ||
                self.events[index][1].type === types.labelLink) &&
            !self.events[index][1]._balanced
        ) {
            labelStart = self.events[index][1];
            break;
        }
    }

    function balanced(code: Code) {
        labelStart._balanced = true;
        return nok(code);
    }

    function afterLabelEnd(code: Code) {
        // Resource: `[asd](fgh)`.
        if (code === codes.leftParenthesis) {
            return effects.attempt(resourceConstruct, ok, defined ? ok : balanced)(code);
        }

        // Collapsed (`[asd][]`) or full (`[asd][fgh]`) reference?
        if (code === codes.leftSquareBracket) {
            return effects.attempt(
                fullReferenceConstruct,
                ok,
                defined ? effects.attempt(collapsedReferenceConstruct, ok, balanced) : balanced
            )(code);
        }

        // Shortcut reference: `[asd]`?
        return defined ? ok(code) : balanced(code);
    }

    return function(code) {
        assert(code === codes.rightSquareBracket, 'expected `]`');
        if (!labelStart) {
            return nok(code);
        }

        // It’s a balanced bracket, but contains a link.
        if (labelStart._inactive) return balanced(code);
        defined =
            self.parser.defined.indexOf(
                normalizeIdentifier(
                    self.sliceSerialize({ start: labelStart.end, end: self.now() })
                )
            ) > -1;
        effects.enter(types.labelEnd);
        effects.enter(types.labelMarker);
        effects.consume(code);
        effects.exit(types.labelMarker);
        effects.exit(types.labelEnd);
        return afterLabelEnd;
    };
};

export default {
    name: 'labelEnd',
    tokenize: tokenizeLabelEnd,
    resolveTo: labelEnd.resolveTo,
    resolveAll: labelEnd.resolveAll,
};
