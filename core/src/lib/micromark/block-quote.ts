import assert from 'assert';
import { blockQuote as defaultBlockQuote } from 'micromark-core-commonmark';
import { Code, Tokenizer } from 'micromark-util-types';
import { types } from 'micromark-util-symbol/types';
import { codes } from 'micromark-util-symbol/codes';
import { constants } from 'micromark-util-symbol/constants';
import { factorySpace } from 'micromark-factory-space';
import { markdownLineEndingOrSpace, markdownSpace, asciiAlpha } from 'micromark-util-character';

const tokenizeBlockQuoteStart: Tokenizer = function(effects, ok, nok) {
    const self = this;

    return start;

    function start(code: Code) {
        if (code === codes.greaterThan) {
            const state = self.containerState;

            assert(state, 'expected `containerState` to be defined in container');

            if (!state.open) {
                effects.enter(types.blockQuote, { _container: true });
                state.open = true;
            }

            effects.enter(types.blockQuotePrefix);
            effects.enter(types.blockQuoteMarker);
            effects.consume(code);
            effects.exit(types.blockQuoteMarker);

            return factorySpace(effects, startTypeMark, types.whitespace);
        }

        return nok(code);
    }

    function startTypeMark(code: Code) {
        if (code === codes.leftSquareBracket) {
            return effects.attempt(typeConstruct, after, after)(code);
        }

        return after(code);
    }


    function after(code: Code) {
        if (markdownSpace(code)) {
            effects.enter(types.blockQuotePrefixWhitespace);
            effects.consume(code);
            effects.exit(types.blockQuotePrefixWhitespace);
            effects.exit(types.blockQuotePrefix);
            return ok;
        }
        effects.exit(types.blockQuotePrefix);
        return ok(code);
    }
};

const tokenizeBlockQuoteContinuation: Tokenizer = function(effects, ok, nok) {
    return factorySpace(
        effects,
        effects.attempt(blockQuote, ok, nok),
        types.linePrefix,
        this.parser.constructs.disable.null.includes('codeIndented')
            ? undefined
            : constants.tabSize
    );
};

const tokenizeType: Tokenizer = function(effects, ok, nok) {
    function start(code: Code) {
        effects.enter('blockQuoteTypeMarker');
        effects.consume(code);
        effects.exit('blockQuoteTypeMarker');
        return startType;
    }

    function startType(code: Code) {
        if (code === codes.rightSquareBracket) {
            return nok(code);
        }
        effects.enter('blockQuoteType');
        effects.enter(types.chunkString, { contentType: 'string' });
        return type(code);
    }

    function type(code: Code) {
        if (code === codes.rightSquareBracket) {
            effects.exit(types.chunkString);
            effects.exit('blockQuoteType');
            effects.enter('blockQuoteTypeMarker');
            effects.consume(code);
            effects.exit('blockQuoteTypeMarker');
            return checkLink;
        }

        if (markdownLineEndingOrSpace(code) || !asciiAlpha(code)) {
            return nok(code);
        }

        effects.consume(code);
        return type;
    }

    function checkLink(code: Code) {
        if (code === codes.leftParenthesis) {
            return nok(code);
        }
        return ok(code);
    }

    return start;
};

const typeConstruct = {
    tokenize: tokenizeType,
    partial: true
};

export const blockQuote = {
    name: 'blockQuote',
    tokenize: tokenizeBlockQuoteStart,
    continuation: { tokenize: tokenizeBlockQuoteContinuation },
    exit: defaultBlockQuote.exit
};

export default blockQuote;
