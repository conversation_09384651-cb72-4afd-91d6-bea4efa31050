import { unified } from 'unified';
import parse from 'remark-parse';
import gfm from 'remark-gfm';
import breaks from 'remark-breaks';
import imageSize from './remark/image-size';
import align from './remark/align';
import blockquote from './remark/blockquote';
import blankline from './remark/blankline';
import toc from './remark/toc';
import x from './remark/x';
import frontmatter from 'remark-frontmatter';
import { Options as ToMarkdownOptions } from 'mdast-util-to-markdown';
import { Options as FromMarkdownOptions } from 'mdast-util-from-markdown';
import { Handlers } from 'mdast-util-to-hast';
import remarkDirective from 'remark-directive';
import { remarkHeadingId } from 'remark-custom-heading-id';

declare module 'unified' {

    interface Data {
        toMarkdownExtensions: NonNullable<ToMarkdownOptions['extensions']>;
        micromarkExtensions: NonNullable<FromMarkdownOptions['extensions']>;
        fromMarkdownExtensions: NonNullable<FromMarkdownOptions['mdastExtensions']>;
        toHastExtensions: Handlers[];

        [key: string]: unknown;
    }

    interface FrozenProcessor {
        data(): Data;

        data<K extends keyof Data>(key: K): Data[K];
    }

}

const processor = unified()
    .use(parse)
    .use(function() {
        const data = this.data();

        //mdast-util-from-markdown 参数
        data['micromarkExtensions'] = [];
        data['fromMarkdownExtensions'] = [];

        //
        data['toMarkdownExtensions'] = [];
        data['toHastExtensions'] = [];
    })
    .use(remarkDirective)
    .use(frontmatter, ['yaml'])
    .use(gfm)
    .use(breaks)
    .use(remarkHeadingId)
    .use(x)
    .use(imageSize)
    .use(align)
    .use(blockquote)
    .use(blankline)
    .use(toc)
;

export default processor;
