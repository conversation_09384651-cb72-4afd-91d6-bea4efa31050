import { SummaryPartShape } from '../entities/summary-part';
import { SummaryArticleShape } from '../entities/summary-article';
import { select } from 'unist-util-select';
import { Node } from 'unist';
import markdown from './markdown';
import toString from './unist/unist-util-to-string';
import { Heading, Link, List } from 'mdast';
import { is } from 'unist-util-is';

function listArticles(list: List): SummaryArticleShape[] {

    const articles: SummaryArticleShape[] = [];

    for (const item of list.children) {

        const children = item.children;

        const link = select('paragraph:first-child>*', item) as Link | null;

        if (!link) {
            continue;
        }

        const title = toString(link);
        if (!title) {
            continue;
        }
        let ref = '';
        if (link.url) {
            ref = decodeURI(link.url);

            if (ref.match(/[\\:*?"<>|]/)) {
                ref = '';
            }
        }

        let article: SummaryArticleShape = {
            title,
            ref
        };

        if (children.length > 1) {
            const subList: Node = children[children.length - 1];
            if (is<List>(subList, 'list')) {
                article.children = listArticles(subList);
            }
        }
        articles.push(article);
    }
    return articles;
}

const md = markdown();

export default function parseSummary(content: string) {

    const root = md.parse(content);

    const parts: SummaryPartShape[] = [];
    let pendingPart: SummaryPartShape | undefined;

    for (const node of root.children as Node[]) {
        if (is<Heading>(node, 'heading') && [2, 3].includes(node.depth)) {
            if (pendingPart) {
                parts.push(pendingPart);
            }

            pendingPart = {
                title: toString(node).trim()
            };
        }

        if (is<List>(node, 'list')) {
            const articles = listArticles(node);
            if (pendingPart) {
                pendingPart.articles = (pendingPart.articles || []).concat(articles);
            } else {
                pendingPart = {
                    title: '',
                    articles
                };
            }
        }
    }

    if (pendingPart) {
        parts.push(pendingPart);
    } else {
        parts.push({
            title: ''
        });
    }

    return parts;
}
