interface NodeCanvasRenderingContext2D extends Pick<
    CanvasRenderingContext2D,
    'beginPath' | 'arc' | 'lineTo' | 'closePath' | 'fillStyle' | 'restore' | 'font' | 'textAlign' | 'fillText'
> {
    fill(): void;
}

interface CanvasType {
    getContext(contextId: '2d'): NodeCanvasRenderingContext2D | null;

    toDataURL(): string;
}

function createDefaultCanvas(size: number): CanvasType {
    const canvas = window.document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    return canvas;
}

export default class LetterAvatar {

    private flatColors: string[] = [
        '#1abc9c',
        '#2ecc71',
        '#3498db',
        '#9b59b6',
        '#34495e',
        '#16a085',
        '#27ae60',
        '#2980b9',
        '#8e44ad',
        '#2c3e50',
        '#f1c40f',
        '#e67e22',
        '#e74c3c',
        '#f39c12',
        '#d35400',
        '#c0392b',
    ];

    private readonly letter: string;

    private readonly size: number;

    private readonly canvas: CanvasType;

    constructor(letter: string, size: number, canvas: CanvasType) {
        this.letter = letter;
        this.size = size;
        this.canvas = canvas;

        this.generate();
    }

    toDataUrl() {
        return this.canvas.toDataURL();
    }

    private generate() {
        const context = this.canvas.getContext('2d');
        if (!context) {
            throw new Error('getContext failed');
        }

        //绘制圆角
        const radius = this.size / 10;
        context.beginPath();
        context.arc(this.size - radius, this.size - radius, radius, 0, Math.PI / 2);
        context.lineTo(radius, this.size);
        context.arc(radius, this.size - radius, radius, Math.PI / 2, Math.PI);
        context.lineTo(0, radius);
        context.arc(radius, radius, radius, Math.PI, Math.PI * 3 / 2);
        context.lineTo(this.size - radius, 0);
        context.arc(this.size - radius, radius, radius, Math.PI * 3 / 2, Math.PI * 2);
        context.lineTo(this.size, this.size - radius);
        context.closePath();

        if (this.letter.length > 0) {
            const text = this.letter[0].toUpperCase();
            context.fillStyle = this.flatColors[text.charCodeAt(0) % this.flatColors.length];
            context.fill();
            context.restore();

            context.font = Math.round(this.size / 2) + 'px Roboto';
            context.textAlign = 'center';
            context.fillStyle = '#FFFFFF';
            context.fillText(this.letter[0].toUpperCase(), this.size / 2, this.size / 1.5);
        } else {
            context.fillStyle = this.flatColors[0];
            context.fill();
        }
    }

    public static create(letter: string, size: number, canvas?: CanvasType) {
        if (!canvas) {
            canvas = createDefaultCanvas(size);
        }

        return new LetterAvatar(letter, size, canvas);
    }
}
