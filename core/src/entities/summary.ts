import { isEmpty, isFunction } from 'lodash';
import parseSummary from '../lib/parse-summary';
import Level from './level';
import SummaryArticle, { SummaryArticleShape } from './summary-article';
import SummaryPart, { SummaryPartShape } from './summary-part';

export default class Summary {

    static file = {
        json: 'SUMMARY.json',
        markdown: 'SUMMARY.md'
    };

    parts: SummaryPart[];
    json: boolean;

    constructor(parts: SummaryPart[] = [], json = false) {
        this.parts = parts;
        this.json = json;
        this.indexLevels();
    }

    insertPart(part: SummaryPart | SummaryPartShape, level?: Level) {
        if (!(part instanceof SummaryPart)) {
            part = SummaryPart.createFromObject(part);
        }

        if (this.isEmpty()) {
            this.parts = [part as SummaryPart];
        } else {
            const index = level ? level.getLeafIndex() : this.parts.length;
            this.parts.splice(index, 0, part as SummaryPart);
        }

        this.indexLevels();
    }

    updatePart(level: Level, data: SummaryPartShape | ((part: SummaryPart) => void)) {
        const part = this.getByLevel(level);
        if (part instanceof SummaryPart) {
            if (isFunction(data)) {
                data(part);
            } else {
                part.title = data.title;
                part.metadata = data.metadata;
            }
        }
    };

    removePart(level: Level) {
        const index = level.getLeafIndex();
        this.parts.splice(index, 1);

        if (this.parts.length === 0) {
            this.parts = [new SummaryPart('')];
        }
        this.indexLevels();
    }

    insertArticle(article: SummaryArticle | SummaryArticleShape, level: Level) {
        if (!(article instanceof SummaryArticle)) {
            article = SummaryArticle.createFromObject(article);
        }

        const parent = this.getParent(level);

        if (!parent) {
            return;
        }

        const articles = parent.getArticles();

        const index = level.getLeafIndex();

        articles.splice(index, 0, article as SummaryArticle);

        parent.indexLevels();
    };

    updateArticle(level: Level, data: SummaryArticleShape | ((article: SummaryArticle) => void)) {
        const article = this.getByLevel(level);
        if (article instanceof SummaryArticle) {
            if (isFunction(data)) {
                data(article);
            } else {
                article.title = data.title;
                article.ref = data.ref;
                article.metadata = data.metadata;
            }

        }
    };

    removeArticle(level: Level) {
        const parent = this.getParent(level);
        if (!parent) {
            return;
        }
        const articles = parent.getArticles();
        const index = level.getLeafIndex();
        articles.splice(index, 1);
        parent.indexLevels();
    };

    toText() {
        let text = '';
        if (this.parts.length === 1 && !this.parts[0].title) {
            text += this.parts[0].toText();
        } else {
            for (let part of this.parts) {
                text += `## ${part.title}`;
                text += `\n`;
                text += part.toText();
                text += '\n';
            }
        }
        return text;
    };

    hasMetadata() {
        let find = false;
        this.getArticle(
            article => {
                if (find) {
                    return true;
                }
                if (!isEmpty(article.metadata)) {
                    find = true;
                    return true;
                }
                return false;
            },
            part => {
                if (!isEmpty(part.metadata)) {
                    find = true;
                }
                return true;
            }
        );
        return find;
    }

    isJson() {
        return this.json;
    }

    getParts() {
        return this.parts;
    }

    getPart(i: number) {
        return this.parts[i];
    }

    getLastPart() {
        return this.parts[this.parts.length - 1];
    }

    getArticles() {
        const articles: SummaryArticle[] = [];
        this.getArticle((article) => {
            articles.push(article);
            return false;
        });

        return articles;
    }

    getArticle(articleIter: (article: SummaryArticle) => boolean, partIter?: (part: SummaryPart) => boolean) {
        return this.parts.reduce<SummaryArticle | undefined>((result, part) => {
            if (result) return result;

            if (partIter && !partIter(part)) {
                return undefined;
            }

            return SummaryArticle.findArticle(part, articleIter);
        }, undefined);
    }

    getArticlePart(article: SummaryArticle): SummaryPart {
        const root = article.getLevel().getRoot();
        return this.getByLevel(root) as SummaryPart;
    }

    getFirstPath(): string | undefined {
        const firstArticle = this.getArticle((article) => !!article.getPath());
        if (firstArticle) {
            return firstArticle.getPath();
        }
    }

    getByLevel(level: Level) {
        const iterByLevel = (article: SummaryArticle | SummaryPart) => article.getLevel().eq(level);

        return this.parts.reduce<SummaryArticle | SummaryPart | undefined>((result, part) => {
            if (result) return result;

            if (iterByLevel(part)) {
                return part;
            }

            return SummaryArticle.findArticle(part, iterByLevel);
        }, undefined);

    }

    getFirstArticle() {
        return this.getArticle(() => true);
    }

    getNextArticle(current: string | SummaryArticle): SummaryArticle | undefined {
        const level = current instanceof SummaryArticle ? current.getLevel() : current;
        let wasPrev = false;

        return this.getArticle((article) => {
            if (wasPrev) return true;

            wasPrev = article.getLevel() == level;
            return false;
        });
    }

    getPrevArticle(current: string | SummaryArticle): SummaryArticle | undefined {
        const level = current instanceof SummaryArticle ? current.getLevel() : current;
        let prev = undefined;

        this.getArticle((article) => {
            if (article.getLevel() == level) {
                return true;
            }

            prev = article;
            return false;
        });

        return prev;
    }

    getParent(current: Level | SummaryArticle) {
        const level = current instanceof SummaryArticle ? current.getLevel() : current;

        const parentLevel = level.getParent();
        if (!parentLevel) {
            return undefined;
        }

        return this.getByLevel(parentLevel);
    }

    indexLevels() {
        for (const [index, part] of this.parts.entries()) {
            const baseLevel = new Level(String(index + 1));
            part.indexLevels(baseLevel);
        }
    }

    isSinglePart() {
        return this.parts.length === 1 && !this.parts[0].title;
    }

    isEmpty() {
        return this.isSinglePart() ? this.parts[0].articles.length === 0 : this.parts.length === 0;
    }

    toObject() {
        return this.parts.map(function(part) {
            return part.toObject();
        });
    }

    static createFromObject(parts: SummaryPartShape[]) {
        return new Summary(parts.map((part) => {
            return SummaryPart.createFromObject(part);
        }), true);
    }

    static createFromText(text: string) {
        const parts = parseSummary(text);
        return new Summary(parts.map((part) => {
            return SummaryPart.createFromObject(part);
        }));
    }

    static create(summary: string | SummaryPartShape[]) {
        return typeof summary === 'object' ? Summary.createFromObject(summary) : Summary.createFromText(summary);
    }
}
