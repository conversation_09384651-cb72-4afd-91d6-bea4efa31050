import Book from './book';
import request from 'axios';

export interface PluginMeta {
    type: 'theme' | 'extension';
    host: string;
    name: string;
    display_name: string;
    description: string;
    version: string;
    builtin: boolean;
    preset?: boolean;
    ebook?: boolean;
    main: string;
    icon: string;
    price: number;
    disabled: boolean;
    expired: string;
}

interface Paginator<T> {
    current_page: number;
    last_page: number;
    per_page: number;
    data: T[];
}

interface PluginTrial {
    expire_time: string;
}

export interface PluginCenterConfig {
    host: string;
    preset?: Record<string, object>;
}

export class PluginCenter {
    protected host: string;
    protected preset?: Record<string, object>;

    constructor({ host, preset }: PluginCenterConfig) {
        this.host = host;
        this.preset = preset;
    }

    getBuyUrl(book: Book, plugin: PluginMeta) {
        return `${this.host}/api/buy?book=${book.id}&plugin=${plugin.name}`;
    }

    async bought(book: Book, plugin: PluginMeta) {
        try {
            await request.get(`${this.host}/api/bought?book=${book.id}&plugin=${plugin.name}`);
            return true;
        } catch {
            return false;
        }
    }

    async trial(book: Book, plugin: PluginMeta) {
        try {
            const { data } = await request.get<PluginTrial>(`${this.host}/api/trial?book=${book.id}&plugin=${plugin.name}`);
            return data;
        } catch {
            return false;
        }
    }

    async queryPlugins({ page = 1, ...params }: { page?: number, type?: string, keyword?: string }) {
        const { data } = await request.get<Paginator<PluginMeta>>(`${this.host}/api/query`, {
            params: {
                ...params,
                page,
            }
        });
        return data;
    }

    async getInstalled(book: Book): Promise<PluginMeta[]> {
        const { config } = book;

        const theme = config.getValue('theme');

        const plugins = [
            `theme-${theme}`,
            ...config.getValue('plugins'),
        ];

        if (this.preset) {
            plugins.push(...Object.keys(this.preset));
            config.setPresetPlugins(this.preset);
        }

        const { data } = await request.get<PluginMeta[]>(`${this.host}/api/installed`, {
            params: {
                book: book.id,
                plugins: plugins.join(',')
            }
        }).catch((e) => {
            throw new Error('Plugins Check Failed:' + e.message);
        });

        //过滤非正常安装的内置插件
        return data.flatMap(meta => {
            if (!meta.builtin || this.preset?.[meta.name]) {
                meta.preset = !!this.preset?.[meta.name];

                return [meta];
            }
            return [];
        });
    }
}

export let pluginCenter: PluginCenter;

export function createPluginCenter(config: PluginCenterConfig) {
    return pluginCenter = new PluginCenter(config);
}
