import { get, isEmpty, repeat } from 'lodash';
import Level from './level';
import SummaryArticle, { SummaryArticleShape } from './summary-article';

export interface SummaryPartShape {
    title: string;
    articles?: SummaryArticleShape[];
    metadata?: Record<string, string>;
}

export default class SummaryPart {

    level: Level;
    title: string;
    articles: SummaryArticle[];
    metadata?: Record<string, any>;

    constructor(title: string, articles: SummaryArticle[] = [], metadata?: Record<string, string>) {
        this.level = new Level();
        this.title = title;
        this.articles = articles;
        this.metadata = metadata;
    }

    getMetadata<T = any>(name: string, defaultValue?: T): T | undefined {
        if (!this.metadata) {
            return defaultValue;
        }
        return get(this.metadata, name, defaultValue);
    }

    getLevel() {
        return this.level;
    }

    getTitle() {
        return this.title;
    }

    getArticles() {
        return this.articles;
    }

    getFirstArticle() {
        return SummaryArticle.findArticle(this, () => true);
    }

    getDescendants() {
        const articles: SummaryArticle[] = [];
        SummaryArticle.findArticle(this, (article) => {
            articles.push(article);
            return false;
        });

        return articles;
    }

    createChildLevel() {
        return this.level.createChild(this.articles.length + 1);
    }

    indexLevels(baseLevel?: Level) {
        if (baseLevel) {
            this.level = baseLevel;
        } else {
            baseLevel = this.level;
        }
        for (const [index, article] of this.articles.entries()) {
            article.indexLevels(baseLevel.createChild(index + 1));
        }
    }

    toText() {
        let text = '';
        SummaryArticle.findArticle(this, (article: SummaryArticle) => {
            const line = article.ref ? `[${article.title}](${article.ref.replace(/\s/g, '%20')})` : article.title;
            text += `${repeat('    ', article.getDepth() - 1)}* ${line}\n`;
            return false;
        });
        return text;
    }

    toObject(): SummaryPartShape {
        const articles = this.articles.map(function(article) {
            return article.toObject();
        });

        return {
            title: this.title,
            metadata: this.metadata,
            articles: isEmpty(articles) ? undefined : articles
        };
    }

    static createFromObject(part: SummaryPartShape) {
        const articles = part.articles?.filter(article => !!article.title)
                             .map((article) => {
                                 return SummaryArticle.createFromObject(article);
                             });
        return new SummaryPart(part.title, articles, part.metadata);
    }
}
