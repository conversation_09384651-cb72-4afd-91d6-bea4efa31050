import SummaryPart from './summary-part';
import Level from './level';
import { posix } from 'path';
import { get, isEmpty, set } from 'lodash';

export interface SummaryArticleShape {
    title: string;
    ref: string;
    path?: string;
    children?: SummaryArticleShape[];
    metadata?: Record<string, string>;
}

export default class SummaryArticle {

    level: Level;
    title: string;
    ref: string;
    path: string;
    children: SummaryArticle[];
    metadata?: Record<string, any>;

    hasPath = false;

    constructor({
        title,
        ref = '',
        path,
        children = [],
        metadata
    }: Omit<SummaryArticleShape, 'children'> & { children?: SummaryArticle[] }) {
        this.title = title;
        this.ref = ref;

        if (!path) {
            if (ref) {
                this.path = posix.format({
                    ...posix.parse(ref),
                    ext: '.html',
                    base: ''
                });
            } else {
                this.path = '';
            }
        } else {
            this.hasPath = true;
            this.path = path;
        }

        this.children = children;
        this.level = new Level();
        this.metadata = metadata;
    }

    getMetadata<T = any>(name: string, defaultValue?: T): T | undefined {
        if (!this.metadata) {
            return defaultValue;
        }
        return get(this.metadata, name, defaultValue);
    }

    setMetadata(name: string, value: any) {
        if (!this.metadata) {
            this.metadata = {};
        }
        set(this.metadata, name, value);
    }

    getLevel() {
        return this.level;
    }

    getTitle() {
        return this.title;
    }

    getRef() {
        return this.ref;
    }

    getPath() {
        return this.path;
    }

    getArticles() {
        return this.children;
    }

    getDescendants() {
        const articles: SummaryArticle[] = [];
        SummaryArticle.findArticle(this, (article) => {
            articles.push(article);
            return false;
        });

        return articles;
    }

    getDepth() {
        return this.getLevel().getDepth();
    }

    createChildLevel() {
        return this.level.createChild(this.children.length + 1);
    }

    indexLevels(baseLevel?: Level) {
        if (baseLevel) {
            this.level = baseLevel;
        } else {
            baseLevel = this.level;
        }
        for (const [index, article] of this.children.entries()) {
            article.indexLevels(baseLevel.createChild(index + 1));
        }
    }

    toObject(): SummaryArticleShape {
        const children = this.children.map(function(article) {
            return article.toObject();
        });

        return {
            title: this.title,
            ref: this.ref,
            path: this.hasPath ? this.path : undefined,
            metadata: this.metadata,
            children: isEmpty(children) ? undefined : children
        };
    }


    static findArticle(base: SummaryArticle | SummaryPart, iter: (article: SummaryArticle) => boolean): SummaryArticle | undefined {
        const articles = base.getArticles();

        return articles.reduce<SummaryArticle | undefined>((result, article) => {
            if (result) return result;

            if (iter(article)) {
                return article;
            }

            return SummaryArticle.findArticle(article, iter);
        }, undefined);
    }

    static createFromObject(article: SummaryArticleShape): SummaryArticle {
        const children = article.children?.filter(article => !!article.title)
                                .map((article) => {
                                    return SummaryArticle.createFromObject(article);
                                });

        return new SummaryArticle({
            ...article,
            children
        });
    }
}
