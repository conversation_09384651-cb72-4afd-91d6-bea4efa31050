import { get, set } from 'lodash';
import Summary from './summary';
import Config from './config';

export default class Book {

    static style = '.topwrite/style.css';
    static logo = 'logo.png';
    static readme = 'README.md';

    readonly id: string;

    sha?: string;
    metadata?: Record<string, any>;

    summary: Summary;
    protected _config!: Config;

    constructor(id: string, config: Config, summary: Summary, metadata?: Record<string, any>, sha?: string) {
        this.id = id;
        this.summary = summary;
        this.config = config;
        this.metadata = metadata;
        this.sha = sha;
    }

    getMetadata<T = any>(name: string, defaultValue?: T): T | undefined {
        if (!this.metadata) {
            return defaultValue;
        }
        return get(this.metadata, name, defaultValue);
    }

    setMetadata(name: string, value: any) {
        if (!this.metadata) {
            this.metadata = {};
        }
        set(this.metadata, name, value);
    }

    get config() {
        return this._config;
    }

    set config(config) {
        config.setKey(`${this.id}:config`);
        this._config = config;
    }
}
