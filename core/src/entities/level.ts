export default class Level {

    data: number[];

    constructor(level: string | number[] = []) {
        this.data = typeof level === 'string' ? level.split('.').map((char) => {
            return parseInt(char, 10);
        }) : level;
    }

    getRoot() {
        return new Level(this.data.slice(0, 1));
    }

    getParent() {
        return new Level(this.data.slice(0, -1));
    }

    createChild(level: number) {
        return new Level(this.data.concat(level));
    }

    isDescendant(level: Level) {
        return level.toString().startsWith(`${this.toString()}.`);
    }

    getNext() {
        const level = [...this.data];
        level[level.length - 1]++;
        return new Level(level);
    }

    getDepth() {
        return this.data.length - 1;
    }

    eq(level: Level) {
        return this.toString() === level.toString();
    }

    getLeafIndex() {
        return this.data[this.data.length - 1] - 1;
    }

    toString(base?: Level) {
        if (base) {
            return this.data.slice(base.data.length).join('.');
        }
        return this.data.join('.');
    }

    static compare(level1: Level, level2: Level) {
        for (let i = 0; i < Math.max(level1.data.length, level2.data.length); i++) {
            let n1 = level1.data[i] || 0;
            let n2 = level2.data[i] || 0;

            if (n1 > n2) return 1;
            if (n2 > n1) return -1;
        }

        return 0;
    }

}
