import path from 'path';
import { get } from 'lodash';

interface FileMeta {
    title?: string;
    description?: string;
    keywords?: string;
    mtime?: string;

    [index: string]: any;
}

export default class File {

    path: string;
    content: string;
    meta: FileMeta;

    constructor({ path, content, meta = {} }: { path: string, content: string, meta?: FileMeta }) {
        this.path = path;
        this.content = content;
        this.meta = meta;
    }

    getMeta<T extends keyof FileMeta>(name: T, defaultValue?: FileMeta[T]): FileMeta[T] {
        return get(this.meta, name, defaultValue);
    }

    relative(to: string) {
        const isDirectory = to.slice(-1) === '/';

        to = path.posix.relative(
            path.posix.dirname(this.path),
            to
        ) || '.';

        return to + (isDirectory ? '/' : '');
    }

    resolve(to: string) {
        return path.posix.join(path.posix.dirname(this.path), to);
    }
}
