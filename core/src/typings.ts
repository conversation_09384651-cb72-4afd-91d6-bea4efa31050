import {
    TextDirective as UTextDirective,
    LeafDirective as ULeafDirective,
    ContainerDirective as UContainerDirective
} from 'mdast-util-directive';
import type { Node } from 'unist';

declare module 'mdast' {
    export type TextDirective = UTextDirective;
    export type LeafDirective = ULeafDirective;
    export type ContainerDirective = UContainerDirective;

    export interface Paragraph {
        align?: AlignType;
    }

    export interface BlankLine extends Node {
        type: 'blankLine';
    }

    export interface Heading {
        align?: AlignType;
    }

    export interface Blockquote {
        variant?: string;
    }

    export interface Image {
        width?: string | number;
        height?: string | number;
        title?: string | undefined;
    }

    export interface XBlock extends Literal {
        type: 'xBlock';
        name: string;
        parameter?: string;
    }

    export interface XInline extends Literal {
        type: 'xInline';
        name: string;
    }

    export interface StaticPhrasingContentMap {
        xinline: XInline;
    }

    export interface BlockContentMap {
        xblock: XBlock;
        blankline: BlankLine;
    }
}

declare module 'json-schema' {
    export interface JSONSchema7 {
        encrypt?: boolean;
    }
}
export {};
