const webpack             = require("webpack");
const path                = require("path");
const WebpackConfigPlugin = require("@topthink/webpack-config-plugin");

module.exports = async env => {
    const isDevelopment = !!env.dev;
    const isServer      = !!env.WEBPACK_SERVE;

    const host = env.prod
        ? "https://sandbox.x.topthink.com"
        : "http://sandbox.x.topthink.org";

    let id = "sandbox:dev";

    return {
        devtool  : isDevelopment ? "cheap-module-source-map" : "source-map",
        mode     : isDevelopment ? "development" : "production",
        entry    : {
            index: {
                import : "./src/index.ts",
                library: {
                    name: "TopWrite",
                    type: "window"
                }
            }
        },
        output   : {
            filename     : "[name].js",
            chunkFilename: "[id]-[contenthash:6].js",
            path         : path.resolve(__dirname, "dist")
        },
        externals: {
            react             : "React",
            "react-dom"       : "ReactDOM",
            "react-dom/client": "ReactDOM"
        },
        resolve  : {
            fallback: {
                url: require.resolve("url/")
            }
        },
        plugins  : [
            new WebpackConfigPlugin({
                serve: isServer,
                html : isServer
                    ? {
                        filename     : "index.html",
                        chunks       : ["index"],
                        template     : "public/index.ejs",
                        inject       : false,
                        scriptLoading: "blocking",
                        publicPath   : "/"
                    }
                    : false
            }),
            new webpack.ProvidePlugin({
                process: require.resolve("process/browser")
            })
        ],
        devServer: {
            hot               : "only",
            client            : {
                webSocketURL: {
                    hostname: "localhost"
                }
            },
            allowedHosts      : "all",
            historyApiFallback: {
                disableDotRule: true
            },
            devMiddleware     : {
                index: ""
            },
            proxy             : {
                context     : (path, req) => {
                    if (path === "/") {
                        path = "/index.html";
                    }
                    return (
                        req.headers.accept.indexOf("application/json") > -1 ||
                        !path.endsWith(".html")
                    );
                },
                target      : `${host}/preview`,
                changeOrigin: true
            }
        }
    };
};
