{"compilerOptions": {"target": "esnext", "module": "esnext", "lib": ["dom", "es2017"], "jsx": "react-jsx", "declaration": true, "declarationDir": "./types", "sourceMap": true, "removeComments": false, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "moduleResolution": "node", "noImplicitAny": false, "baseUrl": "./", "esModuleInterop": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "resolveJsonModule": true}, "include": ["./src/**/*"]}