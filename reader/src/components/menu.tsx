import { InjectedComponentSet, styled, useIntl } from '@topwrite/common';
import { ComponentProps, ReactNode, useCallback } from 'react';
import useWindowScroll from '../lib/use-window-scroll';
import { ReactComponent as GotopIcon } from '../images/gotop.svg';
import Tooltip from 'rc-tooltip';
import 'rc-tooltip/assets/bootstrap.css';
import useWindowSize from '../lib/use-window-size';

type TooltipProps = ComponentProps<typeof Tooltip>;

export interface MenuItemProps {
    tooltip?: TooltipProps['overlay'] | TooltipProps;
    children: ReactNode;
    onClick?: () => void;
}

const MenuItem = function({ tooltip, children, onClick }: MenuItemProps) {
    const item = <a onClick={onClick}>{children}</a>;

    if (tooltip) {
        const defaultProps: Partial<TooltipProps> = {
            placement: 'left',
            getTooltipContainer: (dom) => {
                return dom.parentElement || document.body;
            }
        };

        let props: TooltipProps;
        if (typeof tooltip === 'object' && tooltip.hasOwnProperty('overlay')) {
            props = tooltip as TooltipProps;
        } else {
            props = {
                overlay: tooltip as TooltipProps['overlay']
            };
        }

        return <Tooltip
            {...defaultProps}
            {...props}
        >
            {item}
        </Tooltip>;
    }

    return item;
};


export interface MenuProps {
    className?: string;
}

export default function Menu({ className }: MenuProps) {

    const size = useWindowSize();

    if (size === 'mobile') {
        return null;
    }

    return <Container className={className}>
        <GotopItem />
        <InjectedComponentSet role={'page:menu:item'} component={MenuItem} />
    </Container>;
}

const Container = styled.div`

  position: fixed;
  right: 24px;
  bottom: 48px;
  z-index: 400;
  display: flex;
  flex-direction: column-reverse;

  a {
    width: 42px;
    height: 42px;
    background: #FFFFFF;
    box-shadow: 0 1px 4px -2px rgb(0 0 0 / 13%), 0 2px 8px rgb(0 0 0 / 8%), 0 8px 16px 4px rgb(0 0 0 / 4%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    cursor: pointer;
    margin-top: 16px;
    color: #585a5a;

    svg {
      width: 20px;
      height: 20px;
      fill: currentColor;
    }

    html[data-theme=dark] & {
      background-color: #1a1a1a;
      color: #e8e8e8;
      border: 1px solid rgba(235, 235, 235, 0.15);
      box-shadow: 0 6px 12px 0 rgba(0, 0, 0, 0.24);
    }
  }

`;

const GotopItem = function() {

    const intl = useIntl();

    const scrollUp = useCallback(() => {
        const frame = document.documentElement;
        let pageTop = frame.scrollTop - (frame.clientTop || 0);

        if (pageTop > 0) {
            window.scrollTo(0, pageTop - 30);
            setTimeout(scrollUp, 2);
        }
    }, []);

    const scrollTop = useWindowScroll();

    if (scrollTop > 500) {
        return <MenuItem
            tooltip={intl.formatMessage(
                { defaultMessage: `回到顶部` }
            )}
            onClick={scrollUp}
        ><GotopIcon /></MenuItem>;
    }
    return null;
};
