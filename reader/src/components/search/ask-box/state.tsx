import { Markdown, styled, useSelector } from '@topwrite/common';
import { StartScreen } from './screens/start-screen';
import { ErrorScreen } from '../search-box/screens/error-screen';
import { LoadingIcon } from '../icons';
import { useMemo } from 'react';
import Anchor from '../../anchor';

interface Props {
    onClose(): void;
}

export default function State({ onClose }: Props) {
    const { status, query, answer } = useSelector('ask');

    const components = useMemo(() => {
        return {
            a: Anchor
        };
    }, []);

    if (status === 'error') {
        return <ErrorScreen />;
    }

    if (!query) {
        return <StartScreen />;
    }

    return <div className='DocSearch-Dropdown-Container'>
        <Answer onClick={(e) => {
            if (e.target instanceof HTMLAnchorElement) {
                onClose();
            }
        }}>
            <h2>{query}</h2>
            {status === 'loading' ?
                <Loading><LoadingIcon /></Loading> :
                <Markdown
                    aside={false}
                    inject={false}
                    components={components}
                >
                    {answer}
                </Markdown>}
        </Answer>
    </div>;
}

const Loading = styled.div`
  align-items: center;
  color: var(--docsearch-highlight-color);
  display: flex;
  justify-content: center;

  svg {
    height: 24px;
    width: 24px
  }
`;

const Answer = styled.div`
  margin-top: 16px;
  margin-bottom: 16px;
  background: var(--docsearch-hit-background);
  padding: 16px;
  box-shadow: var(--docsearch-hit-shadow);
  border-radius: 4px;

  h2 {
    padding-bottom: .4em;
    font-size: 1.3em;
    line-height: 1.2;
    border-bottom: 1px solid #eeeeee;
    margin-top: 0;
    margin-bottom: 1em;
  }
`;
