import { FormEvent, useCallback, useEffect, useRef } from 'react';
import { useIntl, useModel } from '@topwrite/common';
import { AskIcon, LoadingIcon } from '../icons';
import { MAX_QUERY_SIZE } from '../constants';

interface Props {
    onClose(): void;
}

export default function Form({ onClose }: Props) {
    const [{ status }, { fetch }] = useModel('ask');
    const intl = useIntl();
    const inputRef = useRef<HTMLInputElement | null>(null);

    useEffect(() => {
        if (inputRef.current) {
            inputRef.current.focus();
        }
    }, []);

    const handleAsk = useCallback((event: FormEvent) => {
        event.preventDefault();
        if (inputRef.current) {
            fetch(inputRef.current.value);
            inputRef.current.value = '';
        }
    }, [inputRef, fetch]);

    return <header className='DocSearch-SearchBar'>
        <form
            className={[
                'DocSearch-Form',
                status === 'loading' || status === 'stalled' && 'DocSearch-Container--Stalled',
            ].filter(Boolean).join(' ')}
            onSubmit={handleAsk}
        >
            <label className='DocSearch-MagnifierLabel'>
                <AskIcon />
            </label>
            <div className='DocSearch-LoadingIndicator'>
                <LoadingIcon />
            </div>
            <input
                placeholder={intl.$t({ defaultMessage: '问一个问题...' })}
                className='DocSearch-Input'
                ref={inputRef}
                autoFocus
                enterKeyHint='send'
                disabled={status === 'loading' || status === 'stalled'}
                maxLength={MAX_QUERY_SIZE}
            />
        </form>
        <button className='DocSearch-Cancel' onClick={onClose}>
            {intl.$t({ defaultMessage: '取消' })}
        </button>
    </header>;
}
