import Form from './form';
import State from './state';
import { useEffect, useRef } from 'react';
import { styled, useSelector } from '@topwrite/common';

interface AskBoxProps {
    onClose(): void;
}

export default function AskBox({ onClose }: AskBoxProps) {

    const { query, status, answer } = useSelector('ask');
    const dropdownRef = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
        if (dropdownRef.current) {
            dropdownRef.current.scrollTop = 0;
        }
    }, [query]);

    useEffect(() => {
        if (dropdownRef.current && status === 'stalled') {
            dropdownRef.current.scrollTop = dropdownRef.current.scrollHeight;
        }
    }, [answer, status]);

    return <>
        <Form onClose={onClose} />
        <Container ref={dropdownRef}>
            <State onClose={onClose} />
        </Container>
    </>;
}

const Container = styled.div`
  max-height: calc(var(--docsearch-modal-height) - var(--docsearch-searchbox-height) - var(--docsearch-spacing) - var(--docsearch-footer-height));
  min-height: var(--docsearch-spacing);
  overflow-y: auto;
  padding: 0 var(--docsearch-spacing);
  scrollbar-color: var(--docsearch-muted-color) var(--docsearch-modal-background);
  scrollbar-width: thin;

  @media (max-width: 768px) {
    max-height: calc(var(--docsearch-vh, 1vh) * 100 - var(--docsearch-searchbox-height) - var(--docsearch-spacing) - var(--docsearch-footer-height));
  }
  @media (max-width: 768px) {
    height: 100%;
  }
`;
