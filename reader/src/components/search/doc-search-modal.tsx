import { useEffect, useRef } from 'react';
import { Footer } from './footer';
import SearchBox from './search-box';
import { useTrapFocus } from './hooks/use-trap-focus';
import { styled } from '@topwrite/common';
import AskBox from './ask-box';
import { useMode } from './mode-provider';

export interface DocSearchModalProps {
    onClose(): void;
}

export function DocSearchModal({ onClose }: DocSearchModalProps) {
    const [mode] = useMode();
    const containerRef = useRef<HTMLDivElement | null>(null);
    const modalRef = useRef<HTMLDivElement | null>(null);

    useTrapFocus({ container: containerRef.current });

    useEffect(() => {
        document.body.classList.add('DocSearch--active');

        return () => {
            document.body.classList.remove('DocSearch--active');
        };
    }, []);

    useEffect(() => {
        function setFullViewportHeight() {
            if (modalRef.current) {
                const vh = window.innerHeight * 0.01;
                modalRef.current.style.setProperty('--docsearch-vh', `${vh}px`);
            }
        }

        setFullViewportHeight();

        window.addEventListener('resize', setFullViewportHeight);

        return () => {
            window.removeEventListener('resize', setFullViewportHeight);
        };
    }, []);

    return <Container
        ref={containerRef}
        className='DocSearch DocSearch-Container'
        onMouseDown={(event) => {
            if (event.target === event.currentTarget) {
                onClose();
            }
        }}
    >
        <div className='DocSearch-Modal' ref={modalRef}>
            {mode === 'search' ? <SearchBox onClose={onClose} /> : <AskBox onClose={onClose} />}
            <Footer />
        </div>
    </Container>;
}

const Container = styled.div`
  .DocSearch-Commands-Key {
    padding-left: 1px;
    font-size: .875rem;
    background-color: rgba(0, 0, 0, 0.1);
    background-image: none;
    box-shadow: none;
  }

  .DocSearch-Title {
    &:not(:last-child) {
      margin-bottom: 16px;
    }
  }

  .DocSearch-Hits {
    ul {
      box-shadow: var(--docsearch-hit-shadow);
      border-radius: 4px;
      margin-bottom: 4px;
      margin-top: 3px;
    }
  }

  .DocSearch-Hit {
    padding-bottom: 0;
    border-radius: 0;

    a {
      border-radius: 0;
      border-top: 1px solid rgb(212, 217, 225, 0.4);
      box-shadow: none;
    }

    &:first-child {
      a {
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        border-top-width: 0;
      }
    }

    &:last-child {
      a {
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }
  }

  .DocSearch-Hit-content-wrapper {
    line-height: 1.3;
  }

  .DocSearch-Hit-title {
    font-size: 1em;
  }

  .DocSearch-Help {
    font-size: 1em;
  }

  .DocSearch-Label {
    line-height: 1;
  }

`;
