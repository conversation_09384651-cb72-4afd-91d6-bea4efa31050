import { useKeyboardEvents } from './hooks/use-keyboard-events';
import { ElementType, useCallback, useEffect, useRef, useState } from 'react';
import { DocSearchButton } from './doc-search-button';
import { createPortal } from 'react-dom';
import { DocSearchModal } from './doc-search-modal';
import { createGlobalStyle, useActions } from '@topwrite/common';
import '@docsearch/css';
import ModeProvider from './mode-provider';
import { usePart } from '../../lib/use-article';

export interface DocSearchProps {
    as?: ElementType;
    className?: string;
    onlyCurrentPart?: boolean;
}

export default function DocSearch({ as, className, onlyCurrentPart, ...props }: DocSearchProps) {
    const searchButtonRef = useRef<HTMLButtonElement>(null);
    const [isOpen, setIsOpen] = useState(false);
    const { setPart: setSearchPart } = useActions('search');
    const { setPart: setAskPart } = useActions('ask');
    const part = usePart();

    const onOpen = useCallback(() => {
        setIsOpen(true);
    }, [setIsOpen]);

    const onClose = useCallback(() => {
        setIsOpen(false);
    }, [setIsOpen]);

    const onInput = useCallback(() => {
        setIsOpen(true);
    }, [setIsOpen]);

    useKeyboardEvents({
        isOpen,
        onOpen,
        onClose,
        onInput,
        searchButtonRef,
    });

    useEffect(() => {
        setSearchPart(onlyCurrentPart ? part?.level.toString() : undefined);
        setAskPart(onlyCurrentPart ? part?.level.toString() : undefined);
    }, [onlyCurrentPart, part]);

    const Component = as || DocSearchButton;

    return <ModeProvider>
        <GlobalStyle />
        <Component onClick={onOpen} className={className} ref={searchButtonRef} />
        {isOpen && createPortal(
            <DocSearchModal
                {...props}
                onClose={onClose}
            />,
            document.body
        )}
    </ModeProvider>;
}

const GlobalStyle = createGlobalStyle`
  :root:root {
    --docsearch-primary-color: var(--ttw-primary-color, #3c60ff);
    --docsearch-hit-height: 48px;
    --docsearch-searchbox-height: 48px;
  }
`;
