import { ComponentProps, forwardRef, useEffect, useState } from 'react';
import { ControlKeyIcon, SearchIcon } from './icons';
import { styled, useIntl } from '@topwrite/common';

export type DocSearchButtonProps = ComponentProps<'button'>

const ACTION_KEY_DEFAULT = 'Ctrl' as const;
const ACTION_KEY_APPLE = '⌘' as const;

function isAppleDevice() {
    return /(Mac|iPhone|iPod|iPad)/i.test(navigator.platform);
}

export const DocSearchButton = forwardRef<HTMLButtonElement, DocSearchButtonProps>(({ className, ...props }, ref) => {
    const [key, setKey] = useState<typeof ACTION_KEY_APPLE | typeof ACTION_KEY_DEFAULT | null>(null);

    useEffect(() => {
        if (typeof navigator !== 'undefined') {
            setKey(isAppleDevice() ? ACTION_KEY_APPLE : ACTION_KEY_DEFAULT);
        }
    }, []);

    const intl = useIntl();

    return <Button
        type='button'
        className='DocSearch DocSearch-Button'
        {...props}
        ref={ref}
    >
        <span className='DocSearch-Button-Container'>
            <SearchIcon />
            <span className='DocSearch-Button-Placeholder'>{intl.$t({ defaultMessage: '搜索' })}</span>
        </span>
        {key !== null && (
            <span className='DocSearch-Button-Keys'>
                <span className='DocSearch-Button-Key'>
                    {key === ACTION_KEY_DEFAULT ? <ControlKeyIcon /> : key}
                </span>
                <span className='DocSearch-Button-Key'>K</span>
            </span>
        )}
    </Button>;
});

const Button = styled.button`
  .DocSearch-Button-Keys {
    min-width: 0;
    padding: .125rem .25rem;
    background: rgba(0, 0, 0, 0.08);
    border-radius: .25rem;
  }

  .DocSearch-Button-Key {
    top: 1px;
    width: auto;
    height: 1.25rem;
    padding-right: .125rem;
    padding-left: .125rem;
    margin-right: 0;
    font-size: .875rem;
    background: none;
    box-shadow: none;
  }
`
