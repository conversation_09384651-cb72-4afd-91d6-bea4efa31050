import { LoadingIcon, ResetIcon, SearchIcon } from '../icons';
import { MAX_QUERY_SIZE } from '../constants';
import React, { useCallback, useMemo, useRef } from 'react';
import { useIntl, useModel } from '@topwrite/common';
import { debounce } from 'lodash';

interface Props {
    onClose(): void;
}

export default function Form({ onClose }: Props) {

    const [{ query, status }, { fetch }] = useModel('search');
    const intl = useIntl();
    const inputRef = useRef<HTMLInputElement | null>(null);
    const initialQuery = useRef(query).current;

    React.useEffect(() => {
        if (inputRef.current) {
            inputRef.current.focus();
        }
    }, []);

    const onReset = useCallback(() => {
        if (inputRef.current) {
            inputRef.current.value = '';
            fetch('');
        }
    }, [fetch]);

    const debounced = useMemo(() => debounce(fetch, 500), [fetch]);

    const handleSearch = useCallback((e) => {
        debounced(e.target.value);
    }, [debounced]);

    return <header className='DocSearch-SearchBar'>
        <form
            className={[
                'DocSearch-Form',
                status === 'loading' && 'DocSearch-Container--Stalled',
            ].filter(Boolean).join(' ')}
            onSubmit={(event) => {
                event.preventDefault();
            }}
        >
            <label className='DocSearch-MagnifierLabel'>
                <SearchIcon />
            </label>
            <div className='DocSearch-LoadingIndicator'>
                <LoadingIcon />
            </div>
            <input
                defaultValue={initialQuery}
                placeholder={intl.$t({ defaultMessage: '搜索文档...' })}
                className='DocSearch-Input'
                enterKeyHint='done'
                ref={inputRef}
                autoFocus
                maxLength={MAX_QUERY_SIZE}
                onChange={handleSearch}
            />
            <button
                title='Clear the query'
                className='DocSearch-Reset'
                hidden={!query}
                onClick={onReset}
            >
                <ResetIcon />
            </button>
        </form>
        <button className='DocSearch-Cancel' onClick={onClose}>
            {intl.$t({ defaultMessage: '取消' })}
        </button>
    </header>;
}
