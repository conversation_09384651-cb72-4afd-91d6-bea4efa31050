import { useMemo } from 'react';
import { SearchResult } from '../../../models/search';
import { useBook, useIntl, useSelector } from '@topwrite/common';
import { ErrorScreen } from './screens/error-screen';
import { StartScreen } from './screens/start-screen';
import { NoResultsScreen } from './screens/no-result-screen';
import { ResultsScreen } from './screens/results-screen';
import { usePart } from '../../../lib/use-article';

export interface StateProps {
    activeIndex?: number;
    onItemClick: (item: SearchResult) => void;
    onActive: (index: number) => void;
}

export default function State(props: StateProps) {

    const { status, part: searchPart, query, results } = useSelector('search');
    const currentPart = usePart();
    const { summary } = useBook();
    const intl = useIntl();

    const collections = useMemo(() => {
        let id = 0;
        let collections;
        if (summary.isSinglePart()) {
            collections = [{
                title: '',
                items: results,
            }];
        } else {
            let parts = summary.getParts();
            if (searchPart) {
                parts = parts.filter(part => part.level.toString() === searchPart);
            }

            collections = parts.map((part) => {
                return {
                    level: part.level,
                    title: searchPart ? '' : (part.title || intl.$t({ defaultMessage: '未命名分组' })),
                    items: results.filter((result) => {
                        const article = summary.getArticle(article => article.path === result.path);
                        if (article) {
                            const articlePart = summary.getArticlePart(article);
                            return articlePart === part;
                        }
                        return false;
                    })
                };
            });

            if (currentPart && !searchPart) {
                collections = collections.sort((a) => {
                    return a.level === currentPart.level ? -1 : 1;
                });
            }
        }
        return collections.filter(collection => collection.items.length > 0).map((collection) => {
            return {
                ...collection,
                items: collection.items.map((item) => {
                    return {
                        ...item,
                        id: id++
                    };
                })
            };
        });
    }, [results]);

    if (status === 'error') {
        return <ErrorScreen />;
    }

    const hasCollections = collections.length > 0;

    if (!query) {
        return <StartScreen hasCollections={hasCollections} />;
    }

    if (!hasCollections) {
        return <NoResultsScreen />;
    }

    return <ResultsScreen {...props} collections={collections} />;
}
