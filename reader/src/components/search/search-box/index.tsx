import Form from './form';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useSelector } from '@topwrite/common';
import State from './state';

interface SearchBoxProps {
    onClose(): void;
}

export default function SearchBox({ onClose }: SearchBoxProps) {
    const { query, results } = useSelector('search');
    const [activeIndex, setActiveIndex] = useState<number | undefined>(undefined);
    const dropdownRef = useRef<HTMLDivElement | null>(null);

    const onActive = useCallback((index) => {
        setActiveIndex(index);
    }, [setActiveIndex]);

    useEffect(() => {
        if (dropdownRef.current) {
            dropdownRef.current.scrollTop = 0;
        }
    }, [query]);

    useEffect(() => {
        const getActiveItem = (activeIndex: number) => {
            return dropdownRef.current?.getElementsByClassName('DocSearch-Hit').item(activeIndex);
        };

        const onKeyDown = (event: KeyboardEvent) => {
            if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
                event.preventDefault();
                setActiveIndex((activeIndex = -1) => {
                    if (event.key === 'ArrowUp') {
                        --activeIndex;
                    } else {
                        ++activeIndex;
                    }
                    if (activeIndex < 0) {
                        activeIndex = results.length - 1;
                    }
                    if (activeIndex >= results.length) {
                        activeIndex = 0;
                    }

                    const activeItem = getActiveItem(activeIndex);
                    if (activeItem) {
                        if ((activeItem as any).scrollIntoViewIfNeeded) {
                            (activeItem as any).scrollIntoViewIfNeeded(false);
                        } else {
                            activeItem.scrollIntoView(false);
                        }
                    }

                    return activeIndex;
                });
            } else if (event.key === 'Enter') {
                event.preventDefault();
                setActiveIndex((activeIndex = -1) => {
                    const activeItem = getActiveItem(activeIndex);
                    if (activeItem) {
                        requestAnimationFrame(() => {
                            activeItem.getElementsByTagName('a').item(0)
                                      ?.dispatchEvent(new Event('click', { bubbles: true }));
                        });
                    }
                    return activeIndex;
                });
            }
        };
        window.addEventListener('keydown', onKeyDown);

        return () => {
            window.removeEventListener('keydown', onKeyDown);
        };
    }, [setActiveIndex, results]);

    return <>
        <Form onClose={onClose} />
        <div className='DocSearch-Dropdown' ref={dropdownRef}>
            <State
                onActive={onActive}
                activeIndex={activeIndex}
                onItemClick={() => {
                    onClose();
                }}
            />
        </div>
    </>;
}
