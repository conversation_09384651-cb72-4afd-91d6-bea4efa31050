import { useIntl, useSelector } from '@topwrite/common';

interface StartScreenProps {
    hasCollections: boolean;
}

export function StartScreen(props: StartScreenProps) {

    const { status } = useSelector('search');
    const intl = useIntl();

    if (status === 'idle' && !props.hasCollections) {
        return (
            <div className='DocSearch-StartScreen'>
                <p className='DocSearch-Help'>{intl.$t({ defaultMessage: '没有最近搜索' })}</p>
            </div>
        );
    }

    return null;
}
