import { ResultItem, Results } from '../results';
import { SearchResult } from '../../../../models/search';

interface ResultsScreenProps {
    activeIndex?: number;
    onItemClick: (item: SearchResult) => void;
    onActive: (index: number) => void;
    collections: { title: string, items: ResultItem[] }[];
}

export function ResultsScreen({ collections, ...props }: ResultsScreenProps) {
    return <div className='DocSearch-Dropdown-Container'>
        {collections.map((collection, index) => {
            return <Results
                key={index}
                title={collection.title}
                items={collection.items}
                {...props}
            />;
        })}
    </div>;
}
