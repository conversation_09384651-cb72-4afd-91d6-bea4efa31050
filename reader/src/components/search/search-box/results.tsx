import { useCallback } from 'react';
import { SearchResult } from '../../../models/search';
import Link from '../../link';
import { ContentIcon, SelectIcon } from '../icons';

export interface ResultItem extends SearchResult {
    id: number;
}

interface ResultsProps {
    items: ResultItem[];
    title: string;
    activeIndex?: number;

    onItemClick(item: ResultItem): void;

    onActive(index: number): void;
}

export function Results({ title, items, ...props }: ResultsProps) {
    return <section className='DocSearch-Hits'>
        <div className='DocSearch-Hit-source'>{title}</div>
        <ul>
            {items.map((item, index) => {
                return <Result
                    key={index}
                    item={item}
                    {...props}
                />;
            })}
        </ul>
    </section>;
}

interface ResultProps {
    activeIndex?: number;

    onItemClick(item: ResultItem): void;

    onActive(index: number): void;

    item: ResultItem;
}

function Result({
    item,
    onItemClick,
    onActive,
    activeIndex
}: ResultProps) {

    const onMouseMove = useCallback(() => {
        onActive(item.id);
    }, [onActive, item]);

    return <li
        className='DocSearch-Hit'
        onClick={() => {
            onItemClick(item);
        }}
        onMouseMove={onMouseMove}
        aria-selected={activeIndex === item.id}
    >
        <Link to={item.path}>
            <div className='DocSearch-Hit-Container'>
                <div className='DocSearch-Hit-icon'>
                    <ContentIcon />
                </div>
                <div className='DocSearch-Hit-content-wrapper'>
                    <span className='DocSearch-Hit-title'>{item.title}</span>
                    {item.body && <span className='DocSearch-Hit-path'>{item.body}</span>}
                </div>
                <div className='DocSearch-Hit-action'>
                    <SelectIcon />
                </div>
            </div>
        </Link>
    </li>;
}
