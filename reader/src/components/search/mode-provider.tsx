import { createContext, Dispatch, ReactNode, SetStateAction, useContext, useState } from 'react';

const Context = createContext<[string, Dispatch<SetStateAction<string>>]>(['search', () => (void 0)]);

export default function({ children }: { children: ReactNode }) {
    const [mode, setMode] = useState('search');

    return <Context.Provider value={[mode, setMode]}>
        {children}
    </Context.Provider>;
}

export const useMode = () => {
    return useContext(Context);
};
