import { Actions, AnyAction, ComponentDescriptorType, States } from '@topwrite/common';
import * as models from './models';
import { ComponentProps } from 'react';
import Markdown from './components/markdown';
import { MenuItemProps, MenuProps } from './components/menu';
import Color from 'color';

type Models = typeof models

declare module '@topwrite/common' {

    export function useSelector<T extends keyof Models>(model: T): States<T, Models>[T];

    export function useActions<T extends keyof Models>(model: T): Actions<T, Models>[T];

    export function useModel<T extends keyof Models>(model: T): [States<T, Models>[T], Actions<T, Models>[T]];

    export interface Model {
        getState<T extends keyof Models>(model: T): Generator<AnyAction, States<T, Models>[T]>;
    }

    export interface OptionsStateType {
        preview: boolean;
        poweredBy: false | {
            name: string
            link: string
        };
    }

    export interface ComponentsMap {
        'page': ComponentDescriptorType;
        'page:menu': ComponentDescriptorType<MenuProps>;
        'page:menu:item': ComponentDescriptorType<MenuItemProps>;
        'page:article:before': ComponentDescriptorType;
        'page:article:after': ComponentDescriptorType;
        'page:article:aside:before': ComponentDescriptorType;
        'page:article:aside:after': ComponentDescriptorType;
        'page:article:markdown': ComponentDescriptorType<ComponentProps<typeof Markdown>>;
        'page:asset': ComponentDescriptorType;
    }

    export interface OptionsStateType {

    }

    export interface ThemeType {
        primaryColor: Color;
    }
}
