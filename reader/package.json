{"name": "@topwrite/reader", "version": "1.0.79", "author": "yunwuxin <<EMAIL>> (https://github.com/yunwuxin)", "license": "ISC", "types": "types/index.d.ts", "main": "dist/index.js", "files": ["dist", "types"], "scripts": {"prebuild": "rimraf dist types", "build": "webpack --progress", "build:dev": "npm run build -- --env dev", "serve": "webpack serve --env dev", "extract": "formatjs extract src/**/*.{ts,tsx} --out-file src/lang/zh-CN.json --ignore src/global.d.ts --id-interpolation-pattern [sha512:contenthash:base64:6] --additional-function-names $t --format simple", "prepack": "npm run build"}, "dependencies": {"@topwrite/common": "^1.0.65", "@types/color": "^3.0.3", "@types/lodash": "^4.14.168", "@types/node": "^16.4.13", "@types/react": "^18", "@types/react-dom": "^18", "color": "^4.2.3", "dayjs": "^1.10.4", "history": "^5.0.0", "immer": "^9.0.6"}, "devDependencies": {"@docsearch/css": "^3.2.1", "@formatjs/cli": "^5.0.2", "@topthink/webpack-config-plugin": "^1.0.16", "crypto-browserify": "^3.12.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "query-string": "^7.1.1", "rc-tooltip": "^5.1.1", "react-loading-bar": "^0.0.7", "rimraf": "^3.0.2", "stream-browserify": "^3.0.0", "typescript": "^5.3.3", "webpack": "^5.36.2", "webpack-cli": "^4.7.0", "webpack-dev-server": "^4.7.3"}}